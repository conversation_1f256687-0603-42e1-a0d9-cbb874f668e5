// Schema Manager for S.T.E.V.I DOS Electron App
export class SchemaManager {
    constructor(dataManager) {
        this.data = dataManager;
        this.schemas = new Map();
        this.initialized = false;
    }

    async initialize() {
        if (this.initialized) return;

        try {
            // Always load fallback schemas first as a safety net
            this.loadFallbackSchemas();

            // Define core tables that should always exist
            const coreTables = [
                'people', 'pets', 'medical_issues', 'disabilities', 'case_management',
                'service_barriers', 'support_contacts', 'incarceration_status', 'bail_conditions',
                'court_dates', 'arrest_history', 'legal_contacts', 'organizations', 'addresses',
                'license_plates', 'incidents', 'bikes', 'bike_activities',
                'people_activities', 'address_activities', 'vehicle_activities',
                'items', 'supply_provisions'
            ];

            // Define optional tables that may not exist in older schemas
            const optionalTables = [
                'encampments'
            ];

            // Load core tables (these should always exist)
            for (const tableName of coreTables) {
                await this.loadTableSchema(tableName);
            }

            // Load optional tables (gracefully handle missing tables)
            for (const tableName of optionalTables) {
                await this.loadTableSchemaOptional(tableName);
            }

            this.initialized = true;
            console.log('Schema manager initialized');
        } catch (error) {
            console.error('Failed to initialize schema manager:', error);
            // Fallback schemas are already loaded
            this.initialized = true;
        }
    }

    async loadTableSchemaOptional(tableName) {
        try {
            await this.loadTableSchema(tableName);
        } catch (error) {
            console.warn(`⚠️ Optional table ${tableName} not available:`, error.message);
            // Use fallback schema for optional tables
            this.loadFallbackSchemaForTable(tableName);
        }
    }

    async loadTableSchema(tableName) {
        try {
            const supabase = this.data.getSupabaseClient();
            if (!supabase) {
                console.warn(`No Supabase client available for schema query: ${tableName}, using fallback`);
                this.loadFallbackSchemaForTable(tableName);
                return;
            }

            // Determine the schema for the table
            const schemaName = this.getSchemaForTable(tableName);

            // Query the information_schema to get column details
            const { data, error } = await supabase
                .from('information_schema.columns')
                .select('column_name, data_type, is_nullable, column_default')
                .eq('table_name', tableName)
                .eq('table_schema', schemaName);

            if (error) {
                console.warn(`Failed to load schema for ${tableName}:`, error);
                console.log(`Using fallback schema for ${tableName}`);
                this.loadFallbackSchemaForTable(tableName);
                return;
            }

            if (!data || data.length === 0) {
                console.warn(`No schema data returned for ${tableName}, using fallback`);
                this.loadFallbackSchemaForTable(tableName);
                return;
            }

            // Convert to our schema format
            const tableSchema = {};
            data.forEach(column => {
                tableSchema[column.column_name] = {
                    type: this.mapPostgresType(column.data_type),
                    nullable: column.is_nullable === 'YES',
                    default: column.column_default
                };
            });

            this.schemas.set(tableName, tableSchema);
            console.log(`Loaded schema for ${tableName}:`, tableSchema);
        } catch (error) {
            console.error(`Error loading schema for ${tableName}:`, error);
        }
    }

    mapPostgresType(pgType) {
        const typeMap = {
            'character varying': 'text',
            'varchar': 'text',
            'text': 'text',
            'integer': 'number',
            'bigint': 'number',
            'smallint': 'number',
            'numeric': 'number',
            'boolean': 'boolean',
            'date': 'date',
            'timestamp with time zone': 'datetime',
            'timestamp without time zone': 'datetime',
            'time without time zone': 'time',
            'time': 'time',
            'uuid': 'text',
            'ARRAY': 'array'
        };

        return typeMap[pgType] || 'text';
    }

    loadFallbackSchemas() {
        // Fallback schemas based on expected structure
        this.schemas.set('people', {
            id: { type: 'number', nullable: false },
            created_at: { type: 'datetime', nullable: false },
            'First Name': { type: 'text', nullable: true },
            'Last Name': { type: 'text', nullable: true },
            date_of_birth: { type: 'date', nullable: true },
            'Active Homelessness': { type: 'boolean', nullable: true },
            'Active Addictions?': { type: 'boolean', nullable: true },
            'Age': { type: 'number', nullable: true },
            first_name: { type: 'text', nullable: true },
            last_name: { type: 'text', nullable: true },
            age: { type: 'number', nullable: true }, // Auto-calculated from date_of_birth
            phone: { type: 'text', nullable: true },
            email: { type: 'text', nullable: true },
            emergency_contact: { type: 'text', nullable: true },
            emergency_contact_phone: { type: 'text', nullable: true },
            currently_homeless: { type: 'boolean', nullable: true },
            housing_status: { type: 'text', nullable: true },
            notes: { type: 'text', nullable: true },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true },
            updated_at: { type: 'datetime', nullable: true },

            // Person-specific single-value fields for homeless/addiction services
            preferred_pronouns: { type: 'text', nullable: true },
            primary_language: { type: 'text', nullable: true },
            has_id_documents: { type: 'boolean', nullable: true },
            veteran_status: { type: 'boolean', nullable: true },
            income_source: { type: 'text', nullable: true },
            risk_level: { type: 'text', nullable: true },
            last_service_date: { type: 'date', nullable: true }
        });

        this.schemas.set('pets', {
            id: { type: 'number', nullable: false },
            person_id: { type: 'number', nullable: false },
            name: { type: 'text', nullable: false },
            species: { type: 'text', nullable: true },
            breed: { type: 'text', nullable: true },
            age: { type: 'integer', nullable: true },
            color: { type: 'text', nullable: true },
            description: { type: 'text', nullable: true },
            microchip_number: { type: 'text', nullable: true },
            vaccination_status: { type: 'text', nullable: true },
            medical_notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: true },
            updated_at: { type: 'datetime', nullable: true },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        this.schemas.set('medical_issues', {
            id: { type: 'number', nullable: false },
            person_id: { type: 'number', nullable: false },
            condition_name: { type: 'text', nullable: false },
            diagnosis_date: { type: 'date', nullable: true },
            severity: { type: 'text', nullable: true },
            status: { type: 'text', nullable: true },
            treatment_notes: { type: 'text', nullable: true },
            medication: { type: 'text', nullable: true },
            follow_up_required: { type: 'boolean', nullable: true },
            follow_up_date: { type: 'date', nullable: true },
            healthcare_provider: { type: 'text', nullable: true },
            notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        this.schemas.set('disabilities', {
            id: { type: 'number', nullable: false },
            person_id: { type: 'number', nullable: false },
            disability_type: { type: 'text', nullable: false },
            description: { type: 'text', nullable: true },
            severity: { type: 'text', nullable: true },
            accommodation_needs: { type: 'text', nullable: true },
            diagnosed_date: { type: 'date', nullable: true },
            healthcare_provider: { type: 'text', nullable: true },
            notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        this.schemas.set('case_management', {
            id: { type: 'number', nullable: false },
            person_id: { type: 'number', nullable: false },
            case_manager_name: { type: 'text', nullable: false },
            case_manager_contact: { type: 'text', nullable: true },
            agency: { type: 'text', nullable: true },
            case_number: { type: 'text', nullable: true },
            start_date: { type: 'date', nullable: true },
            end_date: { type: 'date', nullable: true },
            status: { type: 'text', nullable: true },
            case_type: { type: 'text', nullable: true },
            priority: { type: 'text', nullable: true },
            notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        this.schemas.set('service_barriers', {
            id: { type: 'number', nullable: false },
            person_id: { type: 'number', nullable: false },
            barrier_type: { type: 'text', nullable: false },
            description: { type: 'text', nullable: false },
            severity: { type: 'text', nullable: true },
            status: { type: 'text', nullable: true },
            identified_date: { type: 'date', nullable: true },
            resolved_date: { type: 'date', nullable: true },
            resolution_notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        this.schemas.set('support_contacts', {
            id: { type: 'number', nullable: false },
            person_id: { type: 'number', nullable: false },
            contact_name: { type: 'text', nullable: false },
            relationship: { type: 'text', nullable: true },
            contact_type: { type: 'text', nullable: true },
            phone: { type: 'text', nullable: true },
            email: { type: 'text', nullable: true },
            address: { type: 'text', nullable: true },
            availability: { type: 'text', nullable: true },
            notes: { type: 'text', nullable: true },
            is_emergency_contact: { type: 'boolean', nullable: true },
            is_primary: { type: 'boolean', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        // Criminal Justice Tables
        this.schemas.set('incarceration_status', {
            id: { type: 'number', nullable: false },
            person_id: { type: 'number', nullable: false },
            facility_name: { type: 'text', nullable: true },
            facility_type: { type: 'text', nullable: true },
            status: { type: 'text', nullable: false },
            admission_date: { type: 'date', nullable: true },
            release_date: { type: 'date', nullable: true },
            expected_release_date: { type: 'date', nullable: true },
            charges: { type: 'text', nullable: true },
            sentence_length: { type: 'text', nullable: true },
            notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        this.schemas.set('bail_conditions', {
            id: { type: 'number', nullable: false },
            person_id: { type: 'number', nullable: false },
            condition_type: { type: 'text', nullable: false },
            condition_description: { type: 'text', nullable: false },
            start_date: { type: 'date', nullable: true },
            end_date: { type: 'date', nullable: true },
            amount: { type: 'number', nullable: true },
            surety_name: { type: 'text', nullable: true },
            surety_contact: { type: 'text', nullable: true },
            status: { type: 'text', nullable: true },
            notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        this.schemas.set('court_dates', {
            id: { type: 'number', nullable: false },
            person_id: { type: 'number', nullable: false },
            court_date: { type: 'date', nullable: false },
            court_time: { type: 'time', nullable: true },
            court_name: { type: 'text', nullable: true },
            court_address: { type: 'text', nullable: true },
            case_number: { type: 'text', nullable: true },
            charges: { type: 'text', nullable: true },
            court_type: { type: 'text', nullable: true },
            appearance_type: { type: 'text', nullable: true },
            outcome: { type: 'text', nullable: true },
            next_court_date: { type: 'date', nullable: true },
            lawyer_name: { type: 'text', nullable: true },
            lawyer_contact: { type: 'text', nullable: true },
            status: { type: 'text', nullable: true },
            notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        this.schemas.set('arrest_history', {
            id: { type: 'number', nullable: false },
            person_id: { type: 'number', nullable: false },
            arrest_date: { type: 'date', nullable: false },
            arrest_time: { type: 'time', nullable: true },
            arresting_agency: { type: 'text', nullable: true },
            location: { type: 'text', nullable: true },
            charges: { type: 'text', nullable: false },
            disposition: { type: 'text', nullable: true },
            case_number: { type: 'text', nullable: true },
            booking_number: { type: 'text', nullable: true },
            bail_amount: { type: 'number', nullable: true },
            release_date: { type: 'date', nullable: true },
            release_type: { type: 'text', nullable: true },
            notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        this.schemas.set('legal_contacts', {
            id: { type: 'number', nullable: false },
            person_id: { type: 'number', nullable: false },
            contact_name: { type: 'text', nullable: false },
            contact_type: { type: 'text', nullable: false },
            organization: { type: 'text', nullable: true },
            phone: { type: 'text', nullable: true },
            email: { type: 'text', nullable: true },
            address: { type: 'text', nullable: true },
            case_numbers: { type: 'text', nullable: true },
            relationship_start_date: { type: 'date', nullable: true },
            relationship_end_date: { type: 'date', nullable: true },
            status: { type: 'text', nullable: true },
            notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        this.schemas.set('organizations', {
            id: { type: 'text', nullable: false },
            name: { type: 'text', nullable: false },
            organization_type: { type: 'text', nullable: true },
            description: { type: 'text', nullable: true },
            services_provided: { type: 'text', nullable: true },
            services_tags: { type: 'json', nullable: true },
            address: { type: 'text', nullable: true },
            city: { type: 'text', nullable: true },
            province: { type: 'text', nullable: true },
            postal_code: { type: 'text', nullable: true },
            phone: { type: 'text', nullable: true },
            email: { type: 'text', nullable: true },
            website: { type: 'text', nullable: true },
            contact_person: { type: 'text', nullable: true },
            contact_title: { type: 'text', nullable: true },
            contact_phone: { type: 'text', nullable: true },
            contact_email: { type: 'text', nullable: true },
            operating_hours: { type: 'text', nullable: true },
            availability_notes: { type: 'text', nullable: true },
            partnership_type: { type: 'text', nullable: true },
            referral_process: { type: 'text', nullable: true },
            special_requirements: { type: 'text', nullable: true },
            status: { type: 'text', nullable: true },
            notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        this.schemas.set('addresses', {
            id: { type: 'number', nullable: false },
            street_address: { type: 'text', nullable: false },
            city: { type: 'text', nullable: true },
            province: { type: 'text', nullable: true },
            postal_code: { type: 'text', nullable: true },
            country: { type: 'text', nullable: true },
            notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: true }
        });

        this.schemas.set('license_plates', {
            id: { type: 'text', nullable: false },
            plate_number: { type: 'text', nullable: false },
            province: { type: 'text', nullable: false },
            vehicle_make: { type: 'text', nullable: true },
            vehicle_model: { type: 'text', nullable: true },
            vehicle_year: { type: 'number', nullable: true },
            vehicle_color: { type: 'text', nullable: true },
            owner_name: { type: 'text', nullable: true },
            notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: true }
        });

        this.schemas.set('incidents', {
            id: { type: 'number', nullable: false },
            created_at: { type: 'datetime', nullable: false },
            reporter_id: { type: 'text', nullable: true }, // UUID in database, but treat as text
            location: { type: 'text', nullable: false },
            narrative: { type: 'text', nullable: false },
            tags: { type: 'array', nullable: true },
            is_urgent: { type: 'boolean', nullable: true },
            updated_at: { type: 'datetime', nullable: true },
            incident_number: { type: 'text', nullable: true },
            // Dispatch-specific fields
            incident_date: { type: 'date', nullable: true },
            incident_time: { type: 'time', nullable: true },
            incident_type: { type: 'text', nullable: true },
            description: { type: 'text', nullable: true },
            priority: { type: 'text', nullable: true },
            status: { type: 'text', nullable: true },
            assigned_ranger: { type: 'text', nullable: true },
            coordinates: { type: 'text', nullable: true },
            reported_by: { type: 'text', nullable: true },
            dispatch_notes: { type: 'text', nullable: true },
            // Log entries field for storing activity log
            log_entries: { type: 'jsonb', nullable: true } // JSONB array
        });

        // Activity log schemas
        this.schemas.set('people_activities', {
            id: { type: 'text', nullable: false },
            person_id: { type: 'text', nullable: false },
            activity_type: { type: 'text', nullable: false },
            title: { type: 'text', nullable: false },
            description: { type: 'text', nullable: true },
            location: { type: 'text', nullable: true },
            activity_date: { type: 'date', nullable: false },
            activity_time: { type: 'time', nullable: true },
            staff_member: { type: 'text', nullable: false },
            outcome: { type: 'text', nullable: true },
            follow_up_required: { type: 'boolean', nullable: true },
            follow_up_date: { type: 'date', nullable: true },
            priority: { type: 'text', nullable: true },
            tags: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: false }
        });

        this.schemas.set('address_activities', {
            id: { type: 'text', nullable: false },
            address_id: { type: 'text', nullable: false },
            activity_type: { type: 'text', nullable: false },
            title: { type: 'text', nullable: false },
            description: { type: 'text', nullable: true },
            activity_date: { type: 'date', nullable: false },
            activity_time: { type: 'time', nullable: true },
            staff_member: { type: 'text', nullable: false },
            findings: { type: 'text', nullable: true },
            action_taken: { type: 'text', nullable: true },
            follow_up_required: { type: 'boolean', nullable: true },
            follow_up_date: { type: 'date', nullable: true },
            priority: { type: 'text', nullable: true },
            tags: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: false }
        });

        this.schemas.set('vehicle_activities', {
            id: { type: 'text', nullable: false },
            vehicle_id: { type: 'text', nullable: false },
            activity_type: { type: 'text', nullable: false },
            title: { type: 'text', nullable: false },
            description: { type: 'text', nullable: true },
            location: { type: 'text', nullable: true },
            activity_date: { type: 'date', nullable: false },
            activity_time: { type: 'time', nullable: true },
            staff_member: { type: 'text', nullable: false },
            officer_badge: { type: 'text', nullable: true },
            citation_number: { type: 'text', nullable: true },
            outcome: { type: 'text', nullable: true },
            follow_up_required: { type: 'boolean', nullable: true },
            follow_up_date: { type: 'date', nullable: true },
            priority: { type: 'text', nullable: true },
            tags: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: false }
        });

        // Items table for supply tracking
        this.schemas.set('items', {
            id: { type: 'text', nullable: false },
            name: { type: 'text', nullable: false },
            description: { type: 'text', nullable: true },
            category: { type: 'text', nullable: false },
            unit_type: { type: 'text', nullable: false },
            current_stock: { type: 'integer', nullable: false },
            minimum_threshold: { type: 'integer', nullable: true },
            cost_per_unit: { type: 'decimal', nullable: true },
            supplier: { type: 'text', nullable: true },
            active: { type: 'boolean', nullable: false },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: false }
        });

        // Bikes table for bike registration and theft tracking
        this.schemas.set('bikes', {
            id: { type: 'text', nullable: false }, // UUID in database, but treat as text
            user_id: { type: 'text', nullable: true }, // UUID in database, but treat as text
            owner_name: { type: 'text', nullable: false },
            owner_email: { type: 'text', nullable: false },
            serial_number: { type: 'text', nullable: false },
            make: { type: 'text', nullable: false },
            model: { type: 'text', nullable: false },
            color: { type: 'text', nullable: false },
            value: { type: 'number', nullable: true },
            photo_base64: { type: 'text', nullable: true },
            is_stolen: { type: 'boolean', nullable: false },
            registered_at: { type: 'datetime', nullable: false },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: false },
            theft_date: { type: 'date', nullable: true },
            theft_time: { type: 'time', nullable: true },
            theft_location: { type: 'text', nullable: true },
            suspect_details: { type: 'text', nullable: true },
            theft_notes: { type: 'text', nullable: true },
            reported_stolen_at: { type: 'datetime', nullable: true }
        });

        // Encampments table for encampment location and management tracking
        this.schemas.set('encampments', {
            id: { type: 'text', nullable: false }, // UUID in database, but treat as text
            name: { type: 'text', nullable: false },
            location: { type: 'text', nullable: false },
            coordinates: { type: 'text', nullable: true }, // lat,lng format like incidents
            status: { type: 'text', nullable: false }, // active, inactive, cleared, monitoring
            type: { type: 'text', nullable: true }, // temporary, permanent, seasonal
            estimated_population: { type: 'number', nullable: true },
            description: { type: 'text', nullable: true },
            safety_concerns: { type: 'text', nullable: true },
            services_needed: { type: 'text', nullable: true },
            last_visited: { type: 'datetime', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: true },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        // Media table for file attachments
        this.schemas.set('media', {
            id: { type: 'number', nullable: false },
            record_type: { type: 'text', nullable: false }, // 'incident', 'person', etc.
            record_id: { type: 'number', nullable: false }, // FK to the corresponding table
            filename: { type: 'text', nullable: false },
            stored_at: { type: 'text', nullable: false }, // Azure Blob Storage URL
            description: { type: 'text', nullable: true },
            file_size: { type: 'number', nullable: true },
            content_type: { type: 'text', nullable: true },
            uploaded_at: { type: 'datetime', nullable: false },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: true }
        });

        // Encampments table for encampment location and management tracking
        this.schemas.set('encampments', {
            id: { type: 'text', nullable: false }, // UUID in database, but treat as text
            name: { type: 'text', nullable: false },
            location: { type: 'text', nullable: false },
            coordinates: { type: 'text', nullable: true }, // lat,lng format like incidents
            status: { type: 'text', nullable: false }, // active, inactive, cleared, monitoring
            type: { type: 'text', nullable: true }, // temporary, permanent, seasonal
            estimated_population: { type: 'number', nullable: true },
            description: { type: 'text', nullable: true },
            safety_concerns: { type: 'text', nullable: true },
            services_needed: { type: 'text', nullable: true },
            last_visited: { type: 'datetime', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            updated_at: { type: 'datetime', nullable: true },
            created_by: { type: 'text', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        // Supply provisions table to track what items were provided
        this.schemas.set('supply_provisions', {
            id: { type: 'text', nullable: false },
            activity_id: { type: 'text', nullable: false }, // Links to people_activities
            item_id: { type: 'text', nullable: false },
            quantity_provided: { type: 'integer', nullable: false },
            notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false }
        });

        // Incident links table to track relationships between incidents and other records
        this.schemas.set('incident_links', {
            id: { type: 'text', nullable: false },
            incident_id: { type: 'text', nullable: false },
            linked_record_type: { type: 'text', nullable: false }, // 'person', 'vehicle', 'address', etc.
            linked_record_id: { type: 'text', nullable: false },
            link_type: { type: 'text', nullable: true }, // 'involved', 'witness', 'owner', etc.
            notes: { type: 'text', nullable: true },
            created_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: true }
        });

        // Property records table for stolen/discarded property management
        this.schemas.set('property_records', {
            id: { type: 'text', nullable: false },
            property_number: { type: 'text', nullable: false }, // Auto-generated property ID
            incident_id: { type: 'text', nullable: true }, // Link to incident if applicable
            property_type: { type: 'text', nullable: false }, // 'shopping_cart', 'bicycle', 'electronics', etc.
            category: { type: 'text', nullable: true }, // 'stolen', 'abandoned', 'lost', 'discarded'
            description: { type: 'text', nullable: false },
            brand: { type: 'text', nullable: true },
            model: { type: 'text', nullable: true },
            serial_number: { type: 'text', nullable: true },
            color: { type: 'text', nullable: true },
            estimated_value: { type: 'decimal', nullable: true },
            condition: { type: 'text', nullable: true }, // 'excellent', 'good', 'fair', 'poor', 'damaged'
            found_location: { type: 'text', nullable: false },
            found_coordinates: { type: 'text', nullable: true },
            found_date: { type: 'date', nullable: false },
            found_time: { type: 'time', nullable: false },
            found_by: { type: 'text', nullable: false }, // Ranger who found it
            status: { type: 'text', nullable: false }, // 'found', 'investigating', 'owner_identified', 'returned', 'handed_to_police', 'disposed'
            investigation_notes: { type: 'text', nullable: true },
            owner_name: { type: 'text', nullable: true },
            owner_contact: { type: 'text', nullable: true },
            owner_address: { type: 'text', nullable: true },
            return_location: { type: 'text', nullable: true },
            returned_to: { type: 'text', nullable: true },
            returned_date: { type: 'date', nullable: true },
            returned_time: { type: 'time', nullable: true },
            police_file_number: { type: 'text', nullable: true },
            police_officer: { type: 'text', nullable: true },
            handed_to_police_date: { type: 'date', nullable: true },
            photos: { type: 'text', nullable: true }, // JSON array of photo paths
            created_at: { type: 'datetime', nullable: false },
            created_by: { type: 'text', nullable: false },
            updated_at: { type: 'datetime', nullable: true },
            updated_by: { type: 'text', nullable: true }
        });

        // Property actions table for audit trail
        this.schemas.set('property_actions', {
            id: { type: 'text', nullable: false },
            property_id: { type: 'text', nullable: false },
            action_type: { type: 'text', nullable: false }, // 'found', 'status_changed', 'investigation_updated', 'returned', 'handed_to_police'
            action_description: { type: 'text', nullable: false },
            old_status: { type: 'text', nullable: true },
            new_status: { type: 'text', nullable: true },
            notes: { type: 'text', nullable: true },
            performed_by: { type: 'text', nullable: false },
            performed_at: { type: 'datetime', nullable: false }
        });

        console.log('Loaded fallback schemas');
    }

    getSchemaForTable(tableName) {
        // Map table names to their appropriate schemas
        const coreSchema = [
            'people', 'pets', 'medical_issues', 'disabilities', 'case_management',
            'service_barriers', 'support_contacts', 'incarceration_status', 'bail_conditions',
            'court_dates', 'arrest_history', 'legal_contacts', 'people_activities',
            'ranger_activity_log', 'ranger_activity_people', 'items',
            'supply_provisions', 'addresses', 'address_activities',
            'organizations', 'media', 'vehicle_activities', 'bikes', 'bike_activities',
            'encampments'
        ];

        const auditSchema = [
            'activity_logs', 'found_bike_reports', 'recovery_logs'
        ];

        const caseMgmtSchema = [
            'incidents', 'incident_links', 'property_records',
            'property_actions', 'license_plates'
        ];

        if (coreSchema.includes(tableName)) {
            return 'core';
        } else if (auditSchema.includes(tableName)) {
            return 'audit';
        } else if (caseMgmtSchema.includes(tableName)) {
            return 'case_mgmt';
        } else {
            // Default to public for AI tables and other miscellaneous tables
            return 'public';
        }
    }

    loadFallbackSchemaForTable(tableName) {
        // Load fallback schema for a specific table
        this.loadFallbackSchemas(); // This loads all fallback schemas
        console.log(`Loaded fallback schema for ${tableName}`);
    }

    getTableSchema(tableName) {
        return this.schemas.get(tableName) || {};
    }

    generateFormFields(tableName, excludeFields = ['id', 'created_at', 'updated_at', 'created_by', 'updated_by', 'age', 'Age']) {
        const schema = this.getTableSchema(tableName);
        console.log(`Generating form fields for ${tableName}:`, schema);
        const fields = [];

        // Handle duplicate columns by preferring underscore format
        const processedFields = new Set();
        const duplicateMapping = {
            'First Name': 'first_name',
            'Last Name': 'last_name',
            'Active Homelessness': 'currently_homeless',
            'Age': 'age'
        };

        Object.entries(schema).forEach(([fieldName, fieldInfo]) => {
            if (excludeFields.includes(fieldName)) return;

            // Skip duplicate columns - prefer underscore format
            const preferredField = duplicateMapping[fieldName];
            if (preferredField && schema[preferredField]) {
                return; // Skip this field, use the underscore version instead
            }

            // Skip if we've already processed this field concept
            const fieldConcept = duplicateMapping[fieldName] || fieldName;
            if (processedFields.has(fieldConcept)) return;
            processedFields.add(fieldConcept);

            const field = {
                name: fieldName,
                label: this.formatFieldLabel(fieldName),
                required: !fieldInfo.nullable,
                type: this.mapToFormType(fieldInfo.type)
            };

            // Add specific options for certain field types
            if (fieldInfo.type === 'boolean') {
                field.type = 'select';
                field.options = ['Yes', 'No'];
            }

            // Add specific options for known enum fields
            if (fieldName === 'housing_status') {
                field.type = 'select';
                field.options = [
                    'Housed',
                    'Temporarily Housed',
                    'Unsheltered',
                    'Emergency Shelter',
                    'Transitional Housing',
                    'Unknown'
                ];
            }

            // Risk level options
            if (fieldName === 'risk_level') {
                field.type = 'select';
                field.options = ['Low', 'Medium', 'High'];
            }

            // Income source options
            if (fieldName === 'income_source') {
                field.type = 'select';
                field.options = [
                    'Employment',
                    'Social Assistance',
                    'Disability Benefits',
                    'Pension',
                    'Employment Insurance',
                    'None',
                    'Other'
                ];
            }

            // Primary language options
            if (fieldName === 'primary_language') {
                field.type = 'select';
                field.options = [
                    'English',
                    'French',
                    'Spanish',
                    'Indigenous Language',
                    'Other'
                ];
            }

            // Disability severity options
            if (fieldName === 'severity' && tableName === 'disabilities') {
                field.type = 'select';
                field.options = ['Mild', 'Moderate', 'Severe'];
            }

            // Case management status options
            if (fieldName === 'status' && tableName === 'case_management') {
                field.type = 'select';
                field.options = ['Active', 'Inactive', 'Transferred', 'Closed'];
            }

            // Case management priority options
            if (fieldName === 'priority' && tableName === 'case_management') {
                field.type = 'select';
                field.options = ['Low', 'Medium', 'High', 'Urgent'];
            }

            // Service barrier severity options
            if (fieldName === 'severity' && tableName === 'service_barriers') {
                field.type = 'select';
                field.options = ['Low', 'Medium', 'High'];
            }

            // Service barrier status options
            if (fieldName === 'status' && tableName === 'service_barriers') {
                field.type = 'select';
                field.options = ['Active', 'Resolved', 'In Progress'];
            }

            // Support contact type options
            if (fieldName === 'contact_type' && tableName === 'support_contacts') {
                field.type = 'select';
                field.options = ['Family', 'Friend', 'Professional', 'Peer', 'Other'];
            }

            // Criminal Justice field options
            if (fieldName === 'facility_type' && tableName === 'incarceration_status') {
                field.type = 'select';
                field.options = ['Jail', 'Prison', 'Detention Center', 'Remand', 'Other'];
            }

            if (fieldName === 'status' && tableName === 'incarceration_status') {
                field.type = 'select';
                field.options = ['Currently Incarcerated', 'Released', 'Transferred', 'Unknown'];
            }

            if (fieldName === 'condition_type' && tableName === 'bail_conditions') {
                field.type = 'select';
                field.options = ['Bail', 'Recognizance', 'Probation', 'Parole', 'Other'];
            }

            if (fieldName === 'status' && tableName === 'bail_conditions') {
                field.type = 'select';
                field.options = ['Active', 'Completed', 'Breached', 'Cancelled'];
            }

            if (fieldName === 'court_type' && tableName === 'court_dates') {
                field.type = 'select';
                field.options = ['Criminal', 'Family', 'Civil', 'Traffic', 'Other'];
            }

            if (fieldName === 'appearance_type' && tableName === 'court_dates') {
                field.type = 'select';
                field.options = ['First Appearance', 'Arraignment', 'Preliminary Hearing', 'Trial', 'Sentencing', 'Review', 'Other'];
            }

            if (fieldName === 'status' && tableName === 'court_dates') {
                field.type = 'select';
                field.options = ['Scheduled', 'Completed', 'Missed', 'Cancelled', 'Rescheduled'];
            }

            if (fieldName === 'release_type' && tableName === 'arrest_history') {
                field.type = 'select';
                field.options = ['Bail', 'Own Recognizance', 'Charges Dropped', 'Served Time', 'Other'];
            }

            if (fieldName === 'contact_type' && tableName === 'legal_contacts') {
                field.type = 'select';
                field.options = ['Lawyer', 'Public Defender', 'Probation Officer', 'Parole Officer', 'Legal Aid', 'Other'];
            }

            if (fieldName === 'status' && tableName === 'legal_contacts') {
                field.type = 'select';
                field.options = ['Active', 'Inactive', 'Terminated'];
            }

            // Activity type options for people activities
            if (fieldName === 'activity_type' && tableName === 'people_activities') {
                field.type = 'select';
                field.options = [
                    'visit',
                    'contact',
                    'note',
                    'welfare_check',
                    'service_referral',
                    'supply_provision',
                    'incident',
                    'follow_up',
                    'other'
                ];
            }

            // Activity type options for address activities
            if (fieldName === 'activity_type' && tableName === 'address_activities') {
                field.type = 'select';
                field.options = [
                    'site_visit',
                    'inspection',
                    'complaint',
                    'maintenance',
                    'security_check',
                    'community_event',
                    'note',
                    'other'
                ];
            }

            // Activity type options for vehicle activities
            if (fieldName === 'activity_type' && tableName === 'vehicle_activities') {
                field.type = 'select';
                field.options = [
                    'sighting',
                    'traffic_stop',
                    'parking_violation',
                    'accident',
                    'theft_report',
                    'recovery',
                    'inspection',
                    'note',
                    'other'
                ];
            }

            // Priority options
            if (fieldName === 'priority') {
                field.type = 'select';
                field.options = ['low', 'medium', 'high', 'urgent'];
            }

            if (fieldName === 'incident_type') {
                field.type = 'select';
                field.options = [
                    'Assault',
                    'Theft',
                    'Vandalism',
                    'Drug Activity',
                    'Mental Health Crisis',
                    'Medical Emergency',
                    'Disturbance',
                    'Other'
                ];
            }

            // Item category options
            if (fieldName === 'category' && tableName === 'items') {
                field.type = 'select';
                field.options = [
                    'harm_reduction',
                    'medical',
                    'hygiene',
                    'food',
                    'clothing',
                    'shelter',
                    'safety',
                    'other'
                ];
            }

            // Item unit type options
            if (fieldName === 'unit_type' && tableName === 'items') {
                field.type = 'select';
                field.options = [
                    'pieces',
                    'boxes',
                    'bottles',
                    'packages',
                    'kits',
                    'pairs',
                    'sets',
                    'units',
                    'other'
                ];
            }

            if (fieldName === 'province') {
                field.type = 'select';
                field.options = [
                    'AB', 'BC', 'MB', 'NB', 'NL', 'NS', 'NT', 'NU', 'ON', 'PE', 'QC', 'SK', 'YT'
                ];
            }

            fields.push(field);
        });

        console.log(`Generated ${fields.length} fields for ${tableName}:`, fields);
        return fields;
    }

    formatFieldLabel(fieldName) {
        // If the field name already contains spaces, use it as-is
        if (fieldName.includes(' ')) {
            return fieldName;
        }

        // Otherwise, convert underscore-separated words to title case
        return fieldName
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }

    mapToFormType(schemaType) {
        const typeMap = {
            'text': 'text',
            'number': 'number',
            'boolean': 'select', // Will be converted to Yes/No
            'date': 'date',
            'datetime': 'datetime-local',
            'time': 'time',
            'array': 'text' // Arrays will be handled as comma-separated text
        };

        return typeMap[schemaType] || 'text';
    }

    // Convert form data to database format
    convertFormToDatabase(tableName, formData) {
        const schema = this.getTableSchema(tableName);
        const dbData = { ...formData };

        Object.entries(dbData).forEach(([key, value]) => {
            const fieldInfo = schema[key];
            if (!fieldInfo) return;

            // Convert Yes/No to boolean
            if (fieldInfo.type === 'boolean') {
                if (value === 'Yes') dbData[key] = true;
                else if (value === 'No') dbData[key] = false;
                else dbData[key] = null;
            }

            // Convert comma-separated text to array
            if (fieldInfo.type === 'array') {
                if (value && typeof value === 'string') {
                    dbData[key] = value.split(',').map(item => item.trim()).filter(item => item);
                } else if (!value) {
                    dbData[key] = null;
                }
            }

            // Convert empty strings to null for nullable fields
            if (fieldInfo.nullable && value === '') {
                dbData[key] = null;
            }
        });

        // Calculate age from date_of_birth for people table
        if (tableName === 'people' && dbData.date_of_birth) {
            const birthDate = new Date(dbData.date_of_birth);
            const today = new Date();
            let age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();

            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }

            dbData.age = age >= 0 ? age : null;
        }

        return dbData;
    }

    // Convert database data to form format
    convertDatabaseToForm(tableName, dbData) {
        const schema = this.getTableSchema(tableName);
        const formData = { ...dbData };

        Object.entries(formData).forEach(([key, value]) => {
            const fieldInfo = schema[key];
            if (!fieldInfo) return;

            // Convert boolean to Yes/No
            if (fieldInfo.type === 'boolean') {
                if (value === true) formData[key] = 'Yes';
                else if (value === false) formData[key] = 'No';
                else formData[key] = '';
            }

            // Convert array to comma-separated text
            if (fieldInfo.type === 'array') {
                if (Array.isArray(value)) {
                    formData[key] = value.join(', ');
                } else if (!value) {
                    formData[key] = '';
                }
            }
        });

        return formData;
    }
}
