// Feature Manager for S.T.E.V.I Retro - Handles feature availability based on schema
export class FeatureManager {
    constructor(dataManager, migrationManager) {
        this.data = dataManager;
        this.migrationManager = migrationManager;
        this.availableFeatures = new Set();
        this.featureRequirements = new Map();
        this.initializeFeatureRequirements();
    }

    initializeFeatureRequirements() {
        // Define what each feature requires
        this.featureRequirements.set('encampments', {
            tables: ['encampments'],
            minVersion: '1.1.0',
            description: 'Encampment management functionality'
        });

        // Future features can be added here
        // this.featureRequirements.set('reports', {
        //     tables: ['reports', 'report_templates'],
        //     minVersion: '1.2.0',
        //     description: 'Advanced reporting system'
        // });
    }

    async checkFeatureAvailability() {
        console.log('🔍 Checking feature availability...');
        
        this.availableFeatures.clear();

        // Check each feature
        for (const [featureName, requirements] of this.featureRequirements) {
            const isAvailable = await this.isFeatureAvailable(featureName, requirements);
            
            if (isAvailable) {
                this.availableFeatures.add(featureName);
                console.log(`✅ Feature '${featureName}' is available`);
            } else {
                console.log(`❌ Feature '${featureName}' is not available`);
            }
        }

        console.log(`📊 Available features: ${Array.from(this.availableFeatures).join(', ')}`);
        return this.availableFeatures;
    }

    async isFeatureAvailable(featureName, requirements) {
        try {
            // Check version requirement
            if (this.migrationManager) {
                const currentVersion = await this.migrationManager.getCurrentSchemaVersion();
                if (!this.isVersionSufficient(currentVersion, requirements.minVersion)) {
                    console.log(`⚠️ Feature '${featureName}' requires version ${requirements.minVersion}, current: ${currentVersion}`);
                    return false;
                }
            }

            // Check table requirements
            if (requirements.tables) {
                for (const tableName of requirements.tables) {
                    const tableExists = await this.checkTableExists(tableName);
                    if (!tableExists) {
                        console.log(`⚠️ Feature '${featureName}' requires table '${tableName}' which doesn't exist`);
                        return false;
                    }
                }
            }

            return true;

        } catch (error) {
            console.error(`Error checking feature '${featureName}':`, error);
            return false;
        }
    }

    async checkTableExists(tableName) {
        try {
            // Use the data manager instead of direct Supabase queries
            // This approach works because it uses SQLite caching and graceful fallbacks
            console.log(`🔍 Checking table '${tableName}' using data manager...`);

            const result = await this.data.getAll(tableName);

            // If we get a result (even empty array), the table exists and is accessible
            if (Array.isArray(result)) {
                console.log(`✅ Table '${tableName}' exists and is accessible (${result.length} records)`);
                return true;
            } else {
                console.log(`⚠️ Table '${tableName}' check returned non-array result:`, result);
                return false;
            }

        } catch (error) {
            // If there's an error, the table likely doesn't exist or isn't accessible
            console.log(`⚠️ Table '${tableName}' is not accessible: ${error.message}`);
            return false;
        }
    }

    getSchemaForTable(tableName) {
        // Map table names to their appropriate schemas
        const coreSchema = [
            'people', 'pets', 'medical_issues', 'people_activities',
            'ranger_activity_log', 'ranger_activity_people', 'items',
            'supply_provisions', 'addresses', 'address_activities',
            'organizations', 'media', 'vehicle_activities', 'bikes', 'bike_activities',
            'encampments'
        ];

        const auditSchema = [
            'activity_logs', 'found_bike_reports', 'recovery_logs'
        ];

        const caseMgmtSchema = [
            'incidents', 'incident_links', 'property_records',
            'property_actions', 'license_plates'
        ];

        if (coreSchema.includes(tableName)) {
            return 'core';
        } else if (auditSchema.includes(tableName)) {
            return 'audit';
        } else if (caseMgmtSchema.includes(tableName)) {
            return 'case_mgmt';
        } else {
            return 'public';
        }
    }

    isVersionSufficient(currentVersion, requiredVersion) {
        // Simple version comparison (assumes semantic versioning)
        const current = this.parseVersion(currentVersion);
        const required = this.parseVersion(requiredVersion);

        if (current.major > required.major) return true;
        if (current.major < required.major) return false;
        
        if (current.minor > required.minor) return true;
        if (current.minor < required.minor) return false;
        
        return current.patch >= required.patch;
    }

    parseVersion(version) {
        const parts = version.split('.').map(Number);
        return {
            major: parts[0] || 0,
            minor: parts[1] || 0,
            patch: parts[2] || 0
        };
    }

    isFeatureEnabled(featureName) {
        return this.availableFeatures.has(featureName);
    }

    getAvailableFeatures() {
        return Array.from(this.availableFeatures);
    }

    getFeatureRequirements(featureName) {
        return this.featureRequirements.get(featureName);
    }

    // UI Helper methods
    shouldShowTab(tabName) {
        switch (tabName) {
            case 'encampments':
                return this.isFeatureEnabled('encampments');
            default:
                return true; // Core tabs are always available
        }
    }

    shouldShowMenuItem(menuItem) {
        // Add logic for menu items that depend on features
        if (menuItem.includes('encampment')) {
            return this.isFeatureEnabled('encampments');
        }
        return true;
    }

    getFeatureStatus() {
        const status = {};
        
        for (const [featureName, requirements] of this.featureRequirements) {
            status[featureName] = {
                available: this.isFeatureEnabled(featureName),
                description: requirements.description,
                minVersion: requirements.minVersion,
                tables: requirements.tables
            };
        }

        return status;
    }

    async validateCurrentSetup() {
        const validation = {
            valid: true,
            issues: [],
            warnings: [],
            features: {}
        };

        try {
            // Check feature availability
            await this.checkFeatureAvailability();

            // Validate each feature
            for (const [featureName, requirements] of this.featureRequirements) {
                const featureValidation = await this.validateFeature(featureName, requirements);
                validation.features[featureName] = featureValidation;

                if (!featureValidation.valid) {
                    validation.issues.push(`Feature '${featureName}': ${featureValidation.error}`);
                }

                if (featureValidation.warnings.length > 0) {
                    validation.warnings.push(...featureValidation.warnings);
                }
            }

            // Check for schema version consistency
            if (this.migrationManager) {
                const schemaValidation = await this.migrationManager.validateSchema();
                if (!schemaValidation.valid) {
                    validation.valid = false;
                    validation.issues.push('Schema validation failed: ' + (schemaValidation.error || 'Unknown error'));
                }

                if (schemaValidation.missingTables && schemaValidation.missingTables.length > 0) {
                    validation.warnings.push(`Missing tables: ${schemaValidation.missingTables.join(', ')}`);
                }
            }

        } catch (error) {
            validation.valid = false;
            validation.issues.push(`Validation error: ${error.message}`);
        }

        return validation;
    }

    async validateFeature(featureName, requirements) {
        const validation = {
            valid: true,
            error: null,
            warnings: []
        };

        try {
            // Check version
            if (this.migrationManager) {
                const currentVersion = await this.migrationManager.getCurrentSchemaVersion();
                if (!this.isVersionSufficient(currentVersion, requirements.minVersion)) {
                    validation.valid = false;
                    validation.error = `Requires version ${requirements.minVersion}, current: ${currentVersion}`;
                    return validation;
                }
            }

            // Check tables
            if (requirements.tables) {
                for (const tableName of requirements.tables) {
                    const exists = await this.checkTableExists(tableName);
                    if (!exists) {
                        validation.valid = false;
                        validation.error = `Missing required table: ${tableName}`;
                        return validation;
                    }
                }
            }

        } catch (error) {
            validation.valid = false;
            validation.error = error.message;
        }

        return validation;
    }

    // Method to gracefully disable features
    disableFeature(featureName) {
        this.availableFeatures.delete(featureName);
        console.log(`🚫 Feature '${featureName}' has been disabled`);
    }

    // Method to enable features (after successful migration)
    enableFeature(featureName) {
        if (this.featureRequirements.has(featureName)) {
            this.availableFeatures.add(featureName);
            console.log(`✅ Feature '${featureName}' has been enabled`);
        }
    }
}
