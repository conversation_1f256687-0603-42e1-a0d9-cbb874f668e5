// S.T.E.V.I Retro - Main Application
import { AuthManager } from './auth.js';
import { UIManager } from './ui.js';
import { DataManager } from './data.js';
import { CommandManager } from './commands.js';
import { ConfigManager } from './config.js';
import { UpdateUIManager } from './updater.js';
import { WeatherService } from './weather.js';
import { OutreachTransactionManager } from './outreach-transactions.js';
import { SecurityHeadersManager } from './security-headers.js';
import { SecurityErrorHandler } from './security-error-handler.js';
import { SecurityMonitor } from './security-monitor.js';
import { PropertyManager } from './property-manager.js';
import { BikeManager } from './bike-manager.js';
import { EncampmentManager } from './encampment-manager.js';
import { MigrationManager } from './migration-manager.js';
import { FeatureManager } from './feature-manager.js';

class SteviRetroApp {
    constructor() {
        this.config = new ConfigManager();
        this.auth = new AuthManager();
        this.ui = new UIManager();
        this.data = new DataManager(this.auth);
        this.schema = this.data.schema; // Use the schema manager from data manager
        this.commands = new CommandManager(this);
        this.updateUI = new UpdateUIManager();
        this.weather = new WeatherService(this.config);
        this.security = new SecurityHeadersManager();
        this.securityErrorHandler = new SecurityErrorHandler();
        this.securityMonitor = new SecurityMonitor();
        this.propertyManager = new PropertyManager(this.data, this.auth);
        this.bikeManager = new BikeManager(this.data, this.auth);
        this.encampmentManager = new EncampmentManager(this.data, this.auth);
        this.migrationManager = null; // Will be initialized after auth
        this.featureManager = null; // Will be initialized after auth
        this.currentUser = null;
        this.currentTab = 'dashboard';
        this.currentScreen = null; // Track current screen for navigation
        this.selectedPerson = null; // Track selected person for detail view
        this.selectedIncident = null; // Track selected incident
        this.selectedProperty = null; // Track selected property
        this.selectedEncampment = null; // Track selected encampment for detail view
        this.uploadedFiles = []; // For incident file uploads

        // Make app instance globally available for commands
        window.app = this;

        this.init();
    }

    async init() {
        try {
            console.log('1. Initializing S.T.E.V.I DOS...');

            // Initialize security systems first
            console.log('1.1. Initializing security systems...');
            this.security.initialize();
            this.securityErrorHandler.initialize();
            this.securityMonitor.startMonitoring();

            // Set up Electron API for IPC communication
            this.setupElectronAPI();

            // Start boot sequence
            console.log('2. Starting boot sequence...');
            await this.ui.showBootSequence();
            console.log('3. Boot sequence completed');

            // Initialize Supabase connection
            console.log('4. Initializing Supabase connection...');
            try {
                await this.auth.initializeSupabase();
                console.log('5. Supabase connection initialized');
            } catch (error) {
                console.error('5. Supabase initialization failed:', error.message);
                // Continue anyway - user will see error when trying to login
            }

            // Initialize data manager
            console.log('6. Initializing data manager...');
            await this.data.initialize();
            console.log('7. Data manager initialized');

            // Initialize schema manager
            console.log('8. Initializing schema manager...');
            await this.schema.initialize();
            console.log('9. Schema manager initialized');

            // Check for existing authentication
            console.log('10. Checking authentication...');
            const isAuthenticated = await this.auth.checkAuthentication();
            console.log('11. Authentication check result:', isAuthenticated);

            if (isAuthenticated) {
                console.log('10. User is authenticated, showing main interface...');
                this.currentUser = this.auth.getCurrentUser();
                await this.showMainInterface();
                console.log('11. Main interface shown');
            } else {
                console.log('10. User not authenticated, showing login...');
                await this.showLogin();
                console.log('11. Login screen shown');
            }
        } catch (error) {
            console.error('Initialization error:', error);
            // Show error screen
            document.body.innerHTML = `
                <div style="color: #ff0000; background: #000000; padding: 20px; font-family: monospace;">
                    <h1>S.T.E.V.I DOS - Initialization Error</h1>
                    <p>Error: ${error.message}</p>
                    <p>Stack: ${error.stack}</p>
                    <p>Please check the console for more details.</p>
                    <button onclick="location.reload()" style="background: #ff0000; color: #000000; padding: 10px; border: none; font-family: monospace;">Retry</button>
                </div>
            `;
        }
    }

    async showLogin() {
        this.ui.showScreen('login');

        // Set up login form handlers
        const emailInput = document.getElementById('login-email');
        const passwordInput = document.getElementById('login-password');
        const submitButton = document.getElementById('login-submit');
        const statusDiv = document.getElementById('login-status');

        // Clear any existing event listeners by cloning and replacing elements
        if (submitButton) {
            const newSubmitButton = submitButton.cloneNode(true);
            submitButton.parentNode.replaceChild(newSubmitButton, submitButton);
        }
        if (emailInput) {
            const newEmailInput = emailInput.cloneNode(true);
            emailInput.parentNode.replaceChild(newEmailInput, emailInput);
        }
        if (passwordInput) {
            const newPasswordInput = passwordInput.cloneNode(true);
            passwordInput.parentNode.replaceChild(newPasswordInput, passwordInput);
        }

        // Get the new elements after replacement
        const cleanEmailInput = document.getElementById('login-email');
        const cleanPasswordInput = document.getElementById('login-password');
        const cleanSubmitButton = document.getElementById('login-submit');

        // Reset form state
        if (cleanSubmitButton) {
            cleanSubmitButton.disabled = false;
            cleanSubmitButton.textContent = 'LOGIN';
        }
        if (statusDiv) {
            statusDiv.textContent = '';
            statusDiv.className = 'login-status';
        }

        // Load saved username if available
        const savedUsername = localStorage.getItem('stevidos_saved_username');
        const saveUsernameCheckbox = document.getElementById('save-username');

        if (savedUsername && cleanEmailInput) {
            cleanEmailInput.value = savedUsername;
            if (saveUsernameCheckbox) saveUsernameCheckbox.checked = true;
            if (cleanPasswordInput) cleanPasswordInput.focus(); // Focus password if username is saved
        } else {
            if (cleanEmailInput) cleanEmailInput.focus(); // Focus email if no saved username
        }

        // Handle form submission
        const handleLogin = async (e) => {
            e.preventDefault();

            const email = cleanEmailInput?.value.trim() || '';
            const password = cleanPasswordInput?.value || '';

            if (!email || !password) {
                this.ui.showStatus('Please enter both email and password', 'error');
                return;
            }

            if (cleanSubmitButton) {
                cleanSubmitButton.disabled = true;
                cleanSubmitButton.textContent = 'AUTHENTICATING...';
            }
            if (statusDiv) {
                statusDiv.textContent = 'Connecting to IHARC systems...';
            }

            try {
                console.log('🔄 Attempting login with email:', email);
                const result = await this.auth.login(email, password);
                console.log('🔄 Login result:', result);

                if (result.success) {
                    // Handle username saving
                    if (saveUsernameCheckbox.checked) {
                        localStorage.setItem('stevidos_saved_username', email);
                    } else {
                        localStorage.removeItem('stevidos_saved_username');
                    }

                    this.currentUser = result.user;
                    if (statusDiv) {
                        statusDiv.textContent = 'Authentication successful. Loading interface...';
                    }

                    // Small delay for user feedback
                    setTimeout(async () => {
                        console.log('🔄 Login successful, about to call showMainInterface...');
                        try {
                            await this.showMainInterface();
                            console.log('🔄 showMainInterface completed successfully');
                        } catch (error) {
                            console.error('❌ Error in showMainInterface:', error);
                        }
                    }, 1000);
                } else {
                    if (statusDiv) {
                        statusDiv.textContent = result.error || 'Authentication failed';
                    }
                    if (cleanSubmitButton) {
                        cleanSubmitButton.disabled = false;
                        cleanSubmitButton.textContent = 'LOGIN';
                    }
                }
            } catch (error) {
                console.error('Login error:', error);
                if (statusDiv) {
                    statusDiv.textContent = 'Connection error. Please try again.';
                }
                if (cleanSubmitButton) {
                    cleanSubmitButton.disabled = false;
                    cleanSubmitButton.textContent = 'LOGIN';
                }
            }
        };

        // Event listeners
        if (cleanSubmitButton) {
            cleanSubmitButton.addEventListener('click', handleLogin);
        }

        // Enter key handling
        const handleKeyPress = (e) => {
            if (e.key === 'Enter') {
                if (e.target === cleanEmailInput && cleanPasswordInput) {
                    cleanPasswordInput.focus();
                } else if (e.target === cleanPasswordInput) {
                    handleLogin(e);
                }
            }
        };

        if (cleanEmailInput) {
            cleanEmailInput.addEventListener('keypress', handleKeyPress);
        }
        if (cleanPasswordInput) {
            cleanPasswordInput.addEventListener('keypress', handleKeyPress);
        }
    }

    async showMainInterface() {
        console.log('🔄 Starting showMainInterface...');
        this.ui.showScreen('main');

        // Update user info
        const userInfoElement = document.getElementById('user-info');
        if (this.currentUser) {
            userInfoElement.textContent = `Logged in as: ${this.currentUser.email}`;
        }

        // Start datetime updates
        this.updateDateTime();
        setInterval(() => this.updateDateTime(), 1000);

        // Set up network status monitoring
        this.setupNetworkMonitoring();

        // Set up tab navigation
        this.setupTabNavigation();

        // Show/hide admin tab based on user role
        this.setupRoleBasedVisibility();

        console.log('🔄 About to initialize migration system...');
        // Initialize migration system and check for schema updates
        await this.initializeMigrationSystem();
        console.log('🔄 Migration system initialization completed');

        // Load initial content
        await this.loadTabContent(this.currentTab);

        // Set up menu item click handlers
        this.setupMenuHandlers();

        // Set up real-time data change listeners
        this.setupDataChangeListeners();

        // Check for updates on startup (non-blocking)
        this.checkForUpdatesOnStartup();
    }

    async initializeMigrationSystem() {
        try {
            console.log('🔄 Initializing migration system...');

            // Initialize migration manager
            const supabaseClient = this.data.getSupabaseClient();
            console.log('🔄 Got Supabase client:', !!supabaseClient);
            if (!supabaseClient) {
                console.warn('⚠️ No Supabase client available, skipping migrations');
                return;
            }

            console.log('🔄 Creating MigrationManager...');
            this.migrationManager = new MigrationManager(this.data, supabaseClient);
            console.log('🔄 Creating FeatureManager...');
            this.featureManager = new FeatureManager(this.data, this.migrationManager);

            console.log('🔄 Checking feature availability...');
            // Check feature availability first
            await this.featureManager.checkFeatureAvailability();

            console.log('🔄 Updating UI for features...');
            // Update UI based on available features
            this.updateUIForFeatures();

            // Check if migrations are needed
            const migrationsNeeded = await this.migrationManager.checkMigrationsNeeded();

            if (migrationsNeeded) {
                console.log('🔄 Database migrations required');

                // Show migration dialog
                const proceed = await this.showMigrationDialog();

                if (proceed) {
                    // Run migrations
                    const result = await this.migrationManager.runMigrations();

                    if (result.success) {
                        this.ui.showDialog(
                            'Database Updated',
                            'Your database has been successfully updated with new features!',
                            'success'
                        );

                        // Refresh schema after migration
                        await this.data.schema.initialize();

                        // Re-check feature availability
                        await this.featureManager.checkFeatureAvailability();
                        this.updateUIForFeatures();
                    } else {
                        this.ui.showDialog(
                            'Migration Failed',
                            `Database migration failed: ${result.error}. Some features may not work correctly.`,
                            'error'
                        );
                    }
                } else {
                    this.ui.showDialog(
                        'Migration Skipped',
                        'Database migration was skipped. Some new features may not be available.',
                        'warning'
                    );
                }
            } else {
                console.log('✅ Database schema is up to date');
            }

        } catch (error) {
            console.error('❌ Error in migration system:', error);
            this.ui.showDialog(
                'Migration Error',
                `Error checking for database updates: ${error.message}`,
                'error'
            );
        }
    }

    async showMigrationDialog() {
        return new Promise((resolve) => {
            const dialogHTML = `
                <div class="migration-dialog">
                    <h3>🔄 Database Update Required</h3>
                    <p>This version of the application includes new features that require database updates.</p>
                    <p><strong>What will happen:</strong></p>
                    <ul>
                        <li>New tables and features will be added to your database</li>
                        <li>Existing data will not be affected</li>
                        <li>The process is automatic and safe</li>
                    </ul>
                    <p>Would you like to proceed with the database update?</p>
                    <div class="dialog-actions">
                        <button id="migration-proceed" class="primary-button">Update Database</button>
                        <button id="migration-skip" class="secondary-button">Skip for Now</button>
                    </div>
                </div>
            `;

            // Create modal
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    ${dialogHTML}
                </div>
            `;

            document.body.appendChild(modal);

            // Handle button clicks
            document.getElementById('migration-proceed').addEventListener('click', () => {
                document.body.removeChild(modal);
                resolve(true);
            });

            document.getElementById('migration-skip').addEventListener('click', () => {
                document.body.removeChild(modal);
                resolve(false);
            });
        });
    }

    updateUIForFeatures() {
        if (!this.featureManager) return;

        // Show/hide tabs based on feature availability
        const encampmentsTab = document.querySelector('[data-tab="encampments"]');
        if (encampmentsTab) {
            if (this.featureManager.isFeatureEnabled('encampments')) {
                encampmentsTab.style.display = '';
                console.log('✅ Encampments tab enabled');
            } else {
                encampmentsTab.style.display = 'none';
                console.log('❌ Encampments tab disabled - feature not available');
            }
        }

        // Update navigation if currently on a disabled feature
        if (this.currentTab === 'encampments' && !this.featureManager.isFeatureEnabled('encampments')) {
            console.log('🔄 Redirecting from disabled encampments tab to dashboard');
            this.loadTabContent('dashboard');
        }

        // Log feature status
        const features = this.featureManager.getAvailableFeatures();
        console.log(`📊 UI updated for features: ${features.join(', ')}`);
    }

    getFeatureUnavailableContent(featureName) {
        const featureInfo = this.featureManager ? this.featureManager.getFeatureRequirements(featureName) : null;

        return `
            <div class="content-section">
                <div class="feature-unavailable">
                    <div class="feature-unavailable-icon">🚫</div>
                    <h2>Feature Not Available</h2>
                    <p>The <strong>${featureName}</strong> feature is not available in your current setup.</p>

                    ${featureInfo ? `
                        <div class="feature-requirements">
                            <h3>Requirements:</h3>
                            <ul>
                                <li>Minimum version: ${featureInfo.minVersion}</li>
                                ${featureInfo.tables ? `<li>Required tables: ${featureInfo.tables.join(', ')}</li>` : ''}
                            </ul>
                        </div>
                    ` : ''}

                    <div class="feature-actions">
                        <button class="primary-button" onclick="app.checkForUpdatesOnStartup()">
                            Check for Updates
                        </button>
                        <button class="secondary-button" onclick="app.loadTabContent('dashboard')">
                            Go to Dashboard
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Test function for validating update process
    async testUpdateProcess() {
        console.log('🧪 Starting update process validation...');

        try {
            if (!this.migrationManager || !this.featureManager) {
                console.error('❌ Migration or feature manager not initialized');
                return false;
            }

            // Test 1: Check current schema version
            const currentVersion = await this.migrationManager.getCurrentSchemaVersion();
            console.log(`📊 Current schema version: ${currentVersion}`);

            // Test 2: Check feature availability
            await this.featureManager.checkFeatureAvailability();
            const features = this.featureManager.getAvailableFeatures();
            console.log(`📊 Available features: ${features.join(', ')}`);

            // Test 3: Validate current setup
            const validation = await this.featureManager.validateCurrentSetup();
            console.log('📊 Setup validation:', validation);

            // Test 4: Check if migrations are needed
            const migrationsNeeded = await this.migrationManager.checkMigrationsNeeded();
            console.log(`📊 Migrations needed: ${migrationsNeeded}`);

            // Test 5: Validate schema consistency
            const schemaValidation = await this.migrationManager.validateSchema();
            console.log('📊 Schema validation:', schemaValidation);

            // Test 6: Test encampments functionality if available
            if (this.featureManager.isFeatureEnabled('encampments')) {
                console.log('✅ Encampments feature is available');

                // Test basic encampment operations
                try {
                    const testEncampment = {
                        name: 'Test Encampment',
                        location: 'Test Location',
                        status: 'active',
                        coordinates: '43.9589, -78.1648'
                    };

                    // Note: This is just a validation test, not actually creating data
                    const validationResult = this.encampmentManager.validateEncampmentData(testEncampment);
                    console.log('📊 Encampment validation test:', validationResult);
                } catch (error) {
                    console.warn('⚠️ Encampment validation test failed:', error);
                }
            } else {
                console.log('❌ Encampments feature is not available');
            }

            // Summary
            const summary = {
                schemaVersion: currentVersion,
                featuresAvailable: features,
                migrationsNeeded,
                validationPassed: validation.valid,
                encampmentsEnabled: this.featureManager.isFeatureEnabled('encampments')
            };

            console.log('📊 Update process validation summary:', summary);

            this.ui.showDialog(
                'Update Process Validation',
                `
                <div class="validation-results">
                    <h3>Validation Results</h3>
                    <p><strong>Schema Version:</strong> ${currentVersion}</p>
                    <p><strong>Available Features:</strong> ${features.join(', ') || 'None'}</p>
                    <p><strong>Migrations Needed:</strong> ${migrationsNeeded ? 'Yes' : 'No'}</p>
                    <p><strong>Validation Status:</strong> ${validation.valid ? '✅ Passed' : '❌ Failed'}</p>
                    <p><strong>Encampments Enabled:</strong> ${this.featureManager.isFeatureEnabled('encampments') ? '✅ Yes' : '❌ No'}</p>

                    ${validation.issues.length > 0 ? `
                        <div class="validation-issues">
                            <h4>Issues:</h4>
                            <ul>
                                ${validation.issues.map(issue => `<li>${issue}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${validation.warnings.length > 0 ? `
                        <div class="validation-warnings">
                            <h4>Warnings:</h4>
                            <ul>
                                ${validation.warnings.map(warning => `<li>${warning}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>
                `,
                'info',
                true
            );

            return validation.valid;

        } catch (error) {
            console.error('❌ Update process validation failed:', error);
            this.ui.showDialog(
                'Validation Error',
                `Update process validation failed: ${error.message}`,
                'error'
            );
            return false;
        }
    }

    setupTabNavigation() {
        const tabs = document.querySelectorAll('.tab');

        tabs.forEach(tab => {
            tab.addEventListener('click', async () => {
                const tabName = tab.dataset.tab;

                if (tabName === 'logout') {
                    await this.logout();
                    return;
                }

                // Update active tab
                tabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');

                this.currentTab = tabName;
                await this.loadTabContent(tabName);
            });
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F10') {
                e.preventDefault();
                // Show menu or help
            } else if (e.key === 'Escape') {
                e.preventDefault();
                // Handle escape
            } else if (e.key === 'F1') {
                e.preventDefault();
                // Show help
            }
        });
    }

    setupRoleBasedVisibility() {
        // Show/hide admin tab based on user role
        const adminTab = document.querySelector('.admin-tab');
        if (adminTab) {
            if (this.auth.isAdmin()) {
                adminTab.style.display = 'block';
            } else {
                adminTab.style.display = 'none';
            }
        }
    }

    async loadTabContent(tabName, screenName = null) {
        const contentArea = document.getElementById('content-area');
        this.ui.setStatus('Loading...');

        try {
            // Remove any existing content classes
            contentArea.classList.remove('people-management-content', 'incidents-content', 'incident-search-content');

            // Handle sub-screens for records tab
            if (tabName === 'records' && screenName) {
                switch (screenName) {
                    case 'people-management':
                        contentArea.innerHTML = await this.loadPeopleManagementContent();
                        contentArea.classList.add('people-management-content');
                        this.currentScreen = 'people-management';
                        break;
                    case 'organizations-management':
                        contentArea.innerHTML = await this.loadOrganizationsManagementContent();
                        contentArea.classList.add('organizations-management-content');
                        this.currentScreen = 'organizations-management';
                        break;
                    case 'bikes-management':
                        contentArea.innerHTML = await this.loadBikesManagementContent();
                        contentArea.classList.add('bikes-management-content');
                        this.currentScreen = 'bikes-management';
                        break;
                    case 'person-detail':
                        if (this.selectedPerson) {
                            contentArea.innerHTML = await this.loadPersonDetailContent(this.selectedPerson.id);
                            contentArea.classList.add('people-management-content');
                            this.currentScreen = 'person-detail';
                        }
                        break;
                    case 'incident-search':
                        contentArea.innerHTML = await this.loadIncidentSearchContent();
                        contentArea.classList.add('incident-search-content');
                        this.currentScreen = 'incident-search';
                        this.setupIncidentSearch();
                        break;
                    default:
                        contentArea.innerHTML = await this.loadRecordsContent();
                        this.currentScreen = null;
                }
            } else if (tabName === 'property' && screenName) {
                // Handle sub-screens for property tab
                switch (screenName) {
                    case 'found-property-list':
                        contentArea.innerHTML = await this.loadFoundPropertyListContent();
                        contentArea.classList.add('property-management-content');
                        this.currentScreen = 'found-property-list';
                        break;
                    case 'missing-property-list':
                        contentArea.innerHTML = await this.loadMissingPropertyListContent();
                        contentArea.classList.add('property-management-content');
                        this.currentScreen = 'missing-property-list';
                        break;
                    case 'log-property-recovery':
                        contentArea.innerHTML = await this.loadLogPropertyRecoveryContent();
                        contentArea.classList.add('property-management-content');
                        this.currentScreen = 'log-property-recovery';
                        this.setupPropertyRecoveryForm();
                        break;
                    case 'create-missing-report':
                        contentArea.innerHTML = await this.loadCreateMissingReportContent();
                        contentArea.classList.add('property-management-content');
                        this.currentScreen = 'create-missing-report';
                        break;
                    case 'create-missing-general':
                        contentArea.innerHTML = await this.loadCreateMissingGeneralContent();
                        contentArea.classList.add('property-management-content');
                        this.currentScreen = 'create-missing-general';
                        this.setupMissingGeneralForm();
                        break;
                    case 'create-missing-bike':
                        contentArea.innerHTML = await this.loadCreateMissingBikeContent();
                        contentArea.classList.add('property-management-content');
                        this.currentScreen = 'create-missing-bike';
                        this.setupMissingBikeForm();
                        break;
                    default:
                        contentArea.innerHTML = await this.loadPropertyContent();
                        break;
                }
            } else if (tabName === 'encampments' && screenName) {
                // Handle sub-screens for encampments tab
                switch (screenName) {
                    case 'add-encampment':
                        contentArea.innerHTML = await this.loadAddEncampmentContent();
                        contentArea.classList.add('encampments-management-content');
                        this.currentScreen = 'add-encampment';
                        this.setupAddEncampmentForm();
                        break;
                    case 'encampment-detail':
                        if (this.selectedEncampment) {
                            contentArea.innerHTML = await this.loadEncampmentDetailContent(this.selectedEncampment.id);
                            contentArea.classList.add('encampments-management-content');
                            this.currentScreen = 'encampment-detail';
                        }
                        break;
                    default:
                        contentArea.innerHTML = await this.loadEncampmentsContent();
                        this.currentScreen = null;
                        this.currentScreen = null;
                }
            } else if (tabName === 'incidents' && screenName) {
                // Handle sub-screens for incidents tab
                switch (screenName) {
                    case 'create-incident':
                        contentArea.innerHTML = await this.loadCreateIncidentContent();
                        contentArea.classList.add('incident-creation-content');
                        this.currentScreen = 'create-incident';
                        // Add small delay to ensure DOM is ready
                        setTimeout(() => {
                            this.setupCreateIncidentForm();
                        }, 100);
                        break;
                    case 'edit-incident':
                        contentArea.innerHTML = await this.loadEditIncidentContent();
                        contentArea.classList.add('incident-edit-content');
                        this.currentScreen = 'edit-incident';
                        // Add small delay to ensure DOM is ready
                        setTimeout(() => {
                            this.setupEditIncidentForm();
                        }, 100);
                        break;
                    default:
                        contentArea.innerHTML = await this.loadIncidentsContent();
                        this.currentScreen = null;
                }
            } else {
                // Handle main tabs
                switch (tabName) {
                    case 'dashboard':
                        contentArea.innerHTML = await this.loadDashboardContent();
                        break;
                    case 'incidents':
                        contentArea.innerHTML = await this.loadIncidentsContent();
                        contentArea.classList.add('incidents-content');
                        break;
                    case 'records':
                        contentArea.innerHTML = await this.loadRecordsContent();
                        this.currentScreen = null;
                        break;
                    case 'property':
                        contentArea.innerHTML = await this.loadPropertyContent();
                        break;
                    case 'encampments':
                        if (this.featureManager && this.featureManager.isFeatureEnabled('encampments')) {
                            contentArea.innerHTML = await this.loadEncampmentsContent();
                        } else {
                            contentArea.innerHTML = this.getFeatureUnavailableContent('encampments');
                        }
                        break;
                    case 'reports':
                        contentArea.innerHTML = await this.loadReportsContent();
                        break;
                    case 'system':
                        contentArea.innerHTML = await this.loadSystemContent();
                        break;
                    case 'admin':
                        if (this.auth.isAdmin()) {
                            contentArea.innerHTML = await this.loadAdminContent();
                        } else {
                            contentArea.innerHTML = '<div class="error">Access denied. Admin privileges required.</div>';
                        }
                        break;
                    default:
                        contentArea.innerHTML = '<div>Content not available</div>';
                }
            }
            
            this.ui.setStatus('Ready');

            // Cleanup previous tab if needed
            this.cleanupPreviousTab();

            // Initialize dashboard if it's the dashboard tab
            if (tabName === 'dashboard') {
                this.initializeDashboard();
            }

            // Initialize unified incidents interface
            if (tabName === 'incidents') {
                this.initializeUnifiedIncidents();
            }

            // Property tab now uses menu-based navigation, no initialization needed

            // Initialize admin if it's the admin tab and user has admin access
            if (tabName === 'admin') {
                console.log('=== ADMIN TAB ACCESS CHECK ===');
                console.log('Current user:', this.auth.currentUser?.email);
                console.log('User app_metadata:', this.auth.currentUser?.app_metadata);
                
                // Debug user permissions first
                await this.auth.debugUserPermissions();
                
                // Check if user has admin access
                const hasAdminPermission = this.auth.hasPermission('admin.access');
                const isAdmin = this.auth.isAdmin();
                
                console.log('hasPermission("admin.access"):', hasAdminPermission);
                console.log('isAdmin():', isAdmin);
                
                if (hasAdminPermission || isAdmin) {
                    console.log('User has admin access, initializing admin...');
                    this.initializeAdmin();
                } else {
                    console.warn('User does not have admin access');
                    console.log('User roles:', this.auth.getUserRoles());
                    console.log('User permissions:', this.auth.getUserPermissions());
                    contentArea.innerHTML = `
                        <div class="error">
                            <h3>Access Denied</h3>
                            <p>Admin privileges required.</p>
                            <p>Current user: ${this.auth.currentUser?.email}</p>
                            <p>User roles: ${this.auth.getUserRoles().join(', ') || 'None'}</p>
                            <p>User permissions: ${this.auth.getUserPermissions().join(', ') || 'None'}</p>
                            <button onclick="app.auth.refreshUserPermissions()" class="primary-button">Refresh Permissions</button>
                        </div>
                    `;
                }
            }
        } catch (error) {
            console.error('Error loading content:', error);
            contentArea.innerHTML = '<div>Error loading content</div>';
            this.ui.setStatus('Error');
        }
    }

    cleanupPreviousTab() {
        // Cleanup old dispatch-specific resources (legacy)
        if (this.dispatchUpdateInterval) {
            clearInterval(this.dispatchUpdateInterval);
            this.dispatchUpdateInterval = null;
        }

        if (this.dispatchKeyHandler) {
            document.removeEventListener('keydown', this.dispatchKeyHandler);
            this.dispatchKeyHandler = null;
        }

        if (this.dispatchDataChangeHandler) {
            window.removeEventListener('dataChange', this.dispatchDataChangeHandler);
            this.dispatchDataChangeHandler = null;
        }

        // Cleanup unified incidents resources
        if (this.incidentsUpdateInterval) {
            clearInterval(this.incidentsUpdateInterval);
            this.incidentsUpdateInterval = null;
        }

        if (this.incidentsDataChangeHandler) {
            window.removeEventListener('dataChange', this.incidentsDataChangeHandler);
            this.incidentsDataChangeHandler = null;
        }
    }

    async loadDashboardContent() {
        return `
            <div class="dashboard-container">
                <!-- Dashboard Content - 2-Column Layout with Sidebar -->
                <div class="dashboard-content">
                    <!-- Left Sidebar - Quick Actions -->
                    <div class="dashboard-sidebar">
                        <div class="sidebar-header">
                            <h3>⚡ Quick Actions</h3>
                        </div>
                        <div class="sidebar-content">
                            <div class="quick-actions">
                                <button class="action-btn" data-action="outreach-transactions">
                                    <span class="action-icon">📦</span>
                                    <span class="action-text">Outreach Transactions</span>
                                </button>
                                <button class="action-btn" data-action="log-property">
                                    <span class="action-icon">🏷️</span>
                                    <span class="action-text">Log Property</span>
                                </button>
                                <button class="action-btn" data-action="property-report">
                                    <span class="action-icon">📊</span>
                                    <span class="action-text">Property Report</span>
                                </button>
                                <button class="action-btn" data-action="report-incident">
                                    <span class="action-icon">📋</span>
                                    <span class="action-text">Report Incident</span>
                                </button>
                                <button class="action-btn" data-action="add-person">
                                    <span class="action-icon">👤</span>
                                    <span class="action-text">Add Person</span>
                                </button>
                                <button class="action-btn" data-action="search-records">
                                    <span class="action-icon">🔍</span>
                                    <span class="action-text">Search Records</span>
                                </button>
                                <button class="action-btn" data-action="emergency-contacts">
                                    <span class="action-icon">🚑</span>
                                    <span class="action-text">Emergency Contacts</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content Area -->
                    <div class="main-content-area">
                        <!-- Active Incidents Section -->
                        <div class="dashboard-section incidents-section">
                            <div class="section-header">
                                <h3>🚨 Active Incidents</h3>
                                <span class="incident-count" id="incident-count">0</span>
                            </div>
                            <div class="section-content" id="incidents-content">
                                <div class="loading">Loading incidents...</div>
                            </div>
                            <div class="section-footer">
                                <button class="go-to-dispatch-btn" onclick="app.goToDispatch()">Go to Dispatch</button>
                            </div>
                        </div>

                        <!-- Bottom Row - Weather and Stats -->
                        <div class="bottom-sections">
                            <!-- Weather Section -->
                            <div class="dashboard-section weather-section">
                                <div class="section-header">
                                    <h3>🌤️ Today's Weather</h3>
                                </div>
                                <div class="section-content" id="weather-content">
                                    <div class="loading">Loading weather data...</div>
                                </div>
                            </div>

                            <!-- Stats Section -->
                            <div class="dashboard-section stats-section">
                                <div class="section-header">
                                    <h3>📊 Today's Stats</h3>
                                </div>
                                <div class="section-content" id="stats-content">
                                    <div class="stat-item">
                                        <span class="stat-label">People Contacted:</span>
                                        <span class="stat-value" id="people-contacted">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Visits Completed:</span>
                                        <span class="stat-value" id="visits-completed">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Referrals Made:</span>
                                        <span class="stat-value" id="referrals-made">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Follow-ups Due:</span>
                                        <span class="stat-value" id="followups-due">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Property Pending:</span>
                                        <span class="stat-value" id="property-pending">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async loadIncidentsContent() {
        return `
            <div class="incidents-container">
                <!-- Header -->
                <div class="incidents-header">
                    <h2>INCIDENT MANAGEMENT</h2>
                    <div class="incidents-status">
                        <span id="incident-count-display">0</span> Active Incidents |
                        Last Updated: <span id="last-update">--:--:--</span>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="incidents-main">
                    <!-- Incident List -->
                    <div class="incidents-list-section">
                        <div class="incidents-list-header">
                            <div class="list-filters">
                                <select id="status-filter">
                                    <option value="">All Status</option>
                                    <option value="open">Open</option>
                                    <option value="assigned">Assigned</option>
                                    <option value="en_route">En Route</option>
                                    <option value="closed">Closed</option>
                                </select>
                                <select id="priority-filter">
                                    <option value="">All Priority</option>
                                    <option value="high">High</option>
                                    <option value="medium">Medium</option>
                                    <option value="low">Low</option>
                                </select>
                                <input type="text" id="search-input" placeholder="Search incidents...">
                            </div>
                        </div>
                        <div class="incidents-list" id="incident-list">
                            <div class="loading">Loading incidents...</div>
                        </div>
                    </div>

                    <!-- Incident Details -->
                    <div class="incident-details-section" id="incident-details">
                        <div class="no-selection">
                            <h3>Select an Incident</h3>
                            <p>Choose an incident from the list to view details and manage it.</p>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="incidents-actions">
                    <button class="action-button primary" data-action="create-incident-screen">
                        New Incident
                    </button>
                    <button class="action-button" id="refresh-incidents">
                        Refresh
                    </button>
                    <button class="action-button" id="search-btn">
                        Search
                    </button>
                    <button class="action-button" id="clear-search">
                        Clear
                    </button>
                </div>
            </div>
        `;
    }

    async initializeUnifiedIncidents() {
        try {
            console.log('Initializing unified incidents interface...');

            // Initialize state
            this.selectedIncident = null;
            this.currentIncidentsView = 'dispatch';
            this.incidentsUpdateInterval = null;

            // Set up view switching
            this.setupIncidentsViewSwitching();

            // Set up event handlers
            this.setupUnifiedIncidentsEventHandlers();

            // Load initial data based on current view
            await this.loadIncidentsData();

            // Start real-time updates
            this.startIncidentsUpdates();

            console.log('Unified incidents interface initialized successfully');

        } catch (error) {
            console.error('Error initializing unified incidents:', error);
        }
    }

    setupIncidentsViewSwitching() {
        const viewButtons = document.querySelectorAll('.terminal-cmd[data-view]');
        const views = document.querySelectorAll('.terminal-view');

        viewButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const viewName = btn.dataset.view;

                // Update button states
                viewButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                // Update view visibility
                views.forEach(v => v.classList.remove('active'));
                const targetView = document.getElementById(`${viewName}-view`);
                if (targetView) {
                    targetView.classList.add('active');
                }

                // Update current view and load data
                this.currentIncidentsView = viewName;
                this.loadIncidentsData();

                // Update terminal status
                this.updateTerminalStatus();
            });
        });

        // Add keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (this.currentTab !== 'incidents') return;

            switch(e.key) {
                case 'F1':
                    e.preventDefault();
                    document.querySelector('[data-view="dispatch"]')?.click();
                    break;
                case 'F2':
                    e.preventDefault();
                    document.querySelector('[data-view="list"]')?.click();
                    break;
                case 'F3':
                    e.preventDefault();
                    document.querySelector('[data-view="search"]')?.click();
                    break;
                case 'F4':
                    e.preventDefault();
                    document.querySelector('[data-action="create-incident-screen"]')?.click();
                    break;
                case 'F5':
                    e.preventDefault();
                    document.getElementById('refresh-incidents')?.click();
                    break;
                case 'Escape':
                    // Clear search or go back to dispatch view
                    if (this.currentIncidentsView === 'search') {
                        const searchInput = document.getElementById('search-input');
                        if (searchInput && searchInput.value) {
                            searchInput.value = '';
                            document.getElementById('clear-search')?.click();
                        } else {
                            document.querySelector('[data-view="dispatch"]')?.click();
                        }
                    }
                    break;
                case 'Enter':
                    // Quick search when in search view
                    if (this.currentIncidentsView === 'search' && e.target.id === 'search-input') {
                        e.preventDefault();
                        document.getElementById('search-btn')?.click();
                    }
                    break;
            }
        });
    }

    setupUnifiedIncidentsEventHandlers() {
        // Refresh button
        const refreshBtn = document.getElementById('refresh-incidents');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadIncidentsData();
            });
        }

        // Search functionality
        const searchBtn = document.getElementById('search-btn');
        const searchInput = document.getElementById('search-input');
        const clearBtn = document.getElementById('clear-search');

        if (searchBtn && searchInput) {
            searchBtn.addEventListener('click', () => {
                this.filterIncidents();
            });

            searchInput.addEventListener('input', () => {
                this.filterIncidents();
            });

            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.filterIncidents();
                }
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                if (searchInput) searchInput.value = '';
                const statusFilter = document.getElementById('status-filter');
                const priorityFilter = document.getElementById('priority-filter');
                if (statusFilter) statusFilter.value = '';
                if (priorityFilter) priorityFilter.value = '';
                this.loadIncidentsData();
            });
        }

        // Filter functionality
        const statusFilter = document.getElementById('status-filter');
        const priorityFilter = document.getElementById('priority-filter');

        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.loadIncidentsData();
            });
        }

        if (priorityFilter) {
            priorityFilter.addEventListener('change', () => {
                this.loadIncidentsData();
            });
        }

        // Listen for data changes to automatically refresh
        const dataChangeHandler = (event) => {
            const { table, operation } = event.detail;
            if (table === 'incidents' && this.currentTab === 'incidents') {
                console.log(`🔄 Incident ${operation} detected, refreshing incidents...`);
                this.loadIncidentsData();
            }
        };

        // Remove existing listener if any
        if (this.incidentsDataChangeHandler) {
            window.removeEventListener('dataChange', this.incidentsDataChangeHandler);
        }

        // Add new listener and store reference for cleanup
        this.incidentsDataChangeHandler = dataChangeHandler;
        window.addEventListener('dataChange', dataChangeHandler);
    }

    async loadIncidentsData() {
        await this.loadDispatchView();
    }

    async loadDispatchView() {
        const incidentList = document.getElementById('incident-list');
        const incidentCountDisplay = document.getElementById('incident-count-display');
        if (!incidentList) return;

        try {
            // Show loading state
            incidentList.innerHTML = '<div class="loading">Loading incidents...</div>';

            // Get all incidents
            const incidents = await this.data.search('incidents', {});

            // Apply filters
            let filteredIncidents = this.applyIncidentFilters(incidents);

            // Update count
            if (incidentCountDisplay) {
                incidentCountDisplay.textContent = filteredIncidents.length;
            }

            if (filteredIncidents.length === 0) {
                incidentList.innerHTML = '<div class="loading">No incidents found</div>';
                return;
            }

            // Sort by priority and time
            filteredIncidents.sort((a, b) => {
                const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
                const aPriority = priorityOrder[a.priority] || 2;
                const bPriority = priorityOrder[b.priority] || 2;
                if (bPriority !== aPriority) return bPriority - aPriority;

                // If same priority, sort by time (newest first)
                return new Date(b.created_at) - new Date(a.created_at);
            });

            // Generate clean incident list
            const incidentsHTML = filteredIncidents.map(incident => `
                <div class="incident-item" data-incident-id="${incident.id}">
                    <div class="incident-header">
                        <span class="incident-number">${incident.incident_number || 'N/A'}</span>
                        <span class="incident-priority ${(incident.priority || 'medium').toLowerCase()}">${(incident.priority || 'Medium').toUpperCase()}</span>
                    </div>
                    <div class="incident-type">${incident.incident_type || 'Unknown'}</div>
                    <div class="incident-location">📍 ${(incident.location || 'No location').substring(0, 50)}</div>
                    <div class="incident-time">🕒 ${this.formatTime(incident.incident_time)} | ${this.formatDate(incident.created_at)}</div>
                </div>
            `).join('');

            incidentList.innerHTML = incidentsHTML;

            // Add click handlers
            incidentList.querySelectorAll('.incident-item').forEach(item => {
                item.addEventListener('click', () => {
                    // Remove previous selection
                    incidentList.querySelectorAll('.incident-item').forEach(i => i.classList.remove('selected'));
                    // Add selection to clicked item
                    item.classList.add('selected');
                    // Load incident details
                    this.selectIncident(item.dataset.incidentId);
                });
            });

            // Update status
            this.updateTerminalStatus();

        } catch (error) {
            console.error('Error loading dispatch view:', error);
            incidentList.innerHTML = '<div class="loading">Error loading incidents</div>';
        }
    }

    async loadListView() {
        const incidentsTable = document.getElementById('incidents-table');
        if (!incidentsTable) return;

        try {
            // Show loading state
            incidentsTable.innerHTML = '<div class="loading">Loading incidents...</div>';

            // Get all incidents
            let incidents = await this.data.search('incidents', {});

            // Apply filters
            const statusFilter = document.getElementById('status-filter')?.value;
            const priorityFilter = document.getElementById('priority-filter')?.value;

            if (statusFilter) {
                incidents = incidents.filter(incident => incident.status === statusFilter);
            }

            if (priorityFilter) {
                incidents = incidents.filter(incident => incident.priority === priorityFilter);
            }

            // Sort by creation date (newest first)
            incidents.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

            if (incidents.length === 0) {
                incidentsTable.innerHTML = '<div class="no-incidents">No incidents found</div>';
                return;
            }

            // Generate table HTML
            const tableHTML = `
                <table class="incidents-data-table">
                    <thead>
                        <tr>
                            <th>Number</th>
                            <th>Type</th>
                            <th>Priority</th>
                            <th>Status</th>
                            <th>Location</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${incidents.map(incident => `
                            <tr class="incident-table-row" data-incident-id="${incident.id}">
                                <td>${incident.incident_number || 'N/A'}</td>
                                <td>${incident.incident_type || 'Unknown'}</td>
                                <td><span class="priority-badge ${(incident.priority || 'medium').toLowerCase()}">${incident.priority || 'Medium'}</span></td>
                                <td><span class="status-badge ${(incident.status || 'open').toLowerCase()}">${incident.status || 'Open'}</span></td>
                                <td>${incident.location || 'N/A'}</td>
                                <td>${this.formatDate(incident.created_at)}</td>
                                <td>
                                    <button class="action-btn view-btn" data-action="view" data-incident-id="${incident.id}">View</button>
                                    <button class="action-btn edit-btn" data-action="edit" data-incident-id="${incident.id}">Edit</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            incidentsTable.innerHTML = tableHTML;

            // Add click handlers for actions
            incidentsTable.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const action = btn.dataset.action;
                    const incidentId = btn.dataset.incidentId;

                    if (action === 'view') {
                        this.selectIncident(incidentId);
                        // Switch to dispatch view to show details
                        document.querySelector('[data-view="dispatch"]').click();
                    } else if (action === 'edit') {
                        this.editIncident(incidentId);
                    }
                });
            });

        } catch (error) {
            console.error('Error loading list view:', error);
            incidentsTable.innerHTML = '<div class="error">Failed to load incidents</div>';
        }
    }

    async performIncidentSearch() {
        const searchInput = document.getElementById('search-input');
        const searchResults = document.getElementById('search-results');

        if (!searchInput || !searchResults) return;

        const query = searchInput.value.trim();
        if (!query) {
            searchResults.innerHTML = `
                <div class="search-placeholder">
                    <div class="search-icon">🔍</div>
                    <p>Enter search terms to find incidents</p>
                </div>
            `;
            return;
        }

        try {
            searchResults.innerHTML = '<div class="loading">Searching...</div>';

            // Get all incidents
            const incidents = await this.data.search('incidents', {});

            // Filter incidents based on search query
            const filteredIncidents = incidents.filter(incident => {
                const searchFields = [
                    incident.incident_number,
                    incident.location,
                    incident.description,
                    incident.narrative,
                    incident.incident_type,
                    incident.reported_by
                ].filter(Boolean).join(' ').toLowerCase();

                return searchFields.includes(query.toLowerCase());
            });

            if (filteredIncidents.length === 0) {
                searchResults.innerHTML = `
                    <div class="no-results">
                        <div class="no-results-icon">❌</div>
                        <h3>No incidents found</h3>
                        <p>No incidents match your search criteria: "${query}"</p>
                    </div>
                `;
                return;
            }

            // Generate search results HTML
            const resultsHTML = `
                <div class="search-results-header">
                    <h4>Found ${filteredIncidents.length} incident(s)</h4>
                </div>
                <div class="search-results-list">
                    ${filteredIncidents.map(incident => `
                        <div class="search-result-item" data-incident-id="${incident.id}">
                            <div class="result-header">
                                <span class="incident-number">${incident.incident_number || 'N/A'}</span>
                                <span class="incident-priority priority-${(incident.priority || 'medium').toLowerCase()}">${incident.priority || 'Medium'}</span>
                            </div>
                            <div class="result-type">${incident.incident_type || 'Unknown'}</div>
                            <div class="result-location">📍 ${incident.location}</div>
                            <div class="result-date">📅 ${this.formatDate(incident.created_at)}</div>
                            <div class="result-description">${(incident.description || incident.narrative || '').substring(0, 100)}${(incident.description || incident.narrative || '').length > 100 ? '...' : ''}</div>
                        </div>
                    `).join('')}
                </div>
            `;

            searchResults.innerHTML = resultsHTML;

            // Add click handlers
            searchResults.querySelectorAll('.search-result-item').forEach(item => {
                item.addEventListener('click', () => {
                    const incidentId = item.dataset.incidentId;
                    // Switch to dispatch view and select the incident
                    document.querySelector('[data-view="dispatch"]').click();
                    setTimeout(() => {
                        this.selectIncident(incidentId);
                    }, 100);
                });
            });

        } catch (error) {
            console.error('Error performing search:', error);
            searchResults.innerHTML = '<div class="error">Search failed</div>';
        }
    }

    startIncidentsUpdates() {
        // Clear existing interval
        if (this.incidentsUpdateInterval) {
            clearInterval(this.incidentsUpdateInterval);
        }

        // Start polling for updates every 30 seconds
        this.incidentsUpdateInterval = setInterval(async () => {
            if (this.currentTab === 'incidents') {
                await this.loadIncidentsData();
            }
        }, 30000);
    }

    formatTime(timeString) {
        if (!timeString) return '--:--';
        try {
            // Handle both time strings and full datetime strings
            const time = timeString.includes('T') ?
                new Date(timeString).toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' }) :
                timeString.substring(0, 5); // Just take HH:MM
            return time;
        } catch (error) {
            return '--:--';
        }
    }

    formatPriorityTerminal(priority) {
        switch(priority?.toLowerCase()) {
            case 'high': return '[H]';
            case 'medium': return '[M]';
            case 'low': return '[L]';
            default: return '[M]';
        }
    }

    applyIncidentFilters(incidents) {
        const searchInput = document.getElementById('search-input');
        const statusFilter = document.getElementById('status-filter');
        const priorityFilter = document.getElementById('priority-filter');

        let filtered = [...incidents];

        // Apply search filter
        if (searchInput && searchInput.value.trim()) {
            const searchTerm = searchInput.value.trim().toLowerCase();
            filtered = filtered.filter(incident => {
                const searchFields = [
                    incident.incident_number,
                    incident.location,
                    incident.description,
                    incident.narrative,
                    incident.incident_type,
                    incident.reported_by
                ].filter(field => field); // Remove null/undefined fields

                return searchFields.some(field =>
                    field.toLowerCase().includes(searchTerm)
                );
            });
        }

        // Apply status filter
        if (statusFilter && statusFilter.value) {
            filtered = filtered.filter(incident =>
                incident.status === statusFilter.value
            );
        }

        // Apply priority filter
        if (priorityFilter && priorityFilter.value) {
            filtered = filtered.filter(incident =>
                incident.priority === priorityFilter.value
            );
        }

        return filtered;
    }

    filterIncidents() {
        this.loadIncidentsData();
    }

    updateTerminalStatus() {
        const lastUpdate = document.getElementById('last-update');
        if (lastUpdate) {
            const now = new Date();
            lastUpdate.textContent = now.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }
    }

    async geocodeIncidentAddress() {
        const streetAddress = document.getElementById('street_address')?.value;
        const city = document.getElementById('city')?.value;
        const province = document.getElementById('province')?.value;
        const postalCode = document.getElementById('postal_code')?.value;
        const locationNotes = document.getElementById('location_notes')?.value;
        const statusElement = document.getElementById('geocode-status');
        const coordinatesField = document.getElementById('coordinates');
        const locationField = document.getElementById('location');

        if (!streetAddress || !city || !province) {
            if (statusElement) {
                statusElement.textContent = 'Please fill in street address, city, and province';
                statusElement.className = 'geocode-status error';
            }
            return;
        }

        try {
            if (statusElement) {
                statusElement.textContent = 'Getting coordinates...';
                statusElement.className = 'geocode-status loading';
            }

            // Build the address string
            let fullAddress = `${streetAddress}, ${city}`;

            // Add province (convert code to full name if needed)
            const provinceNames = {
                'ON': 'Ontario',
                'BC': 'British Columbia',
                'AB': 'Alberta',
                'SK': 'Saskatchewan',
                'MB': 'Manitoba',
                'QC': 'Quebec',
                'NB': 'New Brunswick',
                'NS': 'Nova Scotia',
                'PE': 'Prince Edward Island',
                'NL': 'Newfoundland and Labrador',
                'YT': 'Yukon',
                'NT': 'Northwest Territories',
                'NU': 'Nunavut'
            };

            const provinceName = provinceNames[province] || province;
            fullAddress += `, ${provinceName}, Canada`;

            if (postalCode) {
                fullAddress += ` ${postalCode}`;
            }

            // Get Google API key
            const apiKey = this.config?.get('google.apiKey');
            if (!apiKey) {
                throw new Error('Google API key not available');
            }

            // Call Google Geocoding API
            const response = await fetch(
                `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(fullAddress)}&key=${apiKey}`
            );

            if (!response.ok) {
                throw new Error(`Geocoding request failed: ${response.status}`);
            }

            const data = await response.json();

            if (data.status === 'OK' && data.results.length > 0) {
                const result = data.results[0];
                const location = result.geometry.location;
                const formattedAddress = result.formatted_address;

                // Update coordinates field
                if (coordinatesField) {
                    coordinatesField.value = `${location.lat}, ${location.lng}`;
                }

                // Update location field with formatted address and notes
                if (locationField) {
                    let finalLocation = formattedAddress;
                    if (locationNotes) {
                        finalLocation += ` (${locationNotes})`;
                    }
                    locationField.value = finalLocation;
                }

                if (statusElement) {
                    statusElement.textContent = '✓ Coordinates found';
                    statusElement.className = 'geocode-status success';
                }

            } else {
                throw new Error(data.error_message || `Geocoding failed: ${data.status}`);
            }

        } catch (error) {
            console.error('Geocoding error:', error);
            if (statusElement) {
                statusElement.textContent = `Error: ${error.message}`;
                statusElement.className = 'geocode-status error';
            }
        }
    }

    formatDate(dateString) {
        if (!dateString) return 'N/A';
        try {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        } catch (error) {
            return dateString;
        }
    }

    async selectIncident(incidentId) {
        const detailsPane = document.getElementById('incident-details');
        if (!detailsPane) return;

        try {
            // Get incident details
            const incidents = await this.data.search('incidents', {});
            const incident = incidents.find(i => i.id == incidentId);

            if (!incident) {
                detailsPane.innerHTML = `
                    <div class="no-selection">
                        <h3>Incident Not Found</h3>
                        <p>The selected incident could not be loaded.</p>
                    </div>
                `;
                return;
            }

            // Format incident details with tabs
            const details = `
                <div class="incident-detail-tabs">
                    <button class="incident-detail-tab active" data-tab="overview">Overview</button>
                    <button class="incident-detail-tab" data-tab="map">Map</button>
                    <button class="incident-detail-tab" data-tab="links">Links</button>
                    <button class="incident-detail-tab" data-tab="log">Log</button>
                    <button class="incident-detail-tab" data-tab="status">Status</button>
                </div>
                <div class="incident-detail-content">
                    <div class="tab-content active" id="overview-tab">
                        ${this.generateOverviewTab(incident)}
                    </div>
                    <div class="tab-content" id="map-tab">
                        ${this.generateMapTab(incident)}
                    </div>
                    <div class="tab-content" id="links-tab">
                        ${await this.generateLinksTab(incident)}
                    </div>
                    <div class="tab-content" id="log-tab">
                        ${this.generateLogTab(incident)}
                    </div>
                    <div class="tab-content" id="status-tab">
                        ${this.generateStatusTab(incident)}
                    </div>
                </div>
                <div class="incident-actions">
                    <button class="action-button" onclick="app.editIncident('${incident.id}')">
                        Edit Incident
                    </button>
                    <button class="action-button" onclick="app.addLogEntry('${incident.id}')">
                        📝 Add Note
                    </button>
                    <button class="action-button close-button" onclick="app.closeIncident('${incident.id}')">
                        Close Incident
                    </button>
                    <button class="action-button delete-button" onclick="app.deleteIncident('${incident.id}')">
                        Delete Incident
                    </button>
                </div>
                <div class="dispatch-quick-actions">
                    <button class="quick-action-btn" onclick="app.showEditIncidentDialog()">
                        <span class="shortcut-key">F1</span>Edit
                    </button>
                    <button class="quick-action-btn" onclick="app.showAssignRangerDialog()">
                        <span class="shortcut-key">F2</span>Assign
                    </button>
                    <button class="quick-action-btn" onclick="app.showUpdateStatusDialog()">
                        <span class="shortcut-key">F3</span>Status
                    </button>
                    <button class="quick-action-btn" onclick="app.showAddNoteDialog()">
                        <span class="shortcut-key">F4</span>Note
                    </button>
                    <button class="quick-action-btn" onclick="app.showCloseIncidentDialog()">
                        <span class="shortcut-key">F8</span>Close
                    </button>
                </div>
            `;

            detailsPane.innerHTML = details;

            // Store selected incident for actions
            this.selectedIncident = incident;

            // Set up tab switching
            this.setupDetailTabs();

        } catch (error) {
            console.error('Error loading incident details:', error);
            detailsPane.innerHTML = `
                <div class="no-selection">
                    <h3>Error Loading Incident</h3>
                    <p>Failed to load incident details.</p>
                </div>
            `;
        }
    }

    formatTextForTerminal(text, maxWidth = 49) {
        if (!text) return '│                                                   │<br>';

        const words = text.split(' ');
        const lines = [];
        let currentLine = '';

        words.forEach(word => {
            if ((currentLine + word).length <= maxWidth) {
                currentLine += (currentLine ? ' ' : '') + word;
            } else {
                if (currentLine) {
                    lines.push(currentLine);
                    currentLine = word;
                } else {
                    // Word is too long, truncate it
                    lines.push(word.substring(0, maxWidth));
                    currentLine = '';
                }
            }
        });

        if (currentLine) {
            lines.push(currentLine);
        }

        return lines.map(line => `│ ${line.padEnd(maxWidth)} │`).join('<br>') + '<br>';
    }

    async closeIncident(incidentId) {
        try {
            // Get incident details for confirmation
            const incidents = await this.data.search('incidents', {});
            const incident = incidents.find(i => i.id == incidentId);

            if (!incident) {
                this.ui.showDialog('Error', 'Incident not found.', 'error');
                return;
            }

            // Check if already closed
            if (incident.status === 'closed') {
                this.ui.showDialog('Info', 'This incident is already closed.', 'info');
                return;
            }

            // Show confirmation dialog
            const confirmed = await this.ui.showConfirmDialog(
                'Close Incident',
                `Are you sure you want to close incident "${incident.incident_number || incident.id}"?\n\nType: ${incident.incident_type || 'Unknown'}\nLocation: ${incident.location || 'Unknown'}\n\nThis will mark the incident as resolved and closed.`,
                'Close Incident',
                'Cancel'
            );

            if (!confirmed) {
                return;
            }

            // Show loading state
            this.ui.setStatus('Closing incident...', 'info');

            // Update incident status to closed
            const updateData = {
                status: 'closed',
                closed_at: new Date().toISOString(),
                closed_by: this.auth?.getCurrentUser()?.email || 'Unknown',
                updated_at: new Date().toISOString()
            };

            const success = await this.data.update('incidents', incidentId, updateData);

            if (success) {
                this.ui.setStatus('Incident closed successfully', 'success');

                // Add status history entry if the function exists
                if (this.addStatusHistoryEntry) {
                    await this.addStatusHistoryEntry(incidentId, 'Incident closed');
                }

                // Refresh the incident list
                await this.loadIncidentsData();

                // Refresh the details if this incident is selected
                if (this.selectedIncident && this.selectedIncident.id == incidentId) {
                    await this.selectIncident(incidentId);
                }

                // Emit data change event for any listeners
                if (this.data && this.data.emitDataChange) {
                    this.data.emitDataChange('incidents', 'update', { id: incidentId, status: 'closed' });
                }

            } else {
                this.ui.showDialog('Error', 'Failed to close incident. Please try again.', 'error');
            }

        } catch (error) {
            console.error('Error closing incident:', error);
            this.ui.showDialog('Error', `Failed to close incident: ${error.message}`, 'error');
        }
    }

    async deleteIncident(incidentId) {
        try {
            // Get incident details for confirmation
            const incidents = await this.data.search('incidents', {});
            const incident = incidents.find(i => i.id == incidentId);

            if (!incident) {
                this.ui.showDialog('Error', 'Incident not found.', 'error');
                return;
            }

            // Show confirmation dialog
            const confirmed = await this.ui.showConfirmDialog(
                'Delete Incident',
                `Are you sure you want to delete incident "${incident.incident_number || incident.id}"?\n\nType: ${incident.incident_type || 'Unknown'}\nLocation: ${incident.location || 'Unknown'}\n\nThis action cannot be undone.`,
                'Delete',
                'Cancel'
            );

            if (!confirmed) {
                return;
            }

            // Show loading state
            this.ui.setStatus('Deleting incident...', 'info');

            // First, delete all incident links to prevent orphaned records
            try {
                const links = await this.data.search('incident_links', { incident_id: incidentId });
                for (const link of links) {
                    await this.data.delete('incident_links', link.id);
                }
                console.log(`Deleted ${links.length} incident links for incident ${incidentId}`);
            } catch (linkError) {
                console.warn('Error deleting incident links:', linkError);
                // Continue with incident deletion even if link deletion fails
            }

            // Delete from database and cache
            const success = await this.data.delete('incidents', incidentId);

            if (success) {
                this.ui.setStatus('Incident deleted successfully', 'success');

                // Clear the details pane if this incident was selected
                if (this.selectedIncident && this.selectedIncident.id == incidentId) {
                    const detailsPane = document.getElementById('incident-details');
                    if (detailsPane) {
                        detailsPane.innerHTML = `
                            <div class="no-selection">
                                <h3>Select an Incident</h3>
                                <p>Choose an incident from the list to view details and manage it.</p>
                            </div>
                        `;
                    }
                    this.selectedIncident = null;
                }

                // Refresh the incident list
                await this.loadIncidentsData();

                // Emit data change event for any listeners
                if (this.data && this.data.emitDataChange) {
                    this.data.emitDataChange('incidents', 'delete', { id: incidentId });
                }

            } else {
                this.ui.showDialog('Error', 'Failed to delete incident. Please try again.', 'error');
            }

        } catch (error) {
            console.error('Error deleting incident:', error);
            this.ui.showDialog('Error', `Failed to delete incident: ${error.message}`, 'error');
        }
    }

    async loadIncidentSearchContent() {
        return `
            <div class="incident-search-container">
                <div class="search-header">
                    <h2>INCIDENT SEARCH</h2>
                    <button class="back-button" data-action="back-to-records">← Back to Records</button>
                </div>

                <div class="search-form">
                    <div class="search-filters">
                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="incident-search-text">Search Text:</label>
                                <input type="text" id="incident-search-text" placeholder="Incident number, location, description...">
                            </div>
                            <div class="filter-group">
                                <label for="incident-search-type">Type:</label>
                                <select id="incident-search-type">
                                    <option value="">All Types</option>
                                    <option value="Assault">Assault</option>
                                    <option value="Theft">Theft</option>
                                    <option value="Vandalism">Vandalism</option>
                                    <option value="Drug Activity">Drug Activity</option>
                                    <option value="Mental Health Crisis">Mental Health Crisis</option>
                                    <option value="Medical Emergency">Medical Emergency</option>
                                    <option value="Disturbance">Disturbance</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>

                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="incident-search-status">Status:</label>
                                <select id="incident-search-status">
                                    <option value="">All Status</option>
                                    <option value="open">Open</option>
                                    <option value="assigned">Assigned</option>
                                    <option value="en_route">En Route</option>
                                    <option value="closed">Closed</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="incident-search-priority">Priority:</label>
                                <select id="incident-search-priority">
                                    <option value="">All Priority</option>
                                    <option value="high">High</option>
                                    <option value="medium">Medium</option>
                                    <option value="low">Low</option>
                                </select>
                            </div>
                        </div>

                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="incident-search-date-from">Date From:</label>
                                <input type="date" id="incident-search-date-from">
                            </div>
                            <div class="filter-group">
                                <label for="incident-search-date-to">Date To:</label>
                                <input type="date" id="incident-search-date-to">
                            </div>
                        </div>

                        <div class="search-actions">
                            <button class="action-button primary" id="perform-incident-search">
                                Search Incidents
                            </button>
                            <button class="action-button" id="clear-incident-search">
                                Clear Filters
                            </button>
                        </div>
                    </div>
                </div>

                <div class="search-results-section">
                    <div class="results-header">
                        <h3>Search Results</h3>
                        <span class="results-count" id="incident-results-count">0 incidents found</span>
                    </div>
                    <div class="incident-search-results" id="incident-search-results">
                        <div class="no-results">
                            <p>Enter search criteria and click "Search Incidents" to find historical incidents.</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupIncidentSearch() {
        const searchBtn = document.getElementById('perform-incident-search');
        const clearBtn = document.getElementById('clear-incident-search');
        const searchText = document.getElementById('incident-search-text');

        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.performIncidentSearch();
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearIncidentSearch();
            });
        }

        if (searchText) {
            searchText.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performIncidentSearch();
                }
            });
        }
    }

    async performIncidentSearch() {
        const searchText = document.getElementById('incident-search-text')?.value || '';
        const searchType = document.getElementById('incident-search-type')?.value || '';
        const searchStatus = document.getElementById('incident-search-status')?.value || '';
        const searchPriority = document.getElementById('incident-search-priority')?.value || '';
        const dateFrom = document.getElementById('incident-search-date-from')?.value || '';
        const dateTo = document.getElementById('incident-search-date-to')?.value || '';

        const resultsContainer = document.getElementById('incident-search-results');
        const resultsCount = document.getElementById('incident-results-count');

        if (!resultsContainer) return;

        try {
            // Show loading
            resultsContainer.innerHTML = '<div class="loading">Searching incidents...</div>';

            // Get all incidents
            const allIncidents = await this.data.search('incidents', {});

            // Apply filters
            let filteredIncidents = allIncidents.filter(incident => {
                // Text search
                if (searchText) {
                    const searchFields = [
                        incident.incident_number,
                        incident.location,
                        incident.description,
                        incident.narrative,
                        incident.incident_type,
                        incident.reported_by
                    ].filter(field => field);

                    const matchesText = searchFields.some(field =>
                        field.toLowerCase().includes(searchText.toLowerCase())
                    );
                    if (!matchesText) return false;
                }

                // Type filter
                if (searchType && incident.incident_type !== searchType) return false;

                // Status filter
                if (searchStatus && incident.status !== searchStatus) return false;

                // Priority filter
                if (searchPriority && incident.priority !== searchPriority) return false;

                // Date filters
                if (dateFrom || dateTo) {
                    const incidentDate = new Date(incident.created_at);
                    if (dateFrom && incidentDate < new Date(dateFrom)) return false;
                    if (dateTo && incidentDate > new Date(dateTo + 'T23:59:59')) return false;
                }

                return true;
            });

            // Sort by date (newest first)
            filteredIncidents.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

            // Update results count
            if (resultsCount) {
                resultsCount.textContent = `${filteredIncidents.length} incident${filteredIncidents.length !== 1 ? 's' : ''} found`;
            }

            if (filteredIncidents.length === 0) {
                resultsContainer.innerHTML = '<div class="no-results"><p>No incidents found matching your search criteria.</p></div>';
                return;
            }

            // Generate results HTML
            const resultsHTML = filteredIncidents.map(incident => `
                <div class="search-result-incident" data-incident-id="${incident.id}">
                    <div class="incident-summary">
                        <div class="incident-header">
                            <span class="incident-number">${incident.incident_number || 'N/A'}</span>
                            <span class="incident-date">${this.formatDate(incident.created_at)}</span>
                            <span class="incident-priority ${(incident.priority || 'medium').toLowerCase()}">${(incident.priority || 'Medium').toUpperCase()}</span>
                        </div>
                        <div class="incident-type">${incident.incident_type || 'Unknown'}</div>
                        <div class="incident-location">📍 ${incident.location || 'No location'}</div>
                        <div class="incident-description">${(incident.description || incident.narrative || 'No description').substring(0, 150)}${(incident.description || incident.narrative || '').length > 150 ? '...' : ''}</div>
                        <div class="incident-status">Status: ${incident.status || 'Open'}</div>
                    </div>
                    <div class="incident-actions">
                        <button class="action-button" onclick="app.viewIncidentFromSearch('${incident.id}')">
                            View Details
                        </button>
                    </div>
                </div>
            `).join('');

            resultsContainer.innerHTML = resultsHTML;

        } catch (error) {
            console.error('Error searching incidents:', error);
            resultsContainer.innerHTML = '<div class="error">Error searching incidents. Please try again.</div>';
        }
    }

    clearIncidentSearch() {
        document.getElementById('incident-search-text').value = '';
        document.getElementById('incident-search-type').value = '';
        document.getElementById('incident-search-status').value = '';
        document.getElementById('incident-search-priority').value = '';
        document.getElementById('incident-search-date-from').value = '';
        document.getElementById('incident-search-date-to').value = '';

        const resultsContainer = document.getElementById('incident-search-results');
        const resultsCount = document.getElementById('incident-results-count');

        if (resultsContainer) {
            resultsContainer.innerHTML = '<div class="no-results"><p>Enter search criteria and click "Search Incidents" to find historical incidents.</p></div>';
        }

        if (resultsCount) {
            resultsCount.textContent = '0 incidents found';
        }
    }

    async viewIncidentFromSearch(incidentId) {
        // Navigate to incidents tab and select the incident
        await this.loadTabContent('incidents');
        setTimeout(() => {
            this.selectIncident(incidentId);
        }, 500);
    }

    // Temporary storage for incident creation links
    incidentCreationLinks = {
        people: [],
        vehicles: [],
        addresses: []
    };

    async showPersonLinkDialog() {
        try {
            const people = await this.data.search('people', {});

            if (!people || people.length === 0) {
                // Offer to create a new person
                const createNew = await this.ui.showConfirmDialog(
                    'No People Found',
                    'No people records found. Would you like to create a new person record?',
                    'Create New Person',
                    'Cancel'
                );

                if (createNew) {
                    await this.showCreatePersonDialog();
                }
                return;
            }

            const personOptions = people.map(person => ({
                value: person.id,
                label: `${person.first_name} ${person.last_name} - DOB: ${person.date_of_birth || 'Unknown'}`
            }));

            const fields = [
                {
                    name: 'person_id',
                    type: 'select',
                    label: 'Select Person',
                    options: [
                        { value: 'new', label: '+ Create New Person' },
                        ...personOptions
                    ],
                    required: true
                },
                {
                    name: 'link_type',
                    type: 'select',
                    label: 'Relationship Type',
                    options: [
                        { value: 'involved', label: 'Involved Party' },
                        { value: 'witness', label: 'Witness' },
                        { value: 'complainant', label: 'Complainant' },
                        { value: 'suspect', label: 'Suspect' },
                        { value: 'victim', label: 'Victim' },
                        { value: 'other', label: 'Other' }
                    ],
                    required: true
                },
                {
                    name: 'notes',
                    type: 'textarea',
                    label: 'Notes (Optional)',
                    placeholder: 'Additional details about this person\'s involvement...'
                }
            ];

            this.ui.showForm('Link Person to Incident', fields, async (formData) => {
                if (formData.person_id === 'new') {
                    await this.showCreatePersonDialog();
                    return;
                }

                const person = people.find(p => p.id === formData.person_id);
                if (person) {
                    this.incidentCreationLinks.people.push({
                        record: person,
                        link_type: formData.link_type,
                        notes: formData.notes || null
                    });
                    this.updateIncidentCreationLinksDisplay();
                }
            });

        } catch (error) {
            console.error('Error showing person link dialog:', error);
            this.ui.showDialog('Error', 'Failed to load people records', 'error');
        }
    }

    async showCreatePersonDialog() {
        const fields = [
            {
                name: 'first_name',
                type: 'text',
                label: 'First Name',
                required: true
            },
            {
                name: 'last_name',
                type: 'text',
                label: 'Last Name',
                required: true
            },
            {
                name: 'date_of_birth',
                type: 'date',
                label: 'Date of Birth'
            },
            {
                name: 'phone',
                type: 'tel',
                label: 'Phone Number'
            },
            {
                name: 'email',
                type: 'email',
                label: 'Email Address'
            }
        ];

        this.ui.showForm('Create New Person', fields, async (formData) => {
            try {
                const personData = {
                    ...formData,
                    created_at: new Date().toISOString(),
                    created_by: this.auth.getCurrentUser()?.email || 'System'
                };

                const newPerson = await this.data.insert('people', personData);
                this.ui.showDialog('Success', 'Person created successfully!', 'success');

                // Now show the link dialog again with the new person
                setTimeout(() => this.showPersonLinkDialog(), 500);

            } catch (error) {
                console.error('Error creating person:', error);
                this.ui.showDialog('Error', 'Failed to create person record', 'error');
            }
        });
    }

    updateIncidentCreationLinksDisplay() {
        // Update people display
        const peopleContainer = document.getElementById('linked-people');
        if (peopleContainer) {
            if (this.incidentCreationLinks.people.length === 0) {
                peopleContainer.innerHTML = '<div class="no-links">No people linked yet</div>';
            } else {
                peopleContainer.innerHTML = this.incidentCreationLinks.people.map((link, index) => `
                    <div class="linked-record-item">
                        <div class="linked-record-info">
                            <div class="linked-record-type">${link.link_type.toUpperCase()}</div>
                            <div class="linked-record-details">
                                ${link.record.first_name} ${link.record.last_name}
                                ${link.record.date_of_birth ? ` - DOB: ${link.record.date_of_birth}` : ''}
                            </div>
                            ${link.notes ? `<div class="linked-record-notes">${link.notes}</div>` : ''}
                        </div>
                        <button class="remove-link" onclick="app.removeIncidentCreationLink('people', ${index})">×</button>
                    </div>
                `).join('');
            }
        }

        // Update vehicles display
        const vehiclesContainer = document.getElementById('linked-vehicles');
        if (vehiclesContainer) {
            if (this.incidentCreationLinks.vehicles.length === 0) {
                vehiclesContainer.innerHTML = '<div class="no-links">No vehicles linked yet</div>';
            } else {
                vehiclesContainer.innerHTML = this.incidentCreationLinks.vehicles.map((link, index) => `
                    <div class="linked-record-item">
                        <div class="linked-record-info">
                            <div class="linked-record-type">${link.link_type.toUpperCase()}</div>
                            <div class="linked-record-details">
                                ${link.record.license_plate} - ${link.record.make} ${link.record.model}
                                ${link.record.year ? ` (${link.record.year})` : ''}
                            </div>
                            ${link.notes ? `<div class="linked-record-notes">${link.notes}</div>` : ''}
                        </div>
                        <button class="remove-link" onclick="app.removeIncidentCreationLink('vehicles', ${index})">×</button>
                    </div>
                `).join('');
            }
        }

        // Update addresses display
        const addressesContainer = document.getElementById('linked-addresses');
        if (addressesContainer) {
            if (this.incidentCreationLinks.addresses.length === 0) {
                addressesContainer.innerHTML = '<div class="no-links">No addresses linked yet</div>';
            } else {
                addressesContainer.innerHTML = this.incidentCreationLinks.addresses.map((link, index) => `
                    <div class="linked-record-item">
                        <div class="linked-record-info">
                            <div class="linked-record-type">${link.link_type.toUpperCase()}</div>
                            <div class="linked-record-details">
                                ${link.record.street_address}, ${link.record.city}
                                ${link.record.province ? `, ${link.record.province}` : ''}
                            </div>
                            ${link.notes ? `<div class="linked-record-notes">${link.notes}</div>` : ''}
                        </div>
                        <button class="remove-link" onclick="app.removeIncidentCreationLink('addresses', ${index})">×</button>
                    </div>
                `).join('');
            }
        }
    }

    removeIncidentCreationLink(type, index) {
        this.incidentCreationLinks[type].splice(index, 1);
        this.updateIncidentCreationLinksDisplay();
    }

    async showVehicleLinkDialog() {
        try {
            const vehicles = await this.data.search('license_plates', {});

            if (!vehicles || vehicles.length === 0) {
                const createNew = await this.ui.showConfirmDialog(
                    'No Vehicles Found',
                    'No vehicle records found. Would you like to create a new vehicle record?',
                    'Create New Vehicle',
                    'Cancel'
                );

                if (createNew) {
                    await this.showCreateVehicleDialog();
                }
                return;
            }

            const vehicleOptions = vehicles.map(vehicle => ({
                value: vehicle.id,
                label: `${vehicle.license_plate} - ${vehicle.make} ${vehicle.model} ${vehicle.year || ''}`
            }));

            const fields = [
                {
                    name: 'vehicle_id',
                    type: 'select',
                    label: 'Select Vehicle',
                    options: [
                        { value: 'new', label: '+ Create New Vehicle' },
                        ...vehicleOptions
                    ],
                    required: true
                },
                {
                    name: 'link_type',
                    type: 'select',
                    label: 'Relationship Type',
                    options: [
                        { value: 'involved', label: 'Involved Vehicle' },
                        { value: 'suspect', label: 'Suspect Vehicle' },
                        { value: 'witness', label: 'Witness Vehicle' },
                        { value: 'stolen', label: 'Stolen Vehicle' },
                        { value: 'other', label: 'Other' }
                    ],
                    required: true
                },
                {
                    name: 'notes',
                    type: 'textarea',
                    label: 'Notes (Optional)',
                    placeholder: 'Additional details about this vehicle\'s involvement...'
                }
            ];

            this.ui.showForm('Link Vehicle to Incident', fields, async (formData) => {
                if (formData.vehicle_id === 'new') {
                    await this.showCreateVehicleDialog();
                    return;
                }

                const vehicle = vehicles.find(v => v.id === formData.vehicle_id);
                if (vehicle) {
                    this.incidentCreationLinks.vehicles.push({
                        record: vehicle,
                        link_type: formData.link_type,
                        notes: formData.notes || null
                    });
                    this.updateIncidentCreationLinksDisplay();
                }
            });

        } catch (error) {
            console.error('Error showing vehicle link dialog:', error);
            this.ui.showDialog('Error', 'Failed to load vehicle records', 'error');
        }
    }

    async showCreateVehicleDialog() {
        const fields = [
            {
                name: 'license_plate',
                type: 'text',
                label: 'License Plate',
                required: true
            },
            {
                name: 'make',
                type: 'text',
                label: 'Make',
                required: true
            },
            {
                name: 'model',
                type: 'text',
                label: 'Model',
                required: true
            },
            {
                name: 'year',
                type: 'number',
                label: 'Year'
            },
            {
                name: 'color',
                type: 'text',
                label: 'Color'
            }
        ];

        this.ui.showForm('Create New Vehicle', fields, async (formData) => {
            try {
                const vehicleData = {
                    ...formData,
                    created_at: new Date().toISOString(),
                    created_by: this.auth.getCurrentUser()?.email || 'System'
                };

                const newVehicle = await this.data.insert('license_plates', vehicleData);
                this.ui.showDialog('Success', 'Vehicle created successfully!', 'success');

                setTimeout(() => this.showVehicleLinkDialog(), 500);

            } catch (error) {
                console.error('Error creating vehicle:', error);
                this.ui.showDialog('Error', 'Failed to create vehicle record', 'error');
            }
        });
    }

    async showAddressLinkDialog() {
        try {
            const addresses = await this.data.search('addresses', {});

            if (!addresses || addresses.length === 0) {
                const createNew = await this.ui.showConfirmDialog(
                    'No Addresses Found',
                    'No address records found. Would you like to create a new address record?',
                    'Create New Address',
                    'Cancel'
                );

                if (createNew) {
                    await this.showCreateAddressDialog();
                }
                return;
            }

            const addressOptions = addresses.map(address => ({
                value: address.id,
                label: `${address.street_address}, ${address.city}${address.province ? `, ${address.province}` : ''}`
            }));

            const fields = [
                {
                    name: 'address_id',
                    type: 'select',
                    label: 'Select Address',
                    options: [
                        { value: 'new', label: '+ Create New Address' },
                        ...addressOptions
                    ],
                    required: true
                },
                {
                    name: 'link_type',
                    type: 'select',
                    label: 'Relationship Type',
                    options: [
                        { value: 'incident_location', label: 'Incident Location' },
                        { value: 'related_location', label: 'Related Location' },
                        { value: 'suspect_address', label: 'Suspect Address' },
                        { value: 'witness_address', label: 'Witness Address' },
                        { value: 'other', label: 'Other' }
                    ],
                    required: true
                },
                {
                    name: 'notes',
                    type: 'textarea',
                    label: 'Notes (Optional)',
                    placeholder: 'Additional details about this address...'
                }
            ];

            this.ui.showForm('Link Address to Incident', fields, async (formData) => {
                if (formData.address_id === 'new') {
                    await this.showCreateAddressDialog();
                    return;
                }

                const address = addresses.find(a => a.id === formData.address_id);
                if (address) {
                    this.incidentCreationLinks.addresses.push({
                        record: address,
                        link_type: formData.link_type,
                        notes: formData.notes || null
                    });
                    this.updateIncidentCreationLinksDisplay();
                }
            });

        } catch (error) {
            console.error('Error showing address link dialog:', error);
            this.ui.showDialog('Error', 'Failed to load address records', 'error');
        }
    }

    async showCreateAddressDialog() {
        const fields = [
            {
                name: 'street_address',
                type: 'text',
                label: 'Street Address',
                required: true
            },
            {
                name: 'city',
                type: 'text',
                label: 'City',
                required: true
            },
            {
                name: 'province',
                type: 'text',
                label: 'Province',
                required: true
            },
            {
                name: 'postal_code',
                type: 'text',
                label: 'Postal Code'
            }
        ];

        this.ui.showForm('Create New Address', fields, async (formData) => {
            try {
                const addressData = {
                    ...formData,
                    created_at: new Date().toISOString(),
                    created_by: this.auth.getCurrentUser()?.email || 'System'
                };

                const newAddress = await this.data.insert('addresses', addressData);
                this.ui.showDialog('Success', 'Address created successfully!', 'success');

                setTimeout(() => this.showAddressLinkDialog(), 500);

            } catch (error) {
                console.error('Error creating address:', error);
                this.ui.showDialog('Error', 'Failed to create address record', 'error');
            }
        });
    }

    async editIncident(incidentId) {
        try {
            console.log('Edit incident:', incidentId);

            // Store the incident ID for editing
            this.editingIncidentId = incidentId;

            // Navigate to edit screen
            this.loadTabContent('incidents', 'edit-incident');

        } catch (error) {
            console.error('Error starting incident edit:', error);
            this.ui.showDialog('Error', 'Failed to start editing incident', 'error');
        }
    }

    async removeLinkFromIncident(linkId, incidentId) {
        try {
            const confirmed = await this.ui.showConfirmDialog(
                'Remove Link',
                'Are you sure you want to remove this link from the incident?',
                'Remove',
                'Cancel'
            );

            if (!confirmed) return;

            await this.data.delete('incident_links', linkId);
            this.showToast('Link removed successfully', 'success');

            // Refresh the links display
            this.loadIncidentLinks(incidentId);

        } catch (error) {
            console.error('Error removing link:', error);
            this.showToast('Failed to remove link', 'error');
        }
    }

    async loadEditIncidentContent() {
        console.log('Loading edit incident content...');

        // Get the incident data
        const incident = await this.data.get('incidents', this.editingIncidentId);
        if (!incident) {
            return `
                <div class="error-container">
                    <h2>Incident Not Found</h2>
                    <p>The incident you're trying to edit could not be found.</p>
                    <button class="secondary-button" data-action="back-to-incidents">
                        ← Back to Incidents
                    </button>
                </div>
            `;
        }

        return `
            <div class="incident-form-container">
                <div class="section-header">
                    <h2>EDIT INCIDENT REPORT #${incident.incident_number || incident.id}</h2>
                    <button class="secondary-button" data-action="back-to-incidents">
                        ← Back to Incidents
                    </button>
                </div>

                <div class="incident-form-scroll">
                    <div class="incident-edit-form">
                    <form id="edit-incident-form">
                        <!-- Basic Information Section -->
                        <div class="form-section">
                            <h3>Basic Information</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="incident_type">Incident Type *</label>
                                    <select id="incident_type" name="incident_type" required>
                                        <option value="">Select incident type...</option>
                                        <option value="Assault" ${incident.incident_type === 'Assault' ? 'selected' : ''}>Assault</option>
                                        <option value="Theft" ${incident.incident_type === 'Theft' ? 'selected' : ''}>Theft</option>
                                        <option value="Vandalism" ${incident.incident_type === 'Vandalism' ? 'selected' : ''}>Vandalism</option>
                                        <option value="Drug Activity" ${incident.incident_type === 'Drug Activity' ? 'selected' : ''}>Drug Activity</option>
                                        <option value="Mental Health Crisis" ${incident.incident_type === 'Mental Health Crisis' ? 'selected' : ''}>Mental Health Crisis</option>
                                        <option value="Medical Emergency" ${incident.incident_type === 'Medical Emergency' ? 'selected' : ''}>Medical Emergency</option>
                                        <option value="Disturbance" ${incident.incident_type === 'Disturbance' ? 'selected' : ''}>Disturbance</option>
                                        <option value="Other" ${incident.incident_type === 'Other' ? 'selected' : ''}>Other</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="priority">Priority</label>
                                    <select id="priority" name="priority">
                                        <option value="low" ${incident.priority === 'low' ? 'selected' : ''}>Low</option>
                                        <option value="medium" ${incident.priority === 'medium' || !incident.priority ? 'selected' : ''}>Medium</option>
                                        <option value="high" ${incident.priority === 'high' ? 'selected' : ''}>High</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Location Information -->
                        <div class="form-section">
                            <h3>Location Information</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="location">Location *</label>
                                    <input type="text" id="location" name="location" required
                                           value="${incident.location || ''}"
                                           placeholder="e.g., 123 Main Street, Cobourg">
                                </div>
                                <div class="form-group">
                                    <label for="coordinates">Coordinates</label>
                                    <input type="text" id="coordinates" name="coordinates"
                                           value="${incident.coordinates || ''}"
                                           placeholder="44.0956,-78.1675 (optional)">
                                </div>
                            </div>
                        </div>

                        <!-- Description Section -->
                        <div class="form-section">
                            <h3>Incident Details</h3>
                            <div class="form-row">
                                <div class="form-group full-width">
                                    <label for="description">Description *</label>
                                    <textarea id="description" name="description" required rows="4"
                                              placeholder="Detailed description of the incident">${incident.description || ''}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Status Section -->
                        <div class="form-section">
                            <h3>Status Information</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select id="status" name="status">
                                        <option value="open" ${incident.status === 'open' || !incident.status ? 'selected' : ''}>Open</option>
                                        <option value="in_progress" ${incident.status === 'in_progress' ? 'selected' : ''}>In Progress</option>
                                        <option value="resolved" ${incident.status === 'resolved' ? 'selected' : ''}>Resolved</option>
                                        <option value="closed" ${incident.status === 'closed' ? 'selected' : ''}>Closed</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Police Information Section -->
                        <div class="form-section">
                            <h3>Police Information</h3>
                            <div class="form-row">
                                <div class="form-field">
                                    <label for="police_notified">
                                        <input type="checkbox" id="police_notified" name="police_notified" ${incident.police_notified ? 'checked' : ''}>
                                        Police Notified
                                    </label>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="police_file_number">Police File Number</label>
                                    <input type="text" id="police_file_number" name="police_file_number"
                                           value="${incident.police_file_number || ''}"
                                           placeholder="Police file or case number">
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions">
                            <button type="button" class="secondary-button" data-action="back-to-incidents">
                                Cancel
                            </button>
                            <button type="submit" class="primary-button" id="update-incident">
                                Update Incident Report
                            </button>
                        </div>
                    </form>
                    </div>
                </div>
            </div>
        `;
    }

    async loadCreateIncidentContent() {
        console.log('Loading create incident content...');
        return `
            <div class="incident-form-container">
                <div class="section-header">
                    <h2>CREATE NEW INCIDENT REPORT</h2>
                    <button class="secondary-button" data-action="back-to-incidents">
                        ← Back to Incidents
                    </button>
                </div>

                <div class="incident-form-scroll">
                    <div class="incident-creation-form">
                    <form id="create-incident-form">
                        <!-- Basic Information Section -->
                        <div class="form-section">
                            <h3>Basic Information</h3>
                            <div class="form-row">
                                <div class="form-field">
                                    <label for="use_current_time">
                                        <input type="checkbox" id="use_current_time" name="use_current_time" checked>
                                        Use current date and time
                                    </label>
                                </div>
                            </div>
                            <div class="form-row" id="datetime-fields" style="display: none;">
                                <div class="form-group">
                                    <label for="incident_date">Incident Date *</label>
                                    <input type="date" id="incident_date" name="incident_date" required>
                                </div>
                                <div class="form-group">
                                    <label for="incident_time">Incident Time *</label>
                                    <input type="time" id="incident_time" name="incident_time" required>
                                </div>
                            </div>
                            <!-- Address Section -->
                            <div class="form-section">
                                <h3>Location Information</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="street_address">Street Address *</label>
                                        <input type="text" id="street_address" name="street_address" required
                                               placeholder="e.g., 123 Main Street">
                                    </div>
                                    <div class="form-group">
                                        <label for="city">City *</label>
                                        <input type="text" id="city" name="city" required
                                               placeholder="e.g., Cobourg" value="Cobourg">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="province">Province *</label>
                                        <select id="province" name="province" required>
                                            <option value="">Select province...</option>
                                            <option value="ON" selected>Ontario</option>
                                            <option value="BC">British Columbia</option>
                                            <option value="AB">Alberta</option>
                                            <option value="SK">Saskatchewan</option>
                                            <option value="MB">Manitoba</option>
                                            <option value="QC">Quebec</option>
                                            <option value="NB">New Brunswick</option>
                                            <option value="NS">Nova Scotia</option>
                                            <option value="PE">Prince Edward Island</option>
                                            <option value="NL">Newfoundland and Labrador</option>
                                            <option value="YT">Yukon</option>
                                            <option value="NT">Northwest Territories</option>
                                            <option value="NU">Nunavut</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="postal_code">Postal Code</label>
                                        <input type="text" id="postal_code" name="postal_code"
                                               placeholder="e.g., K9A 1A1" pattern="[A-Za-z]\d[A-Za-z] ?\d[A-Za-z]\d">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group full-width">
                                        <label for="location_notes">Location Notes</label>
                                        <input type="text" id="location_notes" name="location_notes"
                                               placeholder="Additional location details (e.g., near the park, behind the building)">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group full-width">
                                        <button type="button" class="secondary-button" id="geocode-address">
                                            📍 Get Coordinates from Address
                                        </button>
                                        <span id="geocode-status" class="geocode-status"></span>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="coordinates">Coordinates (Auto-filled)</label>
                                        <input type="text" id="coordinates" name="coordinates" readonly
                                               placeholder="Will be filled automatically">
                                    </div>
                                    <div class="form-group">
                                        <label for="location">Full Address (Auto-filled)</label>
                                        <input type="text" id="location" name="location" readonly
                                               placeholder="Will be filled automatically">
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="incident_type">Incident Type *</label>
                                    <select id="incident_type" name="incident_type" required>
                                        <option value="">Select incident type...</option>
                                        <option value="Assault">Assault</option>
                                        <option value="Theft">Theft</option>
                                        <option value="Vandalism">Vandalism</option>
                                        <option value="Drug Activity">Drug Activity</option>
                                        <option value="Mental Health Crisis">Mental Health Crisis</option>
                                        <option value="Medical Emergency">Medical Emergency</option>
                                        <option value="Disturbance">Disturbance</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Description Section -->
                        <div class="form-section">
                            <h3>Incident Details</h3>
                            <div class="form-row">
                                <div class="form-group full-width">
                                    <label for="description">Description *</label>
                                    <textarea id="description" name="description" required rows="4"
                                              placeholder="Detailed description of the incident"></textarea>
                                </div>
                            </div>
                            <!-- Record Linking Section -->
                            <div class="form-row">
                                <div class="form-group full-width">
                                    <h4>Linked Records</h4>
                                    <div class="record-links-container">
                                        <div class="link-section">
                                            <div class="link-header">
                                                <span>People Involved</span>
                                                <button type="button" class="link-button" onclick="app.showPersonLinkDialog()">
                                                    + Add Person
                                                </button>
                                            </div>
                                            <div class="linked-records" id="linked-people">
                                                <div class="no-links">No people linked yet</div>
                                            </div>
                                        </div>

                                        <div class="link-section">
                                            <div class="link-header">
                                                <span>Vehicles Involved</span>
                                                <button type="button" class="link-button" onclick="app.showVehicleLinkDialog()">
                                                    + Add Vehicle
                                                </button>
                                            </div>
                                            <div class="linked-records" id="linked-vehicles">
                                                <div class="no-links">No vehicles linked yet</div>
                                            </div>
                                        </div>

                                        <div class="link-section">
                                            <div class="link-header">
                                                <span>Related Addresses</span>
                                                <button type="button" class="link-button" onclick="app.showAddressLinkDialog()">
                                                    + Add Address
                                                </button>
                                            </div>
                                            <div class="linked-records" id="linked-addresses">
                                                <div class="no-links">No addresses linked yet</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="priority">Priority</label>
                                    <select id="priority" name="priority">
                                        <option value="low">Low</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="high">High</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="coordinates">Coordinates</label>
                                    <input type="text" id="coordinates" name="coordinates"
                                           placeholder="44.0956,-78.1675 (optional)">
                                </div>
                            </div>
                        </div>

                        <!-- Police Information Section -->
                        <div class="form-section">
                            <h3>Police Information</h3>
                            <div class="form-row">
                                <div class="form-field">
                                    <label for="police_notified">
                                        <input type="checkbox" id="police_notified" name="police_notified">
                                        Police Notified
                                    </label>
                                </div>
                            </div>
                            <div class="form-row" id="police-fields" style="display: none;">
                                <div class="form-group">
                                    <label for="police_file_number">Police File Number</label>
                                    <input type="text" id="police_file_number" name="police_file_number"
                                           placeholder="Police file or case number">
                                </div>
                            </div>
                        </div>

                        <!-- File Upload Section -->
                        <div class="form-section">
                            <h3>📎 Attachments</h3>
                            <div class="file-upload-area" id="file-upload-area">
                                <div class="upload-zone" id="upload-zone">
                                    <div class="upload-icon">📁</div>
                                    <div class="upload-text">
                                        <p>Drag and drop files here, or <button type="button" class="link-button" id="browse-files">browse</button></p>
                                        <p class="upload-hint">Supports photos, documents, and other file types</p>
                                    </div>
                                    <input type="file" id="file-input" multiple accept="*/*" style="display: none;">
                                </div>
                                <div class="uploaded-files" id="uploaded-files"></div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions">
                            <button type="button" class="secondary-button" data-action="back-to-incidents">
                                Cancel
                            </button>
                            <button type="submit" class="primary-button" id="submit-incident">
                                Create Incident Report
                            </button>
                        </div>
                    </form>
                    </div>
                </div>
            </div>
        `;
    }

    setupEditIncidentForm() {
        console.log('Setting up edit incident form...');

        // Handle form submission
        const form = document.getElementById('edit-incident-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleEditIncidentFormSubmission();
            });
        }

        // Handle back button
        const backButtons = document.querySelectorAll('[data-action="back-to-incidents"]');
        backButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.loadTabContent('incidents');
            });
        });
    }

    async handleEditIncidentFormSubmission() {
        const form = document.getElementById('edit-incident-form');
        const submitButton = document.getElementById('update-incident');

        if (!form) return;

        try {
            // Disable submit button
            submitButton.disabled = true;
            submitButton.textContent = 'Updating Incident...';

            // Collect form data
            const formData = new FormData(form);
            const updateData = {};

            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                if (key === 'police_notified') {
                    updateData[key] = true; // Checkbox is checked if present
                } else {
                    updateData[key] = value;
                }
            }

            // Handle unchecked checkbox
            if (!updateData.police_notified) {
                updateData.police_notified = false;
            }

            // Add metadata
            updateData.updated_at = new Date().toISOString();
            updateData.updated_by = this.auth.getCurrentUser()?.email || 'System';

            // Update the incident
            await this.data.update('incidents', this.editingIncidentId, updateData);

            this.showToast('Incident updated successfully', 'success');

            // Navigate back to incidents list
            this.loadTabContent('incidents');

        } catch (error) {
            console.error('Error updating incident:', error);
            this.ui.showDialog('Error', `Failed to update incident: ${error.message}`, 'error');
        } finally {
            // Re-enable submit button
            submitButton.disabled = false;
            submitButton.textContent = 'Update Incident Report';
        }
    }

    setupCreateIncidentForm() {
        console.log('Setting up create incident form...');
        // Handle datetime toggle
        const useCurrentTimeCheckbox = document.getElementById('use_current_time');
        const datetimeFields = document.getElementById('datetime-fields');
        const incidentDateInput = document.getElementById('incident_date');
        const incidentTimeInput = document.getElementById('incident_time');

        if (useCurrentTimeCheckbox) {
            useCurrentTimeCheckbox.addEventListener('change', () => {
                if (useCurrentTimeCheckbox.checked) {
                    datetimeFields.style.display = 'none';
                    // Remove required attribute when hidden
                    incidentDateInput.removeAttribute('required');
                    incidentTimeInput.removeAttribute('required');
                } else {
                    datetimeFields.style.display = 'flex';
                    // Add required attribute when visible
                    incidentDateInput.setAttribute('required', 'required');
                    incidentTimeInput.setAttribute('required', 'required');
                }
            });

            // Set initial state (checkbox is checked by default)
            if (useCurrentTimeCheckbox.checked) {
                incidentDateInput.removeAttribute('required');
                incidentTimeInput.removeAttribute('required');
            }
        }

        // Handle police notification toggle
        const policeNotifiedCheckbox = document.getElementById('police_notified');
        const policeFields = document.getElementById('police-fields');

        if (policeNotifiedCheckbox) {
            policeNotifiedCheckbox.addEventListener('change', () => {
                if (policeNotifiedCheckbox.checked) {
                    policeFields.style.display = 'flex';
                } else {
                    policeFields.style.display = 'none';
                }
            });
        }

        // Set up file upload functionality
        this.setupFileUpload();

        // Handle geocoding
        const geocodeButton = document.getElementById('geocode-address');
        if (geocodeButton) {
            geocodeButton.addEventListener('click', async () => {
                await this.geocodeIncidentAddress();
            });
        }

        // Auto-geocode when address fields change
        const addressFields = ['street_address', 'city', 'province', 'postal_code'];
        addressFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('blur', () => {
                    // Auto-geocode after a short delay if all required fields are filled
                    setTimeout(() => {
                        const streetAddress = document.getElementById('street_address')?.value;
                        const city = document.getElementById('city')?.value;
                        const province = document.getElementById('province')?.value;

                        if (streetAddress && city && province) {
                            this.geocodeIncidentAddress();
                        }
                    }, 500);
                });
            }
        });

        // Handle form submission
        const form = document.getElementById('create-incident-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleIncidentFormSubmission();
            });
        }

        // Handle back navigation
        document.querySelectorAll('[data-action="back-to-incidents"]').forEach(button => {
            button.addEventListener('click', () => {
                this.loadTabContent('incidents');
            });
        });
    }

    setupFileUpload() {
        const uploadZone = document.getElementById('upload-zone');
        const fileInput = document.getElementById('file-input');
        const browseButton = document.getElementById('browse-files');
        const uploadedFilesContainer = document.getElementById('uploaded-files');

        console.log('Setting up file upload...', {
            uploadZone: !!uploadZone,
            fileInput: !!fileInput,
            browseButton: !!browseButton,
            uploadedFilesContainer: !!uploadedFilesContainer
        });

        if (!uploadZone || !fileInput || !browseButton) {
            console.error('File upload elements not found');
            return;
        }

        this.uploadedFiles = [];

        // Handle browse button click
        browseButton.addEventListener('click', () => {
            fileInput.click();
        });

        // Handle file input change
        fileInput.addEventListener('change', (e) => {
            this.handleFileSelection(Array.from(e.target.files));
        });

        // Handle drag and drop
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('drag-over');
        });

        uploadZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('drag-over');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('drag-over');
            const files = Array.from(e.dataTransfer.files);
            this.handleFileSelection(files);
        });
    }

    handleFileSelection(files) {
        const uploadedFilesContainer = document.getElementById('uploaded-files');
        if (!uploadedFilesContainer) return;

        files.forEach(file => {
            // Add file to uploaded files array
            const fileData = {
                id: this.generateFileId(),
                file: file,
                name: file.name,
                size: file.size,
                type: file.type,
                status: 'pending'
            };

            this.uploadedFiles.push(fileData);

            // Create file preview element
            const fileElement = this.createFilePreviewElement(fileData);
            uploadedFilesContainer.appendChild(fileElement);
        });

        // Show uploaded files container if hidden
        if (this.uploadedFiles.length > 0) {
            uploadedFilesContainer.style.display = 'block';
        }
    }

    createFilePreviewElement(fileData) {
        const fileElement = document.createElement('div');
        fileElement.className = 'uploaded-file';
        fileElement.dataset.fileId = fileData.id;

        const isImage = fileData.type.startsWith('image/');
        const fileIcon = this.getFileIcon(fileData.type);

        fileElement.innerHTML = `
            <div class="file-preview">
                ${isImage ? `<img src="${URL.createObjectURL(fileData.file)}" alt="${fileData.name}" class="file-thumbnail">` : `<div class="file-icon">${fileIcon}</div>`}
            </div>
            <div class="file-info">
                <div class="file-name">${fileData.name}</div>
                <div class="file-size">${this.formatFileSize(fileData.size)}</div>
                <div class="file-status" id="status-${fileData.id}">Ready to upload</div>
            </div>
            <button type="button" class="remove-file-btn" data-file-id="${fileData.id}">×</button>
        `;

        // Handle file removal
        const removeButton = fileElement.querySelector('.remove-file-btn');
        removeButton.addEventListener('click', () => {
            this.removeFile(fileData.id);
        });

        return fileElement;
    }

    removeFile(fileId) {
        // Remove from uploaded files array
        this.uploadedFiles = this.uploadedFiles.filter(file => file.id !== fileId);

        // Remove from DOM
        const fileElement = document.querySelector(`[data-file-id="${fileId}"]`);
        if (fileElement) {
            fileElement.remove();
        }

        // Hide container if no files
        const uploadedFilesContainer = document.getElementById('uploaded-files');
        if (this.uploadedFiles.length === 0 && uploadedFilesContainer) {
            uploadedFilesContainer.style.display = 'none';
        }
    }

    getFileIcon(mimeType) {
        if (mimeType.startsWith('image/')) return '🖼️';
        if (mimeType === 'application/pdf') return '📄';
        if (mimeType.startsWith('video/')) return '🎥';
        if (mimeType.startsWith('audio/')) return '🎵';
        if (mimeType.includes('word') || mimeType.includes('document')) return '📝';
        if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';
        return '📁';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    generateFileId() {
        return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    async handleIncidentFormSubmission() {
        // Prevent duplicate submissions
        if (this.isSubmittingIncident) {
            console.log('⚠️ Incident submission already in progress, ignoring duplicate');
            return;
        }

        this.isSubmittingIncident = true;

        const form = document.getElementById('create-incident-form');
        const submitButton = document.getElementById('submit-incident');

        if (!form) {
            this.isSubmittingIncident = false;
            return;
        }

        try {
            // Disable submit button
            submitButton.disabled = true;
            submitButton.textContent = 'Creating Incident...';

            // Collect form data
            const formData = new FormData(form);
            const incidentData = {};

            // Process form fields (exclude UI-only fields)
            for (let [key, value] of formData.entries()) {
                // Skip UI-only fields that don't belong in the database
                if (key === 'use_current_time') {
                    continue;
                }

                if (key === 'police_notified') {
                    incidentData[key] = form.querySelector(`[name="${key}"]`).checked;
                } else {
                    incidentData[key] = value;
                }
            }

            // Generate incident number
            incidentData.incident_number = this.generateIncidentNumber();
            incidentData.reported_by = this.auth.getCurrentUser()?.email;
            incidentData.status = 'open';
            incidentData.created_at = new Date().toISOString();

            // Set incident date/time
            if (form.querySelector('[name="use_current_time"]').checked) {
                const now = new Date();
                incidentData.incident_date = now.toISOString().split('T')[0];
                incidentData.incident_time = now.toTimeString().split(' ')[0];
            }

            // Transform form data to match Supabase schema
            const dbIncidentData = {
                // Basic fields
                location: incidentData.location,
                narrative: incidentData.description, // Map description to narrative
                description: incidentData.description, // Also store in description field for dispatch
                incident_number: incidentData.incident_number,
                reporter_id: null, // Will be set by RLS policy or trigger

                // Dispatch-specific fields
                incident_date: incidentData.incident_date,
                incident_time: incidentData.incident_time,
                incident_type: incidentData.incident_type,
                priority: incidentData.priority || 'medium',
                status: incidentData.status || 'open',
                assigned_ranger: incidentData.assigned_ranger || null,
                coordinates: incidentData.coordinates || null,
                reported_by: incidentData.reported_by,
                dispatch_notes: incidentData.dispatch_notes || null,

                // Address components (for future use and better data structure)
                street_address: incidentData.street_address || null,
                city: incidentData.city || null,
                province: incidentData.province || null,
                postal_code: incidentData.postal_code || null,
                location_notes: incidentData.location_notes || null,

                // Additional fields
                is_urgent: incidentData.is_urgent || false,
                tags: incidentData.tags ? (Array.isArray(incidentData.tags) ? incidentData.tags : [incidentData.tags]) : []
            };

            // Save incident to database
            const result = await this.data.insert('incidents', dbIncidentData);
            const incidentId = result.id;

            // Upload files if any
            if (this.uploadedFiles && this.uploadedFiles.length > 0) {
                await this.uploadIncidentFiles(incidentId);
            }

            // Save linked records
            await this.saveIncidentCreationLinks(incidentId);

            // Refresh dispatch if it's currently active
            if (this.currentTab === 'dispatch') {
                this.loadDispatchIncidents();
            }

            // Emit data change event for any listeners
            if (this.data && this.data.emitDataChange) {
                this.data.emitDataChange('incidents', 'create', result);
            }

            // Show success message with dispatch option
            const dialogResult = await this.ui.showDialog(
                'Incident Created',
                `Incident ${incidentData.incident_number} has been successfully created.`,
                'success',
                {
                    showCancel: true,
                    confirmText: 'View in Dispatch',
                    cancelText: 'Back to Incidents'
                }
            );

            // Navigate based on user choice
            if (dialogResult === true) {
                // User chose to view in dispatch
                this.goToDispatch(incidentId);
            } else {
                // Navigate back to incidents
                this.loadTabContent('incidents');
            }

        } catch (error) {
            console.error('Error creating incident:', error);
            this.ui.showDialog('Error', `Failed to create incident: ${error.message}`, 'error');
        } finally {
            // Re-enable submit button
            submitButton.disabled = false;
            submitButton.textContent = 'Create Incident Report';

            // Reset submission flag
            this.isSubmittingIncident = false;
        }
    }

    async saveIncidentCreationLinks(incidentId) {
        try {
            const allLinks = [
                ...this.incidentCreationLinks.people.map(link => ({
                    incident_id: incidentId,
                    linked_record_type: 'person',
                    linked_record_id: link.record.id,
                    link_type: link.link_type,
                    notes: link.notes,
                    created_at: new Date().toISOString(),
                    created_by: this.auth.getCurrentUser()?.email || 'System'
                })),
                ...this.incidentCreationLinks.vehicles.map(link => ({
                    incident_id: incidentId,
                    linked_record_type: 'vehicle',
                    linked_record_id: link.record.id,
                    link_type: link.link_type,
                    notes: link.notes,
                    created_at: new Date().toISOString(),
                    created_by: this.auth.getCurrentUser()?.email || 'System'
                })),
                ...this.incidentCreationLinks.addresses.map(link => ({
                    incident_id: incidentId,
                    linked_record_type: 'address',
                    linked_record_id: link.record.id,
                    link_type: link.link_type,
                    notes: link.notes,
                    created_at: new Date().toISOString(),
                    created_by: this.auth.getCurrentUser()?.email || 'System'
                }))
            ];

            // Save all links
            for (const linkData of allLinks) {
                await this.data.insert('incident_links', linkData);
            }

            console.log(`Saved ${allLinks.length} incident links for incident ${incidentId}`);

            // Clear the temporary links
            this.incidentCreationLinks = {
                people: [],
                vehicles: [],
                addresses: []
            };

        } catch (error) {
            console.error('Error saving incident links:', error);
            // Don't throw error to prevent incident creation from failing
        }
    }

    async uploadIncidentFiles(incidentId) {
        if (!this.uploadedFiles || this.uploadedFiles.length === 0) return;

        try {
            // Check if IPC is available (Electron environment)
            const { ipcRenderer } = window.require ? window.require('electron') : null;
            if (!ipcRenderer) {
                throw new Error('File upload not available in this environment');
            }

            // Prepare files for upload (convert to serializable format)
            const filesToUpload = await Promise.all(
                this.uploadedFiles.map(async (uploadedFile) => {
                    const file = uploadedFile.file;
                    const arrayBuffer = await file.arrayBuffer();
                    return {
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        data: Array.from(new Uint8Array(arrayBuffer)) // Convert to array for IPC
                    };
                })
            );

            // Listen for progress updates
            ipcRenderer.on('upload-progress', (event, progress) => {
                const statusElement = document.getElementById(`status-${this.uploadedFiles[progress.fileIndex]?.id}`);
                if (statusElement) {
                    statusElement.textContent = `Uploading... ${progress.percentage}%`;
                }
            });

            // Upload files via IPC to main process
            const uploadResults = await ipcRenderer.invoke('upload-files-to-azure', filesToUpload, incidentId);

            // Save file metadata to database
            for (const fileResult of uploadResults.successful) {
                await this.data.insert('media', {
                    record_type: 'incident',
                    record_id: incidentId,
                    filename: fileResult.fileName,
                    stored_at: fileResult.url,
                    description: `Incident attachment: ${fileResult.fileName}`,
                    file_size: fileResult.fileSize,
                    content_type: fileResult.contentType,
                    uploaded_at: fileResult.uploadedAt
                });
            }

            // Clean up progress listener
            if (window.require) {
                const { ipcRenderer } = window.require('electron');
                ipcRenderer.removeAllListeners('upload-progress');
            }

            // Show upload results
            if (uploadResults.failed.length > 0) {
                console.warn('Some files failed to upload:', uploadResults.failed);
                this.ui.showDialog(
                    'Upload Warning',
                    `${uploadResults.totalUploaded} files uploaded successfully. ${uploadResults.totalFailed} files failed to upload.`,
                    'warning'
                );
            }

        } catch (error) {
            console.error('Error uploading files:', error);
            this.ui.showDialog('Upload Error', `Files could not be uploaded: ${error.message}`, 'error');

            // Clean up progress listener on error too
            if (window.require) {
                const { ipcRenderer } = window.require('electron');
                ipcRenderer.removeAllListeners('upload-progress');
            }
        }
    }

    generateIncidentNumber() {
        const now = new Date();
        const year = now.getFullYear().toString().slice(-2);
        const sequence = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
        return `IHARC-${year}-${sequence}`;
    }

    async loadIncidentAttachments(incidentId) {
        try {
            // Get media records for this incident
            const attachments = await this.data.search('media', {
                record_type: 'incident',
                record_id: incidentId
            });

            return attachments || [];
        } catch (error) {
            console.error('Error loading incident attachments:', error);
            return [];
        }
    }

    async downloadIncidentAttachment(attachment) {
        try {
            // Import Azure storage service
            const { azureStorage } = await import('./azure-storage.js');

            // Initialize Azure storage
            await azureStorage.initialize();

            // Extract file path from stored_at URL
            const url = new URL(attachment.stored_at);
            const filePath = url.pathname.split('/').slice(2).join('/'); // Remove container name

            // Generate secure download URL
            const downloadUrl = await azureStorage.generateSecureFileUrl(filePath);

            // Open download in new window/tab
            window.open(downloadUrl, '_blank');

        } catch (error) {
            console.error('Error downloading attachment:', error);
            this.ui.showDialog('Download Error', `Failed to download file: ${error.message}`, 'error');
        }
    }

    generateAttachmentsHTML(attachments) {
        if (!attachments || attachments.length === 0) {
            return '<div class="no-attachments">No attachments found</div>';
        }

        return attachments.map(attachment => `
            <div class="attachment-item" data-attachment-id="${attachment.id}">
                <div class="attachment-icon">${this.getFileIcon(attachment.content_type)}</div>
                <div class="attachment-info">
                    <div class="attachment-name">${attachment.filename}</div>
                    <div class="attachment-details">
                        ${this.formatFileSize(attachment.file_size)} •
                        ${this.formatDate(attachment.uploaded_at)}
                    </div>
                    ${attachment.description ? `<div class="attachment-description">${attachment.description}</div>` : ''}
                </div>
                <div class="attachment-actions">
                    <button class="action-button" onclick="app.downloadIncidentAttachment(${JSON.stringify(attachment).replace(/"/g, '&quot;')})">
                        Download
                    </button>
                </div>
            </div>
        `).join('');
    }

    getFileIcon(contentType) {
        if (!contentType) return '📁';
        if (contentType.startsWith('image/')) return '🖼️';
        if (contentType === 'application/pdf') return '📄';
        if (contentType.startsWith('video/')) return '🎥';
        if (contentType.startsWith('audio/')) return '🎵';
        if (contentType.includes('word') || contentType.includes('document')) return '📝';
        if (contentType.includes('excel') || contentType.includes('spreadsheet')) return '📊';
        return '📁';
    }

    formatFileSize(bytes) {
        if (!bytes || bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async loadRecordsContent() {
        return `
            <div class="content-section">
                <h2>RECORDS MANAGEMENT</h2>
                <div class="menu-grid">
                    <div class="menu-item" data-action="manage-people">
                        <div class="menu-icon">👥</div>
                        <div class="menu-title">Manage People</div>
                        <div class="menu-desc">View and manage person records</div>
                    </div>
                    <div class="menu-item" data-action="manage-organizations">
                        <div class="menu-icon">🏢</div>
                        <div class="menu-title">Manage Organizations</div>
                        <div class="menu-desc">View and manage partner organizations</div>
                    </div>
                    <div class="menu-item" data-action="add-person">
                        <div class="menu-icon">👤</div>
                        <div class="menu-title">Add Person</div>
                        <div class="menu-desc">Create new person record</div>
                    </div>
                    <div class="menu-item" data-action="add-address">
                        <div class="menu-icon">🏠</div>
                        <div class="menu-title">Add Address</div>
                        <div class="menu-desc">Create new address record</div>
                    </div>
                    <div class="menu-item" data-action="add-plate">
                        <div class="menu-icon">🚗</div>
                        <div class="menu-title">Add License Plate</div>
                        <div class="menu-desc">Create new plate record</div>
                    </div>
                    <div class="menu-item" data-action="manage-bikes">
                        <div class="menu-icon">🚲</div>
                        <div class="menu-title">Manage Bikes</div>
                        <div class="menu-desc">View and manage bike records</div>
                    </div>
                    <div class="menu-item" data-action="search-records">
                        <div class="menu-icon">🔍</div>
                        <div class="menu-title">Search Records</div>
                        <div class="menu-desc">Find existing records</div>
                    </div>
                    <div class="menu-item" data-action="search-incidents">
                        <div class="menu-icon">📋</div>
                        <div class="menu-title">Search Incidents</div>
                        <div class="menu-desc">Search historical incidents</div>
                    </div>
                </div>
            </div>
        `;
    }

    async loadPeopleManagementContent() {
        const people = await this.data.search('people', {});

        return `
            <div class="content-section">
                <div class="section-header">
                    <h2>PEOPLE MANAGEMENT</h2>
                    <div class="header-actions">
                        <button class="primary-button" data-action="add-person">
                            <span class="button-icon">👤</span>
                            Add Person
                        </button>
                        <button class="secondary-button" data-action="back-to-records">
                            <span class="button-icon">←</span>
                            Back to Records
                        </button>
                    </div>
                </div>

                <div class="search-section">
                    <div class="search-bar">
                        <input type="text" id="people-search" placeholder="Search people by name, email, phone..." class="search-input">
                        <button class="search-button" id="search-people-btn">🔍</button>
                    </div>
                </div>

                <div class="people-list-container">
                    <div class="people-count">
                        <span id="people-count">${people.length}</span> people found
                    </div>
                    <div class="people-list" id="people-list">
                        ${this.renderPeopleList(people)}
                    </div>
                </div>
            </div>
        `;
    }

    async loadOrganizationsManagementContent() {
        const organizations = await this.data.search('organizations', {});

        return `
            <div class="content-section">
                <div class="section-header">
                    <h2>ORGANIZATION MANAGEMENT</h2>
                    <div class="header-actions">
                        <button class="primary-button" data-action="add-organization">
                            <span class="button-icon">🏢</span>
                            Add Organization
                        </button>
                        <button class="secondary-button" data-action="back-to-records">
                            <span class="button-icon">←</span>
                            Back to Records
                        </button>
                    </div>
                </div>

                <div class="search-section">
                    <div class="search-bar">
                        <input type="text" id="organizations-search" placeholder="Search organizations by name, type, services..." class="search-input">
                        <button class="search-button" id="search-organizations-btn">🔍</button>
                    </div>
                </div>

                <div class="organizations-list-container">
                    <div class="organizations-count">
                        <span id="organizations-count">${organizations.length}</span> organizations found
                    </div>
                    <div class="organizations-list" id="organizations-list">
                        ${this.renderOrganizationsList(organizations)}
                    </div>
                </div>
            </div>
        `;
    }

    async loadBikesManagementContent() {
        const bikes = await this.bikeManager.getAllBikes();

        return `
            <div class="content-section">
                <div class="section-header">
                    <h2>BIKE MANAGEMENT</h2>
                    <div class="header-actions">
                        <button class="primary-button" data-action="add-bike">
                            <span class="button-icon">🚲</span>
                            Register Bike
                        </button>
                        <button class="secondary-button" data-action="back-to-records">
                            <span class="button-icon">←</span>
                            Back to Records
                        </button>
                    </div>
                </div>

                <div class="search-section">
                    <div class="search-bar">
                        <input type="text" id="bikes-search" placeholder="Search bikes by serial, make, model, owner..." class="search-input">
                        <button class="search-button" id="search-bikes-btn">🔍</button>
                    </div>
                    <div class="filter-section">
                        <select id="bike-status-filter" class="filter-select">
                            <option value="">All Bikes</option>
                            <option value="registered">Registered</option>
                            <option value="stolen">Stolen</option>
                            <option value="recovered">Recovered</option>
                        </select>
                    </div>
                </div>

                <div class="bikes-list-container">
                    <div class="bikes-count">
                        <span id="bikes-count">${bikes.length}</span> bikes found
                    </div>
                    <div class="bikes-list" id="bikes-list">
                        ${this.renderBikesList(bikes)}
                    </div>
                </div>
            </div>
        `;
    }

    renderPeopleList(people) {
        if (people.length === 0) {
            return '<div class="no-records">No people records found. Click "Add Person" to create the first record.</div>';
        }

        return people.map(person => `
            <div class="person-card" data-person-id="${person.id}">
                <div class="person-avatar">
                    <div class="avatar-placeholder">
                        ${(person.first_name?.[0] || '?').toUpperCase()}${(person.last_name?.[0] || '').toUpperCase()}
                    </div>
                </div>
                <div class="person-info">
                    <div class="person-name">
                        ${person.first_name || ''} ${person.last_name || ''}
                    </div>
                    <div class="person-details">
                        ${person.email ? `<span class="detail-item">📧 ${person.email}</span>` : ''}
                        ${person.phone ? `<span class="detail-item">📞 ${person.phone}</span>` : ''}
                        ${person.housing_status ? `<span class="detail-item">🏠 ${person.housing_status}</span>` : ''}
                    </div>
                    <div class="person-meta">
                        ID: ${person.id} | Created: ${this.formatDate(person.created_at)}
                    </div>
                </div>
                <div class="person-actions">
                    <button class="action-button view-button" data-action="view-person" data-person-id="${person.id}">
                        View Details
                    </button>
                </div>
            </div>
        `).join('');
    }

    renderBikesList(bikes) {
        if (bikes.length === 0) {
            return '<div class="no-records">No bike records found. Click "Register Bike" to create the first record.</div>';
        }

        return bikes.map(bike => `
            <div class="bike-card ${bike.is_stolen ? 'stolen' : ''}" data-bike-id="${bike.id}">
                <div class="bike-status">
                    ${bike.is_stolen ?
                        '<span class="status-badge stolen">STOLEN</span>' :
                        '<span class="status-badge registered">REGISTERED</span>'
                    }
                </div>
                <div class="bike-info">
                    <div class="bike-header">
                        <div class="bike-make-model">
                            ${bike.make} ${bike.model}
                        </div>
                        <div class="bike-serial">
                            Serial: ${bike.serial_number}
                        </div>
                    </div>
                    <div class="bike-details">
                        <span class="detail-item">🎨 ${bike.color}</span>
                        <span class="detail-item">👤 ${bike.owner_name}</span>
                        ${bike.owner_email ? `<span class="detail-item">📧 ${bike.owner_email}</span>` : ''}
                        ${bike.value ? `<span class="detail-item">💰 $${bike.value}</span>` : ''}
                    </div>
                    <div class="bike-meta">
                        Registered: ${this.formatDate(bike.registered_at)}
                        ${bike.is_stolen && bike.theft_date ? ` | Stolen: ${this.formatDate(bike.theft_date)}` : ''}
                    </div>
                </div>
                <div class="bike-actions">
                    <button class="action-button view-button" data-action="view-bike" data-bike-id="${bike.id}">
                        View Details
                    </button>
                    ${bike.is_stolen ?
                        `<button class="action-button recovery-button" data-action="mark-bike-recovered" data-bike-id="${bike.id}">
                            Mark Recovered
                        </button>` :
                        `<button class="action-button report-button" data-action="report-bike-stolen" data-bike-id="${bike.id}">
                            Report Stolen
                        </button>`
                    }
                </div>
            </div>
        `).join('');
    }

    renderOrganizationsList(organizations) {
        if (organizations.length === 0) {
            return '<div class="no-records">No organization records found. Click "Add Organization" to create the first record.</div>';
        }

        return organizations.map(org => `
            <div class="organization-card" data-organization-id="${org.id}">
                <div class="organization-icon">
                    <div class="org-icon-placeholder">
                        ${(org.name?.[0] || '?').toUpperCase()}
                    </div>
                </div>
                <div class="organization-info">
                    <div class="organization-name">
                        ${org.name || 'Unnamed Organization'}
                    </div>
                    <div class="organization-details">
                        ${org.organization_type ? `<span class="detail-item">🏷️ ${org.organization_type}</span>` : ''}
                        ${org.phone ? `<span class="detail-item">📞 ${org.phone}</span>` : ''}
                        ${org.email ? `<span class="detail-item">📧 ${org.email}</span>` : ''}
                        ${org.partnership_type ? `<span class="detail-item">🤝 ${org.partnership_type}</span>` : ''}
                    </div>
                    <div class="organization-services">
                        ${org.services_provided ? `Services: ${org.services_provided.substring(0, 100)}${org.services_provided.length > 100 ? '...' : ''}` : 'No services listed'}
                    </div>
                    ${org.services_tags && org.services_tags.length > 0 ? `
                        <div class="services-tags">
                            ${org.services_tags.map(tag => `<span class="service-tag">${tag}</span>`).join('')}
                        </div>
                    ` : ''}
                    <div class="organization-meta">
                        ID: ${org.id} | Status: ${org.status || 'Active'} | Created: ${this.formatDate(org.created_at)}
                    </div>
                </div>
                <div class="organization-actions">
                    <button class="action-button view-button" data-action="view-organization" data-organization-id="${org.id}">
                        View Details
                    </button>
                    <button class="action-button edit-button" data-action="edit-organization" data-organization-id="${org.id}">
                        Edit
                    </button>
                </div>
            </div>
        `).join('');
    }

    async loadPersonDetailContent(personId) {
        const person = await this.data.get('people', personId);
        if (!person) {
            return '<div class="error">Person not found</div>';
        }

        this.selectedPerson = person;

        return `
            <div class="content-section">
                <div class="section-header">
                    <h2>PERSON DETAILS</h2>
                    <div class="header-actions">
                        <button class="primary-button" data-action="edit-person" data-person-id="${person.id}">
                            <span class="button-icon">✏️</span>
                            Edit Person
                        </button>
                        <button class="secondary-button" data-action="back-to-people">
                            <span class="button-icon">←</span>
                            Back to People
                        </button>
                    </div>
                </div>

                <div class="person-detail-container">
                    <div class="person-detail-header">
                        <div class="person-avatar-large">
                            <div class="avatar-placeholder-large">
                                ${(person.first_name?.[0] || '?').toUpperCase()}${(person.last_name?.[0] || '').toUpperCase()}
                            </div>
                        </div>
                        <div class="person-title-info">
                            <h3 class="person-full-name">
                                ${person.first_name || ''} ${person.last_name || ''}
                            </h3>
                            <div class="person-id">ID: ${person.id}</div>
                            <div class="person-created">Created: ${this.formatDate(person.created_at)}</div>
                        </div>
                    </div>

                    <div class="person-detail-tabs">
                        <button class="person-detail-tab active" data-tab="personal">Personal Info</button>
                        <button class="person-detail-tab" data-tab="pets">Pets</button>
                        <button class="person-detail-tab" data-tab="medical">Medical</button>
                        <button class="person-detail-tab" data-tab="disabilities">Disabilities</button>
                        <button class="person-detail-tab" data-tab="case-management">Case Management</button>
                        <button class="person-detail-tab" data-tab="barriers">Service Barriers</button>
                        <button class="person-detail-tab" data-tab="support">Support Network</button>
                        <button class="person-detail-tab" data-tab="criminal-justice">Criminal Justice</button>
                        <button class="person-detail-tab" data-tab="activities">Activities</button>
                    </div>

                    <div class="person-detail-content">
                        <div class="tab-content active" id="personal-tab">
                            ${this.generatePersonalInfoTab(person)}
                        </div>
                        <div class="tab-content" id="pets-tab">
                            ${this.generatePetsTab(person)}
                        </div>
                        <div class="tab-content" id="medical-tab">
                            ${this.generateMedicalTab(person)}
                        </div>
                        <div class="tab-content" id="disabilities-tab">
                            ${this.generateDisabilitiesTab(person)}
                        </div>
                        <div class="tab-content" id="case-management-tab">
                            ${this.generateCaseManagementTab(person)}
                        </div>
                        <div class="tab-content" id="barriers-tab">
                            ${this.generateServiceBarriersTab(person)}
                        </div>
                        <div class="tab-content" id="support-tab">
                            ${this.generateSupportNetworkTab(person)}
                        </div>
                        <div class="tab-content" id="criminal-justice-tab">
                            ${this.generateCriminalJusticeTab(person)}
                        </div>
                        <div class="tab-content" id="activities-tab">
                            ${this.generateActivitiesTab(person)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generatePersonalInfoTab(person) {
        return `
            <div class="detail-sections">
                <div class="detail-section">
                    <h4>Personal Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>First Name:</label>
                            <span>${person.first_name || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Last Name:</label>
                            <span>${person.last_name || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Date of Birth:</label>
                            <span>${person.date_of_birth || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Gender:</label>
                            <span>${person.gender || 'Not provided'}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>Contact Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Email:</label>
                            <span>${person.email || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Phone:</label>
                            <span>${person.phone || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Emergency Contact:</label>
                            <span>${person.emergency_contact || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Emergency Phone:</label>
                            <span>${person.emergency_phone || 'Not provided'}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>Housing & Services</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Housing Status:</label>
                            <span>${person.housing_status || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Services Received:</label>
                            <span>${person.services_received || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Special Needs:</label>
                            <span>${person.special_needs || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Medical Conditions:</label>
                            <span>${person.medical_conditions || 'Not provided'}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>Additional Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item full-width">
                            <label>Notes:</label>
                            <span>${person.notes || 'No additional notes'}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>Record Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Created By:</label>
                            <span>${person.created_by || 'System'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Created At:</label>
                            <span>${this.formatDateTime(person.created_at)}</span>
                        </div>
                        <div class="detail-item">
                            <label>Last Updated:</label>
                            <span>${this.formatDateTime(person.updated_at) || 'Never'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Updated By:</label>
                            <span>${person.updated_by || 'N/A'}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generatePetsTab(person) {
        return `
            <div class="pets-section">
                <div class="section-header">
                    <h4>Pet Information</h4>
                    <button class="primary-button" data-action="add-pet" data-person-id="${person.id}">
                        <span class="button-icon">+</span>
                        Add Pet
                    </button>
                </div>
                <div id="pets-list" class="pets-list">
                    <div class="loading">Loading pets...</div>
                </div>
            </div>
        `;
    }

    generateMedicalTab(person) {
        return `
            <div class="medical-section">
                <div class="section-header">
                    <h4>Medical Information</h4>
                    <button class="primary-button" data-action="add-medical-issue" data-person-id="${person.id}">
                        <span class="button-icon">+</span>
                        Add Medical Issue
                    </button>
                </div>
                <div id="medical-issues-list" class="medical-issues-list">
                    <div class="loading">Loading medical issues...</div>
                </div>
            </div>
        `;
    }

    generateActivitiesTab(person) {
        return `
            <div class="activity-section">
                <h4>Recent Activities</h4>
                <div id="person-activities" class="activities-list">
                    <div class="loading">Loading activities...</div>
                </div>
            </div>
        `;
    }

    formatDate(dateString) {
        if (!dateString) return 'Unknown';
        try {
            return new Date(dateString).toLocaleDateString();
        } catch {
            return dateString;
        }
    }

    formatDateTime(dateString) {
        if (!dateString) return 'Unknown';
        try {
            return new Date(dateString).toLocaleString();
        } catch {
            return dateString;
        }
    }

    async loadReportsContent() {
        return `
            <div class="content-section">
                <h2>REPORTS & ANALYTICS</h2>
                <div class="menu-grid">
                    <div class="menu-item" data-action="generate-report">
                        <div class="menu-icon">📊</div>
                        <div class="menu-title">Generate Report</div>
                        <div class="menu-desc">Create custom reports</div>
                    </div>
                    <div class="menu-item" data-action="export-data">
                        <div class="menu-icon">💾</div>
                        <div class="menu-title">Export Data</div>
                        <div class="menu-desc">Export records and reports</div>
                    </div>
                </div>
            </div>
        `;
    }

    async loadSystemContent() {
        return `
            <div class="content-section">
                <h2>SYSTEM ADMINISTRATION</h2>
                <div class="menu-grid">
                    <div class="menu-item" data-action="sync-data">
                        <div class="menu-icon">🔄</div>
                        <div class="menu-title">Sync Data</div>
                        <div class="menu-desc">Synchronize with server</div>
                    </div>
                    <div class="menu-item" data-action="user-profile">
                        <div class="menu-icon">👤</div>
                        <div class="menu-title">User Profile</div>
                        <div class="menu-desc">Manage your account</div>
                    </div>
                    <div class="menu-item" data-action="settings">
                        <div class="menu-icon">⚙️</div>
                        <div class="menu-title">Settings</div>
                        <div class="menu-desc">Configure application</div>
                    </div>
                    <div class="menu-item" data-action="check-updates">
                        <div class="menu-icon">🔍</div>
                        <div class="menu-title">Check for Updates</div>
                        <div class="menu-desc">Check for application updates</div>
                    </div>
                    <div class="menu-item" data-action="release-notes">
                        <div class="menu-icon">📋</div>
                        <div class="menu-title">Release Notes</div>
                        <div class="menu-desc">View current version release notes</div>
                    </div>
                    <div class="menu-item" data-action="create-test-data">
                        <div class="menu-icon">🧪</div>
                        <div class="menu-title">Add Test Data</div>
                        <div class="menu-desc">Create sample data for testing</div>
                    </div>
                    <div class="menu-item" data-action="remove-test-data">
                        <div class="menu-icon">🗑️</div>
                        <div class="menu-title">Remove Test Data</div>
                        <div class="menu-desc">Clean up test data</div>
                    </div>
                    <div class="menu-item" data-action="about">
                        <div class="menu-icon">ℹ️</div>
                        <div class="menu-title">About</div>
                        <div class="menu-desc">System information</div>
                    </div>
                </div>
            </div>
        `;
    }

    async loadAdminContent() {
        return `
            <div class="admin-container">
                <div class="admin-header">
                    <h2>ADMIN PANEL</h2>
                    <div class="admin-user-info">
                        Logged in as: ${this.currentUser?.email} (Administrator)
                    </div>
                </div>

                <div class="admin-sections">
                    <div class="admin-nav">
                        <button class="admin-nav-btn active" data-section="items">ITEM MANAGEMENT</button>
                        <button class="admin-nav-btn" data-section="users">USER MANAGEMENT</button>
                        <button class="admin-nav-btn" data-section="roles">ROLE MANAGEMENT</button>
                    </div>

                    <div class="admin-section" id="items-section">
                        <div class="section-header">
                            <h3>ITEM MANAGEMENT</h3>
                            <button class="primary-button" id="add-item-btn">ADD NEW ITEM</button>
                        </div>

                        <div class="item-controls">
                            <div class="search-controls">
                                <input type="text" id="item-search" placeholder="Search items..." class="search-input">
                                <select id="category-filter" class="filter-select">
                                    <option value="">All Categories</option>
                                    <option value="food">Food</option>
                                    <option value="hygiene">Hygiene</option>
                                    <option value="clothing">Clothing</option>
                                    <option value="medical">Medical</option>
                                    <option value="other">Other</option>
                                </select>
                                <select id="status-filter" class="filter-select">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>

                        <div class="items-list" id="items-list">
                            <div class="loading">Loading items...</div>
                        </div>
                    </div>

                    <div class="admin-section" id="users-section" style="display: none;">
                        <div class="section-header">
                            <h3>USER MANAGEMENT</h3>
                            <button class="primary-button" id="add-user-btn">ADD NEW USER</button>
                        </div>

                        <div class="user-controls">
                            <div class="search-controls">
                                <input type="text" id="user-search" placeholder="Search users..." class="search-input">
                                <select id="role-filter" class="filter-select">
                                    <option value="">All Roles</option>
                                    <option value="iharc_staff">Staff</option>
                                    <option value="iharc_admin">Admin</option>
                                </select>
                            </div>
                        </div>

                        <div class="users-list" id="users-list">
                            <div class="loading">Loading users...</div>
                        </div>
                    </div>

                    <div class="admin-section" id="roles-section" style="display: none;">
                        <div class="section-header">
                            <h3>ROLE MANAGEMENT</h3>
                            <button class="primary-button" id="add-role-btn">CREATE CUSTOM ROLE</button>
                        </div>

                        <div class="role-controls">
                            <div class="search-controls">
                                <input type="text" id="role-search" placeholder="Search roles..." class="search-input">
                                <select id="role-type-filter" class="filter-select">
                                    <option value="">All Roles</option>
                                    <option value="system">System Roles</option>
                                    <option value="custom">Custom Roles</option>
                                </select>
                            </div>
                        </div>

                        <div class="roles-list" id="roles-list">
                            <div class="loading">Loading roles...</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async loadDispatchContent() {
        return `
            <div class="dispatch-container">
                <div class="dispatch-grid">
                    <!-- Left Pane - Incident List -->
                    <div class="dispatch-incident-list">
                        <div class="dispatch-header">
                            <h3>🚨 ACTIVE INCIDENTS</h3>
                            <div class="dispatch-controls">
                                <span class="incident-count" id="dispatch-incident-count">0</span>
                                <button class="refresh-btn" id="dispatch-refresh">↻</button>
                            </div>
                        </div>
                        <div class="incident-list-container" id="dispatch-incident-list">
                            <div class="loading">Loading incidents...</div>
                        </div>
                    </div>

                    <!-- Right Pane - Incident Detail -->
                    <div class="dispatch-incident-detail">
                        <div class="dispatch-header">
                            <h3>📋 INCIDENT DETAILS</h3>
                            <div class="dispatch-shortcuts">
                                <span class="shortcut-hint">F2: Assign | F3: Status | F4: Note | F8: Close</span>
                            </div>
                        </div>
                        <div class="incident-detail-container" id="dispatch-incident-detail">
                            <div class="no-selection">Select an incident to view details</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    updateDateTime() {
        const datetimeElement = document.getElementById('datetime');
        const now = new Date();
        const formatted = now.toLocaleString('en-CA', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
        datetimeElement.textContent = formatted;
    }

    setupNetworkMonitoring() {
        // Initial network status check
        this.updateNetworkStatus();

        // Listen for online/offline events
        window.addEventListener('online', () => {
            console.log('Network: Online');
            this.updateNetworkStatus();
        });

        window.addEventListener('offline', () => {
            console.log('Network: Offline');
            this.updateNetworkStatus();
        });

        // Periodic network check (every 30 seconds)
        setInterval(() => {
            this.checkNetworkConnection();
        }, 30000);
    }

    updateNetworkStatus() {
        const networkStatus = document.getElementById('network-status');
        const networkText = document.getElementById('network-text');
        const isOnline = navigator.onLine;

        if (networkStatus && networkText) {
            if (isOnline) {
                networkStatus.className = 'network-status online';
                networkText.textContent = 'ONLINE';
            } else {
                networkStatus.className = 'network-status offline';
                networkText.textContent = 'OFFLINE';
            }
        }
    }

    async checkNetworkConnection() {
        // Additional check by trying to reach Supabase
        try {
            if (navigator.onLine && this.auth.supabase) {
                // Try a simple query to verify actual connectivity
                const { error } = await this.auth.supabase
                    .from('case_mgmt.incidents')
                    .select('count')
                    .limit(1);

                // If we get here without error, we're truly online
                if (!error) {
                    this.updateNetworkStatus();
                    return;
                }
            }
        } catch (error) {
            // Network might be down even if navigator.onLine is true
            console.log('Network connectivity check failed:', error.message);
        }

        // Update status based on navigator.onLine
        this.updateNetworkStatus();
    }

    setupMenuHandlers() {
        // Add click handlers to menu items and buttons
        document.addEventListener('click', async (e) => {
            const menuItem = e.target.closest('.menu-item');
            const button = e.target.closest('button[data-action]');
            const personCard = e.target.closest('.person-card');
            const viewButton = e.target.closest('.view-button');
            const encampmentCard = e.target.closest('.encampment-item');
            const encampmentButton = e.target.closest('.encampment-item .action-button');

            // Handle menu items
            if (menuItem) {
                const action = menuItem.dataset.action;
                if (action) {
                    await this.handleMenuAction(action);
                }
            }

            // Handle buttons with data-action
            if (button) {
                const action = button.dataset.action;
                if (action) {
                    await this.handleMenuAction(action, button);
                }
            }

            // Handle person card clicks
            if (personCard && !viewButton) {
                const personId = personCard.dataset.personId;
                if (personId) {
                    await this.viewPersonDetail(personId);
                }
            }

            // Handle view button clicks
            if (viewButton) {
                e.stopPropagation();
                const personId = viewButton.dataset.personId;
                if (personId) {
                    await this.viewPersonDetail(personId);
                }
            }

            // Handle encampment card clicks
            if (encampmentCard && !encampmentButton) {
                const encampmentId = encampmentCard.dataset.encampmentId;
                if (encampmentId) {
                    await this.viewEncampmentDetail(encampmentId);
                }
            }

            // Handle encampment button clicks (prevent card click)
            if (encampmentButton) {
                e.stopPropagation();
            }
        });
    }

    setupDataChangeListeners() {
        // Listen for real-time data changes
        window.addEventListener('dataChange', (event) => {
            const { table, operation, record } = event.detail;
            console.log(`🔄 Real-time UI update: ${operation} in ${table}`, record?.id);

            // Refresh current view if it's affected by this change
            this.handleDataChange(table, operation, record);
        });
    }

    handleDataChange(table, operation, record) {
        // Refresh the current tab if it displays data from the changed table
        if (this.shouldRefreshCurrentView(table, operation)) {
            console.log(`🔄 Refreshing current view due to ${operation} in ${table}`);
            this.refreshCurrentView();
        }

        // Show notification for significant changes
        if (operation === 'insert' && table === 'incidents') {
            this.showNotification(`New incident created: ${record.title || record.id}`, 'info');
        }
    }

    shouldRefreshCurrentView(table, operation) {
        const currentTab = this.currentTab;

        // Map tabs to tables they display
        const tabTableMap = {
            'people': ['people', 'addresses'],
            'incidents': ['incidents'],
            'dispatch': ['incidents'],
            'property': ['property_records', 'addresses'],
            'bikes': ['bikes'],
            'encampments': ['encampments'],
            'admin': ['people', 'incidents', 'addresses', 'bikes', 'items', 'encampments']
        };

        const relevantTables = tabTableMap[currentTab] || [];
        return relevantTables.includes(table);
    }

    refreshCurrentView() {
        // Refresh the current tab content
        if (this.currentTab) {
            this.loadTabContent(this.currentTab);
        }
    }

    showNotification(message, type = 'info') {
        // Create a simple notification system
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: var(--bg-color);
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 10000;
            animation: slideIn 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        // Remove after 5 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    async handleMenuAction(action, element = null) {
        this.ui.setStatus(`Executing ${action}...`);

        try {
            // Handle navigation actions
            switch (action) {
                case 'manage-people':
                    await this.loadTabContent('records', 'people-management');
                    break;
                case 'manage-organizations':
                    await this.loadTabContent('records', 'organizations-management');
                    break;
                case 'manage-bikes':
                    await this.loadTabContent('records', 'bikes-management');
                    break;
                case 'back-to-records':
                    await this.loadTabContent('records');
                    break;
                case 'back-to-people':
                    await this.loadTabContent('records', 'people-management');
                    break;
                case 'view-organization':
                    const organizationId = element?.dataset.organizationId;
                    if (organizationId) {
                        await this.viewOrganizationDetail(organizationId);
                    }
                    break;
                case 'edit-organization':
                    const editOrganizationId = element?.dataset.organizationId;
                    if (editOrganizationId) {
                        await this.editOrganization(editOrganizationId);
                    }
                    break;
                case 'add-organization':
                    await this.addOrganization();
                    break;
                case 'view-person':
                    const personId = element?.dataset.personId;
                    if (personId) {
                        await this.viewPersonDetail(personId);
                    }
                    break;
                case 'edit-person':
                    const editPersonId = element?.dataset.personId;
                    if (editPersonId) {
                        await this.editPerson(editPersonId);
                    }
                    break;
                case 'add-pet':
                    const personIdForPet = element?.dataset.personId;
                    if (personIdForPet) {
                        await this.addPet(personIdForPet);
                    }
                    break;
                case 'edit-pet':
                    const petId = element?.dataset.petId;
                    if (petId) {
                        await this.editPet(petId);
                    }
                    break;
                case 'delete-pet':
                    const deletePetId = element?.dataset.petId;
                    if (deletePetId) {
                        await this.deletePet(deletePetId);
                    }
                    break;
                case 'add-medical-issue':
                    const personIdForMedical = element?.dataset.personId;
                    if (personIdForMedical) {
                        await this.addMedicalIssue(personIdForMedical);
                    }
                    break;
                case 'edit-medical-issue':
                    const medicalIssueId = element?.dataset.medicalIssueId;
                    if (medicalIssueId) {
                        await this.editMedicalIssue(medicalIssueId);
                    }
                    break;
                case 'delete-medical-issue':
                    const deleteMedicalIssueId = element?.dataset.medicalIssueId;
                    if (deleteMedicalIssueId) {
                        await this.deleteMedicalIssue(deleteMedicalIssueId);
                    }
                    break;
                case 'list-found-property':
                    await this.loadTabContent('property', 'found-property-list');
                    break;
                case 'list-missing-property':
                    await this.loadTabContent('property', 'missing-property-list');
                    break;
                case 'log-property-recovery':
                    await this.loadTabContent('property', 'log-property-recovery');
                    break;
                case 'create-missing-report':
                    await this.loadTabContent('property', 'create-missing-report');
                    break;
                case 'add-bike':
                    await this.addBike();
                    break;
                case 'view-bike':
                    const bikeId = element?.dataset.bikeId;
                    if (bikeId) {
                        await this.viewBikeDetails(bikeId);
                    }
                    break;
                case 'report-bike-stolen':
                    const reportBikeId = element?.dataset.bikeId;
                    if (reportBikeId) {
                        await this.reportBikeStolen(reportBikeId);
                    }
                    break;
                case 'add-encampment':
                    await this.loadTabContent('encampments', 'add-encampment');
                    break;
                case 'view-encampment':
                    const encampmentId = element?.dataset.encampmentId;
                    if (encampmentId) {
                        await this.viewEncampmentDetail(encampmentId);
                    }
                    break;
                case 'edit-encampment':
                    const editEncampmentId = element?.dataset.encampmentId;
                    if (editEncampmentId) {
                        await this.editEncampment(editEncampmentId);
                    }
                    break;
                case 'back-to-encampments':
                    await this.loadTabContent('encampments');
                    break;
                case 'view-encampment-map':
                    const mapEncampmentId = element?.dataset.encampmentId;
                    if (mapEncampmentId) {
                        await this.viewEncampmentOnMap(mapEncampmentId);
                    }
                    break;
                case 'view-encampments-map':
                    await this.viewAllEncampmentsMap();
                    break;
                case 'mark-bike-recovered':
                    const recoveredBikeId = element?.dataset.bikeId;
                    if (recoveredBikeId) {
                        await this.markBikeRecovered(recoveredBikeId);
                    }
                    break;
                case 'create-incident-screen':
                    await this.loadTabContent('incidents', 'create-incident');
                    break;
                case 'search-incidents':
                    await this.loadTabContent('records', 'incident-search');
                    break;
                default:
                    // Handle other commands through command manager
                    await this.commands.executeCommand(action);
            }

            this.ui.setStatus('Ready');
        } catch (error) {
            console.error('Command execution error:', error);
            this.ui.setStatus('Error');
        }
    }

    async viewPersonDetail(personId) {
        try {
            const person = await this.data.get('people', personId);
            if (person) {
                this.selectedPerson = person;
                await this.loadTabContent('records', 'person-detail');

                // Set up tab navigation for person detail
                setTimeout(() => {
                    this.setupPersonDetailTabs();
                    this.loadPersonActivities(personId);
                    this.loadPersonPets(personId);
                    this.loadPersonMedicalIssues(personId);
                }, 100);
            }
        } catch (error) {
            console.error('Error viewing person detail:', error);
            this.ui.showDialog('Error', 'Failed to load person details', 'error');
        }
    }

    async editPerson(personId) {
        try {
            const person = await this.data.get('people', personId);
            if (person) {
                // Use the existing add-person command but pre-populate with current data
                const fields = this.data.schema.generateFormFields('people');

                // Pre-populate fields with current person data
                fields.forEach(field => {
                    if (person[field.name] !== undefined) {
                        field.value = person[field.name];
                    }
                });

                this.ui.showForm('Edit Person Record', fields, async (formData) => {
                    try {
                        const dbData = this.data.schema.convertFormToDatabase('people', formData);
                        const updatedData = {
                            ...dbData,
                            updated_by: this.auth.getCurrentUser()?.email,
                            updated_at: new Date().toISOString()
                        };

                        await this.data.update('people', personId, updatedData);

                        this.ui.showDialog(
                            'Person Updated',
                            `Person record for ${formData.first_name} ${formData.last_name} has been updated.`,
                            'success'
                        );

                        // Refresh the detail view
                        await this.viewPersonDetail(personId);
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to update person: ${error.message}`, 'error');
                    }
                });
            }
        } catch (error) {
            console.error('Error editing person:', error);
            this.ui.showDialog('Error', 'Failed to load person for editing', 'error');
        }
    }

    async addPet(personId) {
        try {
            const fields = [
                { name: 'name', label: 'Pet Name', type: 'text', required: true },
                {
                    name: 'species',
                    label: 'Species',
                    type: 'select',
                    options: [
                        { value: '', label: 'Select species...' },
                        { value: 'Dog', label: 'Dog' },
                        { value: 'Cat', label: 'Cat' },
                        { value: 'Bird', label: 'Bird' },
                        { value: 'Rabbit', label: 'Rabbit' },
                        { value: 'Other', label: 'Other' }
                    ]
                },
                { name: 'breed', label: 'Breed', type: 'text' },
                { name: 'age', label: 'Age (years)', type: 'number' },
                { name: 'color', label: 'Color', type: 'text' },
                { name: 'description', label: 'Description', type: 'textarea' },
                { name: 'microchip_number', label: 'Microchip Number', type: 'text' },
                {
                    name: 'vaccination_status',
                    label: 'Vaccination Status',
                    type: 'select',
                    options: [
                        { value: '', label: 'Select status...' },
                        { value: 'Up to date', label: 'Up to date' },
                        { value: 'Overdue', label: 'Overdue' },
                        { value: 'Unknown', label: 'Unknown' }
                    ]
                },
                { name: 'medical_notes', label: 'Medical Notes', type: 'textarea' }
            ];

            this.ui.showForm('Add Pet', fields, async (formData) => {
                try {
                    const petData = {
                        ...formData,
                        person_id: personId,
                        created_by: this.auth.getCurrentUser()?.email,
                        created_at: new Date().toISOString()
                    };

                    await this.data.insert('pets', petData);

                    this.ui.showDialog(
                        'Pet Added',
                        `Pet "${formData.name}" has been added.`,
                        'success'
                    );

                    // Refresh the pets list
                    this.loadPersonPets(personId);
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to add pet: ${error.message}`, 'error');
                }
            });
        } catch (error) {
            console.error('Error adding pet:', error);
            this.ui.showDialog('Error', 'Failed to add pet', 'error');
        }
    }

    async editPet(petId) {
        try {
            const pet = await this.data.get('pets', petId);
            if (!pet) {
                this.ui.showDialog('Error', 'Pet not found', 'error');
                return;
            }

            const fields = [
                { name: 'name', label: 'Pet Name', type: 'text', required: true, value: pet.name },
                {
                    name: 'species',
                    label: 'Species',
                    type: 'select',
                    value: pet.species,
                    options: [
                        { value: '', label: 'Select species...' },
                        { value: 'Dog', label: 'Dog' },
                        { value: 'Cat', label: 'Cat' },
                        { value: 'Bird', label: 'Bird' },
                        { value: 'Rabbit', label: 'Rabbit' },
                        { value: 'Other', label: 'Other' }
                    ]
                },
                { name: 'breed', label: 'Breed', type: 'text', value: pet.breed },
                { name: 'age', label: 'Age (years)', type: 'number', value: pet.age },
                { name: 'color', label: 'Color', type: 'text', value: pet.color },
                { name: 'description', label: 'Description', type: 'textarea', value: pet.description },
                { name: 'microchip_number', label: 'Microchip Number', type: 'text', value: pet.microchip_number },
                {
                    name: 'vaccination_status',
                    label: 'Vaccination Status',
                    type: 'select',
                    value: pet.vaccination_status,
                    options: [
                        { value: '', label: 'Select status...' },
                        { value: 'Up to date', label: 'Up to date' },
                        { value: 'Overdue', label: 'Overdue' },
                        { value: 'Unknown', label: 'Unknown' }
                    ]
                },
                { name: 'medical_notes', label: 'Medical Notes', type: 'textarea', value: pet.medical_notes }
            ];

            this.ui.showForm('Edit Pet', fields, async (formData) => {
                try {
                    const updatedData = {
                        ...formData,
                        updated_by: this.auth.getCurrentUser()?.email,
                        updated_at: new Date().toISOString()
                    };

                    await this.data.update('pets', petId, updatedData);

                    this.ui.showDialog(
                        'Pet Updated',
                        `Pet "${formData.name}" has been updated.`,
                        'success'
                    );

                    // Refresh the pets list
                    this.loadPersonPets(pet.person_id);
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to update pet: ${error.message}`, 'error');
                }
            });
        } catch (error) {
            console.error('Error editing pet:', error);
            this.ui.showDialog('Error', 'Failed to edit pet', 'error');
        }
    }

    async deletePet(petId) {
        try {
            const pet = await this.data.get('pets', petId);
            if (!pet) {
                this.ui.showDialog('Error', 'Pet not found', 'error');
                return;
            }

            this.ui.showConfirmDialog(
                'Delete Pet',
                `Are you sure you want to delete the pet "${pet.name}"? This action cannot be undone.`,
                async () => {
                    try {
                        await this.data.delete('pets', petId);

                        this.ui.showDialog(
                            'Pet Deleted',
                            `Pet "${pet.name}" has been deleted.`,
                            'success'
                        );

                        // Refresh the pets list
                        this.loadPersonPets(pet.person_id);
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to delete pet: ${error.message}`, 'error');
                    }
                }
            );
        } catch (error) {
            console.error('Error deleting pet:', error);
            this.ui.showDialog('Error', 'Failed to delete pet', 'error');
        }
    }

    async addMedicalIssue(personId) {
        try {
            const fields = [
                { name: 'condition_name', label: 'Medical Condition', type: 'text', required: true },
                { name: 'diagnosis_date', label: 'Diagnosis Date', type: 'date' },
                {
                    name: 'severity',
                    label: 'Severity',
                    type: 'select',
                    options: [
                        { value: '', label: 'Select severity...' },
                        { value: 'Mild', label: 'Mild' },
                        { value: 'Moderate', label: 'Moderate' },
                        { value: 'Severe', label: 'Severe' },
                        { value: 'Critical', label: 'Critical' }
                    ]
                },
                {
                    name: 'status',
                    label: 'Status',
                    type: 'select',
                    options: [
                        { value: '', label: 'Select status...' },
                        { value: 'Active', label: 'Active' },
                        { value: 'Managed', label: 'Managed' },
                        { value: 'Resolved', label: 'Resolved' },
                        { value: 'Chronic', label: 'Chronic' }
                    ]
                },
                { name: 'treatment_notes', label: 'Treatment Notes', type: 'textarea' },
                { name: 'medication', label: 'Current Medication', type: 'textarea' },
                { name: 'follow_up_required', label: 'Follow-up Required', type: 'checkbox' },
                { name: 'follow_up_date', label: 'Follow-up Date', type: 'date' },
                { name: 'healthcare_provider', label: 'Healthcare Provider', type: 'text' },
                { name: 'notes', label: 'Additional Notes', type: 'textarea' }
            ];

            this.ui.showForm('Add Medical Issue', fields, async (formData) => {
                try {
                    const medicalData = {
                        ...formData,
                        person_id: personId,
                        created_by: this.auth.getCurrentUser()?.email,
                        created_at: new Date().toISOString()
                    };

                    await this.data.insert('medical_issues', medicalData);

                    this.ui.showDialog(
                        'Medical Issue Added',
                        `Medical condition "${formData.condition_name}" has been added.`,
                        'success'
                    );

                    // Refresh the medical issues list
                    this.loadPersonMedicalIssues(personId);
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to add medical issue: ${error.message}`, 'error');
                }
            });
        } catch (error) {
            console.error('Error adding medical issue:', error);
            this.ui.showDialog('Error', 'Failed to add medical issue', 'error');
        }
    }

    async editMedicalIssue(medicalIssueId) {
        try {
            const medicalIssue = await this.data.get('medical_issues', medicalIssueId);
            if (!medicalIssue) {
                this.ui.showDialog('Error', 'Medical issue not found', 'error');
                return;
            }

            const fields = [
                { name: 'condition_name', label: 'Medical Condition', type: 'text', required: true, value: medicalIssue.condition_name },
                { name: 'diagnosis_date', label: 'Diagnosis Date', type: 'date', value: medicalIssue.diagnosis_date },
                {
                    name: 'severity',
                    label: 'Severity',
                    type: 'select',
                    value: medicalIssue.severity,
                    options: [
                        { value: '', label: 'Select severity...' },
                        { value: 'Mild', label: 'Mild' },
                        { value: 'Moderate', label: 'Moderate' },
                        { value: 'Severe', label: 'Severe' },
                        { value: 'Critical', label: 'Critical' }
                    ]
                },
                {
                    name: 'status',
                    label: 'Status',
                    type: 'select',
                    value: medicalIssue.status,
                    options: [
                        { value: '', label: 'Select status...' },
                        { value: 'Active', label: 'Active' },
                        { value: 'Managed', label: 'Managed' },
                        { value: 'Resolved', label: 'Resolved' },
                        { value: 'Chronic', label: 'Chronic' }
                    ]
                },
                { name: 'treatment_notes', label: 'Treatment Notes', type: 'textarea', value: medicalIssue.treatment_notes },
                { name: 'medication', label: 'Current Medication', type: 'textarea', value: medicalIssue.medication },
                { name: 'follow_up_required', label: 'Follow-up Required', type: 'checkbox', value: medicalIssue.follow_up_required },
                { name: 'follow_up_date', label: 'Follow-up Date', type: 'date', value: medicalIssue.follow_up_date },
                { name: 'healthcare_provider', label: 'Healthcare Provider', type: 'text', value: medicalIssue.healthcare_provider },
                { name: 'notes', label: 'Additional Notes', type: 'textarea', value: medicalIssue.notes }
            ];

            this.ui.showForm('Edit Medical Issue', fields, async (formData) => {
                try {
                    const updatedData = {
                        ...formData,
                        updated_by: this.auth.getCurrentUser()?.email,
                        updated_at: new Date().toISOString()
                    };

                    await this.data.update('medical_issues', medicalIssueId, updatedData);

                    this.ui.showDialog(
                        'Medical Issue Updated',
                        `Medical condition "${formData.condition_name}" has been updated.`,
                        'success'
                    );

                    // Refresh the medical issues list
                    this.loadPersonMedicalIssues(medicalIssue.person_id);
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to update medical issue: ${error.message}`, 'error');
                }
            });
        } catch (error) {
            console.error('Error editing medical issue:', error);
            this.ui.showDialog('Error', 'Failed to edit medical issue', 'error');
        }
    }

    async deleteMedicalIssue(medicalIssueId) {
        try {
            const medicalIssue = await this.data.get('medical_issues', medicalIssueId);
            if (!medicalIssue) {
                this.ui.showDialog('Error', 'Medical issue not found', 'error');
                return;
            }

            this.ui.showConfirmDialog(
                'Delete Medical Issue',
                `Are you sure you want to delete the medical condition "${medicalIssue.condition_name}"? This action cannot be undone.`,
                async () => {
                    try {
                        await this.data.delete('medical_issues', medicalIssueId);

                        this.ui.showDialog(
                            'Medical Issue Deleted',
                            `Medical condition "${medicalIssue.condition_name}" has been deleted.`,
                            'success'
                        );

                        // Refresh the medical issues list
                        this.loadPersonMedicalIssues(medicalIssue.person_id);
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to delete medical issue: ${error.message}`, 'error');
                    }
                }
            );
        } catch (error) {
            console.error('Error deleting medical issue:', error);
            this.ui.showDialog('Error', 'Failed to delete medical issue', 'error');
        }
    }

    async loadPersonMedicalIssues(personId) {
        try {
            const medicalIssues = await this.data.search('medical_issues', { person_id: personId });
            const medicalContainer = document.getElementById('medical-issues-list');

            if (!medicalContainer) {
                console.error('Medical issues container not found');
                return;
            }

            if (!medicalIssues || medicalIssues.length === 0) {
                medicalContainer.innerHTML = '<div class="no-medical-issues">No medical issues recorded for this person.</div>';
                return;
            }

            const medicalHTML = medicalIssues.map(issue => `
                <div class="medical-issue-card" data-medical-issue-id="${issue.id}">
                    <div class="medical-issue-info">
                        <div class="medical-issue-name">${issue.condition_name}</div>
                        <div class="medical-issue-details">
                            ${issue.severity ? `<span class="medical-severity severity-${issue.severity?.toLowerCase()}">${issue.severity}</span>` : ''}
                            ${issue.status ? `<span class="medical-status"> • ${issue.status}</span>` : ''}
                            ${issue.diagnosis_date ? `<span class="medical-date"> • Diagnosed: ${this.formatDate(issue.diagnosis_date)}</span>` : ''}
                        </div>
                        ${issue.treatment_notes ? `<div class="medical-treatment">Treatment: ${issue.treatment_notes}</div>` : ''}
                        ${issue.medication ? `<div class="medical-medication">Medication: ${issue.medication}</div>` : ''}
                        ${issue.healthcare_provider ? `<div class="medical-provider">Provider: ${issue.healthcare_provider}</div>` : ''}
                        ${issue.follow_up_required && issue.follow_up_date ? `<div class="medical-followup">Follow-up: ${this.formatDate(issue.follow_up_date)}</div>` : ''}
                        ${issue.notes ? `<div class="medical-notes">${issue.notes}</div>` : ''}
                    </div>
                    <div class="medical-issue-actions">
                        <button class="action-button edit-button" data-action="edit-medical-issue" data-medical-issue-id="${issue.id}">
                            Edit
                        </button>
                        <button class="action-button delete-button" data-action="delete-medical-issue" data-medical-issue-id="${issue.id}">
                            Delete
                        </button>
                    </div>
                </div>
            `).join('');

            medicalContainer.innerHTML = medicalHTML;

        } catch (error) {
            console.error('Error loading person medical issues:', error);
            const medicalContainer = document.getElementById('medical-issues-list');
            if (medicalContainer) {
                medicalContainer.innerHTML = '<div class="error">Failed to load medical issues</div>';
            }
        }
    }

    async addOrganization() {
        try {
            const fields = [
                { name: 'name', label: 'Organization Name', type: 'text', required: true },
                {
                    name: 'organization_type',
                    label: 'Organization Type',
                    type: 'select',
                    options: [
                        { value: '', label: 'Select type...' },
                        { value: 'Food Bank', label: 'Food Bank' },
                        { value: 'Shelter', label: 'Shelter' },
                        { value: 'Healthcare', label: 'Healthcare' },
                        { value: 'Mental Health', label: 'Mental Health' },
                        { value: 'Addiction Services', label: 'Addiction Services' },
                        { value: 'Government Agency', label: 'Government Agency' },
                        { value: 'Non-Profit', label: 'Non-Profit' },
                        { value: 'Religious Organization', label: 'Religious Organization' },
                        { value: 'Community Center', label: 'Community Center' },
                        { value: 'Other', label: 'Other' }
                    ]
                },
                { name: 'description', label: 'Description', type: 'textarea' },
                { name: 'services_provided', label: 'Services Provided', type: 'textarea' },
                {
                    name: 'services_tags',
                    label: 'Service Categories',
                    type: 'multi-select',
                    options: [
                        { value: 'Food', label: 'Food Services' },
                        { value: 'Shelter', label: 'Shelter/Housing' },
                        { value: 'Healthcare', label: 'Healthcare' },
                        { value: 'Mental Health', label: 'Mental Health' },
                        { value: 'Addiction Treatment', label: 'Addiction Treatment' },
                        { value: 'Job Training', label: 'Job Training' },
                        { value: 'Legal Aid', label: 'Legal Aid' },
                        { value: 'Financial Assistance', label: 'Financial Assistance' },
                        { value: 'Clothing', label: 'Clothing' },
                        { value: 'Transportation', label: 'Transportation' },
                        { value: 'Education', label: 'Education' },
                        { value: 'Childcare', label: 'Childcare' },
                        { value: 'Emergency Services', label: 'Emergency Services' },
                        { value: 'Counseling', label: 'Counseling' },
                        { value: 'Case Management', label: 'Case Management' },
                        { value: 'Other', label: 'Other Services' }
                    ]
                },
                { name: 'address', label: 'Address', type: 'text' },
                { name: 'city', label: 'City', type: 'text' },
                { name: 'province', label: 'Province', type: 'text' },
                { name: 'postal_code', label: 'Postal Code', type: 'text' },
                { name: 'phone', label: 'Phone', type: 'tel' },
                { name: 'email', label: 'Email', type: 'email' },
                { name: 'website', label: 'Website', type: 'url' },
                { name: 'contact_person', label: 'Contact Person', type: 'text' },
                { name: 'contact_title', label: 'Contact Title', type: 'text' },
                { name: 'contact_phone', label: 'Contact Phone', type: 'tel' },
                { name: 'contact_email', label: 'Contact Email', type: 'email' },
                { name: 'operating_hours', label: 'Operating Hours', type: 'textarea' },
                { name: 'availability_notes', label: 'Availability Notes', type: 'textarea' },
                {
                    name: 'partnership_type',
                    label: 'Partnership Type',
                    type: 'select',
                    options: [
                        { value: '', label: 'Select partnership type...' },
                        { value: 'Referral Partner', label: 'Referral Partner' },
                        { value: 'Service Provider', label: 'Service Provider' },
                        { value: 'Emergency Contact', label: 'Emergency Contact' },
                        { value: 'Funding Source', label: 'Funding Source' },
                        { value: 'Collaborative Partner', label: 'Collaborative Partner' },
                        { value: 'Other', label: 'Other' }
                    ]
                },
                { name: 'referral_process', label: 'Referral Process', type: 'textarea' },
                { name: 'special_requirements', label: 'Special Requirements', type: 'textarea' },
                {
                    name: 'status',
                    label: 'Status',
                    type: 'select',
                    options: [
                        { value: 'Active', label: 'Active' },
                        { value: 'Inactive', label: 'Inactive' },
                        { value: 'Temporarily Closed', label: 'Temporarily Closed' }
                    ]
                },
                { name: 'notes', label: 'Additional Notes', type: 'textarea' }
            ];

            this.ui.showForm('Add Organization', fields, async (formData) => {
                try {
                    const organizationData = {
                        ...formData,
                        created_by: this.auth.getCurrentUser()?.email,
                        created_at: new Date().toISOString()
                    };

                    await this.data.insert('organizations', organizationData);

                    this.ui.showDialog(
                        'Organization Added',
                        `Organization "${formData.name}" has been added.`,
                        'success'
                    );

                    // Refresh the organizations list
                    await this.loadTabContent('records', 'organizations-management');
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to add organization: ${error.message}`, 'error');
                }
            });
        } catch (error) {
            console.error('Error adding organization:', error);
            this.ui.showDialog('Error', 'Failed to add organization', 'error');
        }
    }

    async editOrganization(organizationId) {
        try {
            const organization = await this.data.get('organizations', organizationId);
            if (!organization) {
                this.ui.showDialog('Error', 'Organization not found', 'error');
                return;
            }

            // Use the same fields as add but pre-populate with current data
            const fields = [
                { name: 'name', label: 'Organization Name', type: 'text', required: true, value: organization.name },
                {
                    name: 'organization_type',
                    label: 'Organization Type',
                    type: 'select',
                    value: organization.organization_type,
                    options: [
                        { value: '', label: 'Select type...' },
                        { value: 'Food Bank', label: 'Food Bank' },
                        { value: 'Shelter', label: 'Shelter' },
                        { value: 'Healthcare', label: 'Healthcare' },
                        { value: 'Mental Health', label: 'Mental Health' },
                        { value: 'Addiction Services', label: 'Addiction Services' },
                        { value: 'Government Agency', label: 'Government Agency' },
                        { value: 'Non-Profit', label: 'Non-Profit' },
                        { value: 'Religious Organization', label: 'Religious Organization' },
                        { value: 'Community Center', label: 'Community Center' },
                        { value: 'Other', label: 'Other' }
                    ]
                },
                { name: 'description', label: 'Description', type: 'textarea', value: organization.description },
                { name: 'services_provided', label: 'Services Provided', type: 'textarea', value: organization.services_provided },
                {
                    name: 'services_tags',
                    label: 'Service Categories',
                    type: 'multi-select',
                    value: organization.services_tags || [],
                    options: [
                        { value: 'Food', label: 'Food Services' },
                        { value: 'Shelter', label: 'Shelter/Housing' },
                        { value: 'Healthcare', label: 'Healthcare' },
                        { value: 'Mental Health', label: 'Mental Health' },
                        { value: 'Addiction Treatment', label: 'Addiction Treatment' },
                        { value: 'Job Training', label: 'Job Training' },
                        { value: 'Legal Aid', label: 'Legal Aid' },
                        { value: 'Financial Assistance', label: 'Financial Assistance' },
                        { value: 'Clothing', label: 'Clothing' },
                        { value: 'Transportation', label: 'Transportation' },
                        { value: 'Education', label: 'Education' },
                        { value: 'Childcare', label: 'Childcare' },
                        { value: 'Emergency Services', label: 'Emergency Services' },
                        { value: 'Counseling', label: 'Counseling' },
                        { value: 'Case Management', label: 'Case Management' },
                        { value: 'Other', label: 'Other Services' }
                    ]
                },
                { name: 'address', label: 'Address', type: 'text', value: organization.address },
                { name: 'city', label: 'City', type: 'text', value: organization.city },
                { name: 'province', label: 'Province', type: 'text', value: organization.province },
                { name: 'postal_code', label: 'Postal Code', type: 'text', value: organization.postal_code },
                { name: 'phone', label: 'Phone', type: 'tel', value: organization.phone },
                { name: 'email', label: 'Email', type: 'email', value: organization.email },
                { name: 'website', label: 'Website', type: 'url', value: organization.website },
                { name: 'contact_person', label: 'Contact Person', type: 'text', value: organization.contact_person },
                { name: 'contact_title', label: 'Contact Title', type: 'text', value: organization.contact_title },
                { name: 'contact_phone', label: 'Contact Phone', type: 'tel', value: organization.contact_phone },
                { name: 'contact_email', label: 'Contact Email', type: 'email', value: organization.contact_email },
                { name: 'operating_hours', label: 'Operating Hours', type: 'textarea', value: organization.operating_hours },
                { name: 'availability_notes', label: 'Availability Notes', type: 'textarea', value: organization.availability_notes },
                {
                    name: 'partnership_type',
                    label: 'Partnership Type',
                    type: 'select',
                    value: organization.partnership_type,
                    options: [
                        { value: '', label: 'Select partnership type...' },
                        { value: 'Referral Partner', label: 'Referral Partner' },
                        { value: 'Service Provider', label: 'Service Provider' },
                        { value: 'Emergency Contact', label: 'Emergency Contact' },
                        { value: 'Funding Source', label: 'Funding Source' },
                        { value: 'Collaborative Partner', label: 'Collaborative Partner' },
                        { value: 'Other', label: 'Other' }
                    ]
                },
                { name: 'referral_process', label: 'Referral Process', type: 'textarea', value: organization.referral_process },
                { name: 'special_requirements', label: 'Special Requirements', type: 'textarea', value: organization.special_requirements },
                {
                    name: 'status',
                    label: 'Status',
                    type: 'select',
                    value: organization.status,
                    options: [
                        { value: 'Active', label: 'Active' },
                        { value: 'Inactive', label: 'Inactive' },
                        { value: 'Temporarily Closed', label: 'Temporarily Closed' }
                    ]
                },
                { name: 'notes', label: 'Additional Notes', type: 'textarea', value: organization.notes }
            ];

            this.ui.showForm('Edit Organization', fields, async (formData) => {
                try {
                    const updatedData = {
                        ...formData,
                        updated_by: this.auth.getCurrentUser()?.email,
                        updated_at: new Date().toISOString()
                    };

                    await this.data.update('organizations', organizationId, updatedData);

                    this.ui.showDialog(
                        'Organization Updated',
                        `Organization "${formData.name}" has been updated.`,
                        'success'
                    );

                    // Refresh the organizations list
                    await this.loadTabContent('records', 'organizations-management');
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to update organization: ${error.message}`, 'error');
                }
            });
        } catch (error) {
            console.error('Error editing organization:', error);
            this.ui.showDialog('Error', 'Failed to edit organization', 'error');
        }
    }

    async viewOrganizationDetail(organizationId) {
        try {
            const organization = await this.data.get('organizations', organizationId);
            if (!organization) {
                this.ui.showDialog('Error', 'Organization not found', 'error');
                return;
            }

            this.selectedOrganization = organization;

            // For now, just show the edit form as a detail view
            // In the future, this could be expanded to a full detail page like persons
            await this.editOrganization(organizationId);
        } catch (error) {
            console.error('Error viewing organization detail:', error);
            this.ui.showDialog('Error', 'Failed to load organization details', 'error');
        }
    }

    async loadPersonActivities(personId) {
        try {
            // This would load activities related to this person
            // For now, show a placeholder
            const activitiesContainer = document.getElementById('person-activities');
            if (activitiesContainer) {
                activitiesContainer.innerHTML = '<div class="no-activities">No recent activities found for this person.</div>';
            }
        } catch (error) {
            console.error('Error loading person activities:', error);
        }
    }

    setupPersonDetailTabs() {
        const tabs = document.querySelectorAll('.person-detail-tab');
        const contents = document.querySelectorAll('.person-detail-content .tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and contents
                tabs.forEach(t => t.classList.remove('active'));
                contents.forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab
                tab.classList.add('active');

                // Show corresponding content
                const tabName = tab.dataset.tab;
                const content = document.getElementById(`${tabName}-tab`);
                if (content) {
                    content.classList.add('active');

                    // Load pets if pets tab is selected
                    if (tabName === 'pets' && this.selectedPerson) {
                        this.loadPersonPets(this.selectedPerson.id);
                    }

                    // Load medical issues if medical tab is selected
                    if (tabName === 'medical' && this.selectedPerson) {
                        this.loadPersonMedicalIssues(this.selectedPerson.id);
                    }
                }
            });
        });
    }

    async loadPersonPets(personId) {
        try {
            const pets = await this.data.search('pets', { person_id: personId });
            const petsContainer = document.getElementById('pets-list');

            if (!petsContainer) {
                console.error('Pets container not found');
                return;
            }

            if (!pets || pets.length === 0) {
                petsContainer.innerHTML = '<div class="no-pets">No pets registered for this person.</div>';
                return;
            }

            const petsHTML = pets.map(pet => `
                <div class="pet-card" data-pet-id="${pet.id}">
                    <div class="pet-info">
                        <div class="pet-name">${pet.name}</div>
                        <div class="pet-details">
                            <span class="pet-species">${pet.species || 'Unknown species'}</span>
                            ${pet.breed ? `<span class="pet-breed"> • ${pet.breed}</span>` : ''}
                            ${pet.age ? `<span class="pet-age"> • ${pet.age} years old</span>` : ''}
                        </div>
                        ${pet.color ? `<div class="pet-color">Color: ${pet.color}</div>` : ''}
                        ${pet.description ? `<div class="pet-description">${pet.description}</div>` : ''}
                        ${pet.microchip_number ? `<div class="pet-microchip">Microchip: ${pet.microchip_number}</div>` : ''}
                        ${pet.vaccination_status ? `<div class="pet-vaccination">Vaccinations: ${pet.vaccination_status}</div>` : ''}
                    </div>
                    <div class="pet-actions">
                        <button class="action-button edit-button" data-action="edit-pet" data-pet-id="${pet.id}">
                            Edit
                        </button>
                        <button class="action-button delete-button" data-action="delete-pet" data-pet-id="${pet.id}">
                            Delete
                        </button>
                    </div>
                </div>
            `).join('');

            petsContainer.innerHTML = petsHTML;

        } catch (error) {
            console.error('Error loading person pets:', error);
            const petsContainer = document.getElementById('pets-list');
            if (petsContainer) {
                petsContainer.innerHTML = '<div class="error">Failed to load pets</div>';
            }
        }
    }

    setupElectronAPI() {
        // Set up Electron API for IPC communication
        if (typeof require !== 'undefined') {
            const { ipcRenderer } = require('electron');

            window.electronAPI = {
                invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),
                send: (channel, ...args) => ipcRenderer.send(channel, ...args),
                on: (channel, callback) => ipcRenderer.on(channel, callback),
                onUpdateProgress: (callback) => {
                    ipcRenderer.on('update-progress', (event, progress) => {
                        callback(progress);
                    });
                }
            };

            console.log('Electron API set up successfully');
        } else {
            console.warn('Electron not available - running in browser mode');
        }
    }

    async checkForUpdatesOnStartup() {
        try {
            // Wait a bit for the app to fully load
            setTimeout(async () => {
                try {
                    console.log('Checking for updates on startup...');
                    const updateInfo = await this.updateUI.checkForUpdates(false);

                    if (updateInfo.available && !this.updateUI.isVersionSkipped(updateInfo.latestVersion)) {
                        console.log('Update available on startup:', updateInfo);
                        // Show a subtle notification that doesn't interrupt the user
                        this.ui.setStatus(`Update available: v${updateInfo.latestVersion} - Check System tab`, 'success');
                    }
                } catch (error) {
                    console.log('Startup update check failed (non-critical):', error.message);
                }
            }, 5000); // Check after 5 seconds
        } catch (error) {
            console.log('Error setting up startup update check:', error.message);
        }
    }

    async logout() {
        await this.auth.logout();
        this.currentUser = null;
        this.ui.showScreen('login');
        await this.showLogin();
    }

    async initializeDashboard() {
        try {
            // Load weather data
            this.loadWeatherData();

            // Load task queue
            this.loadTaskQueue();

            // Load open incidents
            this.loadOpenIncidents();

            // Load recent activities
            this.loadRecentActivities();

            // Load today's stats
            this.loadTodaysStats();

            // Set up quick action handlers
            this.setupQuickActions();

        } catch (error) {
            console.error('Error initializing dashboard:', error);
        }
    }

    async initializeDispatch() {
        try {
            // Initialize dispatch state
            this.selectedIncident = null;
            this.dispatchUpdateInterval = null;

            // Create sample incidents if no dispatch-ready incidents exist (for testing)
            const existingIncidents = await this.data.search('incidents', {});
            const dispatchIncidents = existingIncidents.filter(incident =>
                incident.incident_type && incident.priority && incident.status
            );

            if (dispatchIncidents.length === 0) {
                console.log('No existing dispatch incidents found, creating sample dispatch incidents...');
                await this.createSampleDispatchIncidents();
            } else {
                console.log(`Found ${dispatchIncidents.length} existing dispatch incidents, skipping sample creation`);
            }

            // Load active incidents
            this.loadDispatchIncidents();

            // Set up keyboard shortcuts
            this.setupDispatchKeyboardShortcuts();

            // Set up event handlers
            this.setupDispatchEventHandlers();

            // Start real-time updates
            this.startDispatchUpdates();

        } catch (error) {
            console.error('Error initializing dispatch:', error);
        }
    }

    async loadDispatchIncidents() {
        const incidentList = document.getElementById('dispatch-incident-list');
        const incidentCount = document.getElementById('dispatch-incident-count');
        if (!incidentList) return;

        try {
            // Show loading state
            incidentList.innerHTML = '<div class="loading">Loading incidents...</div>';

            // Get all incidents
            const incidents = await this.data.search('incidents', {});

            // Filter for dispatch-ready incidents (have dispatch fields and not closed/resolved)
            const activeIncidents = incidents.filter(incident =>
                incident.incident_type && incident.priority && incident.status &&
                incident.status !== 'closed' && incident.status !== 'resolved'
            );

            // Update count
            if (incidentCount) {
                incidentCount.textContent = activeIncidents.length;
            }

            if (activeIncidents.length === 0) {
                incidentList.innerHTML = '<div class="no-incidents">No active incidents</div>';
                return;
            }

            // Sort by priority and time
            activeIncidents.sort((a, b) => {
                const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
                const aPriority = priorityOrder[a.priority] || 2;
                const bPriority = priorityOrder[b.priority] || 2;

                if (aPriority !== bPriority) {
                    return bPriority - aPriority; // High priority first
                }

                // Then by time (newest first)
                return new Date(b.incident_date + ' ' + (b.incident_time || '00:00')) -
                       new Date(a.incident_date + ' ' + (a.incident_time || '00:00'));
            });

            // Generate HTML for incident list with headers
            const incidentsHTML = `
                <div class="incident-list-header">
                    <div class="incident-row header-row">
                        <div class="incident-type">TYPE</div>
                        <div class="incident-priority">PRI</div>
                        <div class="incident-status">STATUS</div>
                        <div class="incident-time">TIME</div>
                    </div>
                </div>
                <div class="incident-list-body">
                    ${activeIncidents.map(incident => `
                        <div class="dispatch-incident-item" data-incident-id="${incident.id}">
                            <div class="incident-row">
                                <div class="incident-type">${incident.incident_type || 'Unknown'}</div>
                                <div class="incident-priority ${(incident.priority || 'medium').toLowerCase()}">${(incident.priority || 'M').charAt(0).toUpperCase()}</div>
                                <div class="incident-status">${incident.status || 'Open'}</div>
                                <div class="incident-time">${this.formatTime(incident.incident_time)}</div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;

            incidentList.innerHTML = incidentsHTML;

            // Add click handlers
            incidentList.querySelectorAll('.dispatch-incident-item').forEach(item => {
                item.addEventListener('click', () => {
                    this.selectIncident(item.dataset.incidentId);
                });
            });

        } catch (error) {
            console.error('Error loading dispatch incidents:', error);
            incidentList.innerHTML = '<div class="error">Failed to load incidents</div>';
        }
    }

    setupDispatchKeyboardShortcuts() {
        // Remove existing listener if it exists
        if (this.dispatchKeyHandler) {
            document.removeEventListener('keydown', this.dispatchKeyHandler);
        }

        this.dispatchKeyHandler = (event) => {
            // Only handle shortcuts when dispatch tab is active
            if (this.currentTab !== 'dispatch') return;
            if (!this.selectedIncident) return;

            switch(event.key) {
                case 'F1':
                    event.preventDefault();
                    this.showEditIncidentDialog();
                    break;
                case 'F2':
                    event.preventDefault();
                    this.showAssignRangerDialog();
                    break;
                case 'F3':
                    event.preventDefault();
                    this.showUpdateStatusDialog();
                    break;
                case 'F4':
                    event.preventDefault();
                    this.showAddNoteDialog();
                    break;
                case 'F8':
                    event.preventDefault();
                    this.showCloseIncidentDialog();
                    break;
            }
        };

        document.addEventListener('keydown', this.dispatchKeyHandler);
    }

    setupDispatchEventHandlers() {
        // Set up refresh button
        const refreshBtn = document.getElementById('dispatch-refresh');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadDispatchIncidents();
            });
        }

        // Listen for data changes to automatically refresh dispatch
        const dataChangeHandler = (event) => {
            const { table, operation } = event.detail;
            if (table === 'incidents' && this.currentTab === 'dispatch') {
                console.log(`🔄 Incident ${operation} detected, refreshing dispatch...`);
                this.loadDispatchIncidents();
            }
        };

        // Remove existing listener if any
        if (this.dispatchDataChangeHandler) {
            window.removeEventListener('dataChange', this.dispatchDataChangeHandler);
        }

        // Add new listener and store reference for cleanup
        this.dispatchDataChangeHandler = dataChangeHandler;
        window.addEventListener('dataChange', dataChangeHandler);
    }

    startDispatchUpdates() {
        // Clear existing interval
        if (this.dispatchUpdateInterval) {
            clearInterval(this.dispatchUpdateInterval);
        }

        // Store current incidents for change detection
        this.lastIncidentSnapshot = null;

        // Start polling for updates every 30 seconds
        this.dispatchUpdateInterval = setInterval(async () => {
            if (this.currentTab === 'dispatch') {
                await this.checkForIncidentUpdates();
            }
        }, 30000);
    }

    async checkForIncidentUpdates() {
        try {
            // Get current incidents
            const incidents = await this.data.search('incidents', {});
            const activeIncidents = incidents.filter(incident =>
                incident.status !== 'closed' && incident.status !== 'resolved'
            );

            // Check for changes if we have a previous snapshot
            if (this.lastIncidentSnapshot) {
                const changes = this.detectIncidentChanges(this.lastIncidentSnapshot, activeIncidents);

                // Handle new incidents
                changes.added.forEach(incident => {
                    this.handleNewIncident(incident);
                });

                // Handle updated incidents
                changes.updated.forEach(incident => {
                    this.handleUpdatedIncident(incident);
                });

                // Handle closed incidents
                changes.removed.forEach(incident => {
                    this.handleClosedIncident(incident);
                });
            }

            // Update snapshot
            this.lastIncidentSnapshot = activeIncidents.map(incident => ({...incident}));

            // Refresh the display
            this.loadDispatchIncidents();

        } catch (error) {
            console.error('Error checking for incident updates:', error);
        }
    }

    detectIncidentChanges(oldIncidents, newIncidents) {
        const oldMap = new Map(oldIncidents.map(i => [i.id, i]));
        const newMap = new Map(newIncidents.map(i => [i.id, i]));

        const added = newIncidents.filter(incident => !oldMap.has(incident.id));
        const removed = oldIncidents.filter(incident => !newMap.has(incident.id));
        const updated = newIncidents.filter(incident => {
            const oldIncident = oldMap.get(incident.id);
            return oldIncident && (
                oldIncident.status !== incident.status ||
                oldIncident.assigned_ranger !== incident.assigned_ranger ||
                oldIncident.priority !== incident.priority ||
                oldIncident.updated_at !== incident.updated_at
            );
        });

        return { added, updated, removed };
    }

    handleNewIncident(incident) {
        console.log('New incident detected:', incident.incident_number);

        // Show desktop notification for high priority incidents
        if (incident.priority === 'high') {
            this.showDesktopNotification(
                'New High-Priority Incident',
                `${incident.incident_number}: ${incident.incident_type} at ${incident.location}`
            );
        }

        // Show toast notification
        this.showToast(`New incident: ${incident.incident_number}`, 'info');
    }

    handleUpdatedIncident(incident) {
        console.log('Incident updated:', incident.incident_number);

        // Show toast for status changes
        this.showToast(`Incident ${incident.incident_number} updated`, 'info');
    }

    handleClosedIncident(incident) {
        console.log('Incident closed:', incident.incident_number);

        // Show toast notification
        this.showToast(`Incident ${incident.incident_number} closed`, 'success');
    }

    async showDesktopNotification(title, body) {
        try {
            // Request permission if not already granted
            if (Notification.permission === 'default') {
                await Notification.requestPermission();
            }

            // Show notification if permission granted
            if (Notification.permission === 'granted') {
                const notification = new Notification(title, {
                    body: body,
                    icon: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="64" height="64" fill="#000000"/>
                            <path d="M32 8L56 48H8L32 8Z" fill="#ff0000"/>
                            <text x="32" y="36" text-anchor="middle" fill="#ffffff" font-family="monospace" font-size="16">!</text>
                        </svg>
                    `),
                    tag: 'dispatch-notification'
                });

                // Auto-close after 10 seconds
                setTimeout(() => {
                    notification.close();
                }, 10000);
            }
        } catch (error) {
            console.error('Error showing desktop notification:', error);
        }
    }



    formatTime(timeString) {
        if (!timeString) return '--:--';
        return timeString.substring(0, 5); // HH:MM format
    }

    // Dispatch action methods
    async showEditIncidentDialog() {
        if (!this.selectedIncident) return;

        try {
            // Get current incident data
            const incident = await this.data.get('incidents', this.selectedIncident);
            if (!incident) {
                this.showToast('Incident not found', 'error');
                return;
            }

            const fields = [
                {
                    name: 'incident_number',
                    type: 'text',
                    label: 'Incident Number',
                    value: incident.incident_number || '',
                    required: true
                },
                {
                    name: 'incident_type',
                    type: 'select',
                    label: 'Incident Type',
                    value: incident.incident_type || '',
                    options: [
                        { value: 'welfare_check', label: 'Welfare Check' },
                        { value: 'medical_emergency', label: 'Medical Emergency' },
                        { value: 'mental_health', label: 'Mental Health Crisis' },
                        { value: 'substance_abuse', label: 'Substance Abuse' },
                        { value: 'domestic_violence', label: 'Domestic Violence' },
                        { value: 'missing_person', label: 'Missing Person' },
                        { value: 'noise_complaint', label: 'Noise Complaint' },
                        { value: 'property_damage', label: 'Property Damage' },
                        { value: 'theft', label: 'Theft' },
                        { value: 'assault', label: 'Assault' },
                        { value: 'other', label: 'Other' }
                    ],
                    required: true
                },
                {
                    name: 'priority',
                    type: 'select',
                    label: 'Priority',
                    value: incident.priority || 'medium',
                    options: [
                        { value: 'low', label: 'Low' },
                        { value: 'medium', label: 'Medium' },
                        { value: 'high', label: 'High' }
                    ],
                    required: true
                },
                {
                    name: 'location',
                    type: 'text',
                    label: 'Location/Address',
                    value: incident.location || '',
                    required: true
                },
                {
                    name: 'coordinates',
                    type: 'text',
                    label: 'Coordinates (lat, lng)',
                    value: incident.coordinates || '',
                    placeholder: '43.9589, -78.1648'
                },
                {
                    name: 'description',
                    type: 'textarea',
                    label: 'Description',
                    value: incident.description || '',
                    required: true
                },
                {
                    name: 'people_involved',
                    type: 'text',
                    label: 'People Involved',
                    value: incident.people_involved || ''
                },
                {
                    name: 'caller_name',
                    type: 'text',
                    label: 'Caller Name',
                    value: incident.caller_name || ''
                },
                {
                    name: 'caller_phone',
                    type: 'text',
                    label: 'Caller Phone',
                    value: incident.caller_phone || ''
                }
            ];

            this.ui.showForm('Edit Incident Details', fields, async (formData) => {
                try {
                    // Update the incident
                    const updateData = {
                        ...formData,
                        updated_at: new Date().toISOString(),
                        updated_by: this.auth.getCurrentUser()?.email || 'System'
                    };

                    await this.data.update('incidents', this.selectedIncident, updateData);

                    // Add to status history
                    await this.addStatusHistoryEntry(this.selectedIncident, 'Incident details updated');

                    this.showToast('Incident details updated successfully', 'success');
                    this.loadDispatchIncidents();
                    this.loadIncidentDetails(this.selectedIncident);
                } catch (error) {
                    console.error('Error updating incident details:', error);
                    this.showToast('Failed to update incident details', 'error');
                }
            });

        } catch (error) {
            console.error('Error loading incident for editing:', error);
            this.showToast('Failed to load incident details', 'error');
        }
    }

    showAssignRangerDialog() {
        if (!this.selectedIncident) return;

        const fields = [
            {
                name: 'assigned_ranger',
                type: 'text',
                label: 'Assign to Ranger',
                placeholder: 'Enter ranger name',
                required: true
            }
        ];

        this.ui.showForm('Assign Ranger', fields, async (formData) => {
            try {
                await this.assignRanger(this.selectedIncident, formData.assigned_ranger);
                this.showToast('Ranger assigned successfully', 'success');
                this.loadDispatchIncidents();
                this.loadIncidentDetails(this.selectedIncident);
            } catch (error) {
                console.error('Error assigning ranger:', error);
                this.showToast('Failed to assign ranger', 'error');
            }
        });
    }

    showUpdateStatusDialog() {
        if (!this.selectedIncident) return;

        const fields = [
            {
                name: 'status',
                type: 'select',
                label: 'New Status',
                options: [
                    { value: 'open', label: 'Open' },
                    { value: 'assigned', label: 'Assigned' },
                    { value: 'en_route', label: 'En Route' },
                    { value: 'on_scene', label: 'On Scene' },
                    { value: 'resolved', label: 'Resolved' },
                    { value: 'closed', label: 'Closed' }
                ],
                required: true
            }
        ];

        this.ui.showForm('Update Status', fields, async (formData) => {
            try {
                await this.updateIncidentStatus(this.selectedIncident, formData.status);
                this.showToast('Status updated successfully', 'success');
                this.loadDispatchIncidents();
                this.loadIncidentDetails(this.selectedIncident);
            } catch (error) {
                console.error('Error updating status:', error);
                this.showToast('Failed to update status', 'error');
            }
        });
    }

    showAddNoteDialog() {
        if (!this.selectedIncident) return;

        const fields = [
            {
                name: 'log_entry',
                type: 'textarea',
                label: 'Log Entry',
                placeholder: 'Enter your note or log entry...',
                required: true
            },
            {
                name: 'entry_type',
                type: 'select',
                label: 'Entry Type',
                options: [
                    { value: 'note', label: 'General Note' },
                    { value: 'status_update', label: 'Status Update' },
                    { value: 'action_taken', label: 'Action Taken' },
                    { value: 'follow_up', label: 'Follow-up Required' },
                    { value: 'contact_made', label: 'Contact Made' },
                    { value: 'other', label: 'Other' }
                ],
                required: true
            }
        ];

        this.ui.showForm('Add Log Entry', fields, async (formData) => {
            try {
                await this.saveLogEntry(this.selectedIncident.id, formData.log_entry, formData.entry_type);
                this.showToast('Log entry added successfully', 'success');
                
                // Refresh the incident details to show the new log entry
                this.selectIncident(this.selectedIncident.id);
            } catch (error) {
                console.error('Error adding log entry:', error);
                this.showToast('Failed to add log entry', 'error');
            }
        });
    }

    showCloseIncidentDialog() {
        if (!this.selectedIncident) return;

        const confirmed = confirm('Are you sure you want to close this incident?');
        if (!confirmed) return;

        this.closeIncident(this.selectedIncident);
    }

    generateOverviewTab(incident) {
        return `
            <div class="incident-overview">
                <div class="detail-row">
                    <span class="detail-label">Incident #:</span>
                    <span class="detail-value">${incident.incident_number || 'N/A'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Date/Time:</span>
                    <span class="detail-value">${this.formatDate(incident.incident_date)} ${incident.incident_time || ''}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Type:</span>
                    <span class="detail-value">${incident.incident_type || 'Not specified'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Priority:</span>
                    <span class="detail-value incident-priority ${(incident.priority || 'medium').toLowerCase()}">${(incident.priority || 'Medium').toUpperCase()}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Status:</span>
                    <span class="detail-value">${incident.status || 'Open'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Location:</span>
                    <span class="detail-value">${incident.location || 'Not specified'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Assigned:</span>
                    <span class="detail-value">${incident.assigned_ranger || 'Unassigned'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Description:</span>
                    <span class="detail-value">${incident.description || 'No description'}</span>
                </div>
                ${incident.people_involved ? `
                    <div class="detail-row">
                        <span class="detail-label">People Involved:</span>
                        <span class="detail-value">${incident.people_involved}</span>
                    </div>
                ` : ''}
                <div class="detail-row">
                    <span class="detail-label">Reported By:</span>
                    <span class="detail-value">${incident.reported_by || 'Unknown'}</span>
                </div>
            </div>
        `;
    }

    generateMapTab(incident) {
        const hasCoordinates = incident && incident.coordinates && incident.coordinates.includes(',');
        const incidentId = incident?.id || 'unknown';
        const location = incident?.location || 'Unknown Location';
        const coordinates = incident?.coordinates || '';

        return `
            <div class="incident-map-container">
                <div class="map-placeholder" id="incident-map-${incidentId}">
                    ${hasCoordinates ? 'Loading map...' : 'No coordinates available for this incident'}
                </div>
            </div>
            <div class="map-controls">
                <button class="directions-btn" onclick="app.getDirections('${location.replace(/'/g, "\\'")}')">
                    Get Directions
                </button>
                ${hasCoordinates ? `
                    <button class="directions-btn" onclick="app.showOnMap('${coordinates}', '${location.replace(/'/g, "\\'")}')">
                        View on Google Maps
                    </button>
                ` : ''}
            </div>
        `;
    }

    async generateLinksTab(incident) {
        // This will be populated dynamically when the tab is loaded
        console.log('Generating links tab for incident:', incident.id);
        return `
            <div class="incident-links">
                <h4>Related Records</h4>
                <div class="links-section">
                    <div class="link-category">
                        <h5>People</h5>
                        <button class="attach-btn" onclick="app.attachPerson('${incident.id}')">+ Attach Person</button>
                        <div class="linked-items" id="linked-people-${incident.id}">
                            <div class="loading">Loading linked people...</div>
                        </div>
                    </div>
                    <div class="link-category">
                        <h5>Vehicles</h5>
                        <button class="attach-btn" onclick="app.attachVehicle('${incident.id}')">+ Attach Vehicle</button>
                        <div class="linked-items" id="linked-vehicles-${incident.id}">
                            <div class="loading">Loading linked vehicles...</div>
                        </div>
                    </div>
                    <div class="link-category">
                        <h5>Addresses</h5>
                        <button class="attach-btn" onclick="app.attachAddress('${incident.id}')">+ Attach Address</button>
                        <div class="linked-items" id="linked-addresses-${incident.id}">
                            <div class="loading">Loading linked addresses...</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateLogTab(incident) {
        // Parse log entries
        let logEntries = [];
        if (incident.log_entries) {
            try {
                logEntries = JSON.parse(incident.log_entries);
            } catch (e) {
                logEntries = [];
            }
        }

        // Generate log entries HTML
        let logEntriesHTML = '';
        
        // Add initial incident creation entry
        logEntriesHTML += `
            <div class="log-entry">
                <div class="log-timestamp">${this.formatDateTime(incident.created_at)}</div>
                <div class="log-content">
                    <span class="log-type incident-created">📋 INCIDENT CREATED</span>
                    Incident reported by ${incident.reported_by || 'Unknown'}
                </div>
            </div>
        `;

        // Add structured log entries
        logEntries.forEach(entry => {
            const entryTypeIcons = {
                'note': '📝',
                'status_update': '🔄',
                'action_taken': '✅',
                'follow_up': '⏰',
                'contact_made': '📞',
                'other': '📌'
            };
            
            const icon = entryTypeIcons[entry.entry_type] || '📝';
            const typeClass = `log-type-${entry.entry_type}`;
            
            logEntriesHTML += `
                <div class="log-entry">
                    <div class="log-timestamp">${this.formatDateTime(entry.timestamp)}</div>
                    <div class="log-content">
                        <span class="log-type ${typeClass}">${icon} ${entry.entry_type.toUpperCase().replace('_', ' ')}</span>
                        <div class="log-user">by ${entry.user}</div>
                        <div class="log-text">${entry.content}</div>
                    </div>
                </div>
            `;
        });

        // Add legacy dispatch notes if they exist and haven't been converted
        if (incident.dispatch_notes && logEntries.length === 0) {
            logEntriesHTML += `
                <div class="log-entry">
                    <div class="log-timestamp">${this.formatDateTime(incident.updated_at)}</div>
                    <div class="log-content">
                        <span class="log-type log-type-note">📝 DISPATCH NOTE</span>
                        <div class="log-text">${incident.dispatch_notes}</div>
                    </div>
                </div>
            `;
        }

        return `
            <div class="incident-log">
                <div class="log-header">
                    <h4>Activity Log</h4>
                    <button class="add-log-btn" onclick="app.addLogEntry('${incident.id}')">+ Add Entry</button>
                </div>
                <div class="log-entries" id="log-entries-${incident.id}">
                    ${logEntriesHTML}
                </div>
            </div>
        `;
    }

    generateStatusTab(incident) {
        return `
            <div class="incident-status">
                <h4>Status Timeline</h4>
                <div class="status-timeline" id="status-timeline-${incident.id}">
                    <div class="status-entry">
                        <div class="status-timestamp">${this.formatDate(incident.created_at)}</div>
                        <div class="status-change">Created → Open</div>
                        <div class="status-user">${incident.reported_by || 'System'}</div>
                    </div>
                    ${incident.status !== 'open' ? `
                        <div class="status-entry">
                            <div class="status-timestamp">${this.formatDate(incident.updated_at)}</div>
                            <div class="status-change">Open → ${incident.status}</div>
                            <div class="status-user">${incident.updated_by || 'System'}</div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    setupDetailTabs() {
        const tabs = document.querySelectorAll('.incident-detail-tab');
        const contents = document.querySelectorAll('.tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and contents
                tabs.forEach(t => t.classList.remove('active'));
                contents.forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab
                tab.classList.add('active');

                // Show corresponding content
                const tabName = tab.dataset.tab;
                const content = document.getElementById(`${tabName}-tab`);
                if (content) {
                    content.classList.add('active');

                    // Load map if map tab is selected
                    if (tabName === 'map' && this.selectedIncident) {
                        this.loadIncidentMap(this.selectedIncident.id);
                    }

                    // Load links if links tab is selected
                    if (tabName === 'links' && this.selectedIncident) {
                        // Add small delay to ensure DOM is ready
                        setTimeout(() => {
                            this.loadIncidentLinks(this.selectedIncident.id);
                        }, 100);
                    }
                }
            });
        });
    }

    // Load incident links dynamically
    async loadIncidentLinks(incidentId) {
        console.log('Loading incident links for incident:', incidentId);

        // Check if containers exist
        const peopleContainer = document.getElementById(`linked-people-${incidentId}`);
        const vehiclesContainer = document.getElementById(`linked-vehicles-${incidentId}`);
        const addressesContainer = document.getElementById(`linked-addresses-${incidentId}`);
        console.log('Container check:', {
            people: !!peopleContainer,
            vehicles: !!vehiclesContainer,
            addresses: !!addressesContainer
        });

        try {
            // Get all links for this incident
            const links = await this.data.search('incident_links', { incident_id: incidentId });
            console.log('Found links:', links);

            // Organize links by type
            const linksByType = {
                person: [],
                vehicle: [],
                address: []
            };

            // Fetch the actual record data for each link
            for (const link of links || []) {
                try {
                    let record = null;

                    switch (link.linked_record_type) {
                        case 'person':
                            record = await this.data.get('people', link.linked_record_id);
                            break;
                        case 'vehicle':
                            record = await this.data.get('license_plates', link.linked_record_id);
                            break;
                        case 'address':
                            record = await this.data.get('addresses', link.linked_record_id);
                            break;
                    }

                    if (record) {
                        linksByType[link.linked_record_type].push({
                            ...link,
                            record: record
                        });
                    }
                } catch (error) {
                    console.warn(`Failed to load linked record ${link.linked_record_id}:`, error);
                }
            }

            // Update the UI for each category
            console.log('Updating UI with links by type:', linksByType);
            this.updateLinkedPeople(incidentId, linksByType.person);
            this.updateLinkedVehicles(incidentId, linksByType.vehicle);
            this.updateLinkedAddresses(incidentId, linksByType.address);

        } catch (error) {
            console.error('Error loading incident links:', error);
            // Show error state
            const linkedPeopleEl = document.getElementById(`linked-people-${incidentId}`);
            const linkedVehiclesEl = document.getElementById(`linked-vehicles-${incidentId}`);
            const linkedAddressesEl = document.getElementById(`linked-addresses-${incidentId}`);

            if (linkedPeopleEl) linkedPeopleEl.innerHTML = '<div class="error">Error loading links</div>';
            if (linkedVehiclesEl) linkedVehiclesEl.innerHTML = '<div class="error">Error loading links</div>';
            if (linkedAddressesEl) linkedAddressesEl.innerHTML = '<div class="error">Error loading links</div>';
        }
    }

    updateLinkedPeople(incidentId, links) {
        console.log('updateLinkedPeople called with:', incidentId, links);
        const container = document.getElementById(`linked-people-${incidentId}`);
        console.log('Found container:', container);
        if (!container) {
            console.warn('Container not found for linked-people-' + incidentId);
            return;
        }

        if (!links || links.length === 0) {
            container.innerHTML = '<div class="no-links">No people linked</div>';
            console.log('Set no people linked message');
        } else {
            container.innerHTML = links.map(link => `
                <div class="linked-item">
                    <strong>${link.record.first_name} ${link.record.last_name}</strong>
                    <span class="link-type">(${link.link_type})</span>
                    ${link.notes ? `<div class="link-notes">${link.notes}</div>` : ''}
                    <button class="remove-link-btn" onclick="app.removeLinkFromIncident('${link.id}', '${incidentId}')" title="Remove link">×</button>
                </div>
            `).join('');
            console.log('Set linked people HTML');
        }
    }

    updateLinkedVehicles(incidentId, links) {
        console.log('updateLinkedVehicles called with:', incidentId, links);
        const container = document.getElementById(`linked-vehicles-${incidentId}`);
        console.log('Found vehicles container:', container);
        if (!container) {
            console.warn('Container not found for linked-vehicles-' + incidentId);
            return;
        }

        if (!links || links.length === 0) {
            container.innerHTML = '<div class="no-links">No vehicles linked</div>';
            console.log('Set no vehicles linked message');
        } else {
            container.innerHTML = links.map(link => `
                <div class="linked-item">
                    <strong>${link.record.license_plate || link.record.plate_number}</strong>
                    <span class="vehicle-details">${link.record.make || 'Unknown'} ${link.record.model || 'Unknown'}</span>
                    <span class="link-type">(${link.link_type})</span>
                    ${link.notes ? `<div class="link-notes">${link.notes}</div>` : ''}
                    <button class="remove-link-btn" onclick="app.removeLinkFromIncident('${link.id}', '${incidentId}')" title="Remove link">×</button>
                </div>
            `).join('');
            console.log('Set linked vehicles HTML');
        }
    }

    updateLinkedAddresses(incidentId, links) {
        console.log('updateLinkedAddresses called with:', incidentId, links);
        const container = document.getElementById(`linked-addresses-${incidentId}`);
        console.log('Found addresses container:', container);
        if (!container) {
            console.warn('Container not found for linked-addresses-' + incidentId);
            return;
        }

        if (!links || links.length === 0) {
            container.innerHTML = '<div class="no-links">No addresses linked</div>';
            console.log('Set no addresses linked message');
        } else {
            container.innerHTML = links.map(link => `
                <div class="linked-item">
                    <strong>${link.record.street_address}</strong>
                    <span class="address-details">${link.record.city || ''} ${link.record.postal_code || ''}</span>
                    <span class="link-type">(${link.link_type})</span>
                    ${link.notes ? `<div class="link-notes">${link.notes}</div>` : ''}
                    <button class="remove-link-btn" onclick="app.removeLinkFromIncident('${link.id}', '${incidentId}')" title="Remove link">×</button>
                </div>
            `).join('');
            console.log('Set linked addresses HTML');
        }
    }

    // Note: IDs are auto-generated by Supabase using BIGSERIAL

    getDirections(location) {
        const encodedLocation = encodeURIComponent(location);
        const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodedLocation}`;
        window.open(mapsUrl, '_blank');
    }

    async attachPerson(incidentId) {
        try {
            // Get all people from the database
            const people = await this.data.search('people', {});

            if (!people || people.length === 0) {
                this.ui.showDialog('No People Found', 'No people records found. Please add people records first.', 'warning');
                return;
            }

            // Create options for the select dropdown
            const personOptions = people.map(person => ({
                value: person.id,
                label: `${person.first_name} ${person.last_name} - ${person.date_of_birth || 'DOB Unknown'}`
            }));

            const fields = [
                {
                    name: 'person_id',
                    type: 'select',
                    label: 'Select Person',
                    options: personOptions,
                    required: true
                },
                {
                    name: 'link_type',
                    type: 'select',
                    label: 'Relationship Type',
                    options: [
                        { value: 'involved', label: 'Involved Party' },
                        { value: 'witness', label: 'Witness' },
                        { value: 'complainant', label: 'Complainant' },
                        { value: 'suspect', label: 'Suspect' },
                        { value: 'victim', label: 'Victim' },
                        { value: 'other', label: 'Other' }
                    ],
                    required: true
                },
                {
                    name: 'notes',
                    type: 'textarea',
                    label: 'Notes (Optional)',
                    placeholder: 'Additional details about this person\'s involvement...'
                }
            ];

            this.ui.showForm('Attach Person to Incident', fields, async (formData) => {
                try {
                    // Create the link record
                    const linkData = {
                        incident_id: incidentId,
                        linked_record_type: 'person',
                        linked_record_id: formData.person_id,
                        link_type: formData.link_type,
                        notes: formData.notes || null,
                        created_at: new Date().toISOString(),
                        created_by: this.auth.getCurrentUser()?.email || 'System'
                    };

                    await this.data.insert('incident_links', linkData);

                    this.showToast('Person linked to incident successfully', 'success');

                    // Refresh the incident links to show the new link
                    this.loadIncidentLinks(incidentId);

                } catch (error) {
                    console.error('Error linking person to incident:', error);
                    this.showToast('Failed to link person to incident', 'error');
                }
            });

        } catch (error) {
            console.error('Error loading people for attachment:', error);
            this.showToast('Failed to load people records', 'error');
        }
    }

    async attachVehicle(incidentId) {
        try {
            // Get all license plates (vehicles) from the database
            const vehicles = await this.data.search('license_plates', {});

            if (!vehicles || vehicles.length === 0) {
                this.ui.showDialog('No Vehicles Found', 'No vehicle records found. Please add vehicle records first.', 'warning');
                return;
            }

            // Create options for the select dropdown
            const vehicleOptions = vehicles.map(vehicle => ({
                value: vehicle.id,
                label: `${vehicle.plate_number} - ${vehicle.make || 'Unknown'} ${vehicle.model || 'Unknown'} (${vehicle.color || 'Unknown Color'})`
            }));

            const fields = [
                {
                    name: 'vehicle_id',
                    type: 'select',
                    label: 'Select Vehicle',
                    options: vehicleOptions,
                    required: true
                },
                {
                    name: 'link_type',
                    type: 'select',
                    label: 'Relationship Type',
                    options: [
                        { value: 'involved', label: 'Involved Vehicle' },
                        { value: 'suspect_vehicle', label: 'Suspect Vehicle' },
                        { value: 'victim_vehicle', label: 'Victim Vehicle' },
                        { value: 'witness_vehicle', label: 'Witness Vehicle' },
                        { value: 'abandoned', label: 'Abandoned Vehicle' },
                        { value: 'other', label: 'Other' }
                    ],
                    required: true
                },
                {
                    name: 'notes',
                    type: 'textarea',
                    label: 'Notes (Optional)',
                    placeholder: 'Additional details about this vehicle\'s involvement...'
                }
            ];

            this.ui.showForm('Attach Vehicle to Incident', fields, async (formData) => {
                try {
                    // Create the link record
                    const linkData = {
                        incident_id: incidentId,
                        linked_record_type: 'vehicle',
                        linked_record_id: formData.vehicle_id,
                        link_type: formData.link_type,
                        notes: formData.notes || null,
                        created_at: new Date().toISOString(),
                        created_by: this.auth.getCurrentUser()?.email || 'System'
                    };

                    await this.data.insert('incident_links', linkData);

                    this.showToast('Vehicle linked to incident successfully', 'success');

                    // Refresh the incident links to show the new link
                    this.loadIncidentLinks(incidentId);

                } catch (error) {
                    console.error('Error linking vehicle to incident:', error);
                    this.showToast('Failed to link vehicle to incident', 'error');
                }
            });

        } catch (error) {
            console.error('Error loading vehicles for attachment:', error);
            this.showToast('Failed to load vehicle records', 'error');
        }
    }

    async attachAddress(incidentId) {
        try {
            // Get all addresses from the database
            const addresses = await this.data.search('addresses', {});

            if (!addresses || addresses.length === 0) {
                this.ui.showDialog('No Addresses Found', 'No address records found. Please add address records first.', 'warning');
                return;
            }

            // Create options for the select dropdown
            const addressOptions = addresses.map(address => ({
                value: address.id,
                label: `${address.street_address}, ${address.city || ''} ${address.postal_code || ''}`.trim()
            }));

            const fields = [
                {
                    name: 'address_id',
                    type: 'select',
                    label: 'Select Address',
                    options: addressOptions,
                    required: true
                },
                {
                    name: 'link_type',
                    type: 'select',
                    label: 'Relationship Type',
                    options: [
                        { value: 'incident_location', label: 'Incident Location' },
                        { value: 'related_location', label: 'Related Location' },
                        { value: 'suspect_address', label: 'Suspect Address' },
                        { value: 'victim_address', label: 'Victim Address' },
                        { value: 'witness_address', label: 'Witness Address' },
                        { value: 'other', label: 'Other' }
                    ],
                    required: true
                },
                {
                    name: 'notes',
                    type: 'textarea',
                    label: 'Notes (Optional)',
                    placeholder: 'Additional details about this address\'s relevance...'
                }
            ];

            this.ui.showForm('Attach Address to Incident', fields, async (formData) => {
                try {
                    // Create the link record
                    const linkData = {
                        incident_id: incidentId,
                        linked_record_type: 'address',
                        linked_record_id: formData.address_id,
                        link_type: formData.link_type,
                        notes: formData.notes || null,
                        created_at: new Date().toISOString(),
                        created_by: this.auth.getCurrentUser()?.email || 'System'
                    };

                    await this.data.insert('incident_links', linkData);

                    this.showToast('Address linked to incident successfully', 'success');

                    // Refresh the incident links to show the new link
                    this.loadIncidentLinks(incidentId);

                } catch (error) {
                    console.error('Error linking address to incident:', error);
                    this.showToast('Failed to link address to incident', 'error');
                }
            });

        } catch (error) {
            console.error('Error loading addresses for attachment:', error);
            this.showToast('Failed to load address records', 'error');
        }
    }

    async removeLinkFromIncident(linkId, incidentId) {
        try {
            // Confirm deletion
            const confirmed = confirm('Are you sure you want to remove this link?');
            if (!confirmed) return;

            // Delete the link
            await this.data.delete('incident_links', linkId);

            this.showToast('Link removed successfully', 'success');

            // Refresh the links display
            this.loadIncidentLinks(incidentId);

        } catch (error) {
            console.error('Error removing link:', error);
            this.showToast('Failed to remove link', 'error');
        }
    }

    addLogEntry(incidentId) {
        const fields = [
            {
                name: 'log_entry',
                type: 'textarea',
                label: 'Log Entry',
                placeholder: 'Enter your note or log entry...',
                required: true
            },
            {
                name: 'entry_type',
                type: 'select',
                label: 'Entry Type',
                options: [
                    { value: 'note', label: 'General Note' },
                    { value: 'status_update', label: 'Status Update' },
                    { value: 'action_taken', label: 'Action Taken' },
                    { value: 'follow_up', label: 'Follow-up Required' },
                    { value: 'contact_made', label: 'Contact Made' },
                    { value: 'other', label: 'Other' }
                ],
                required: true
            }
        ];

        this.ui.showForm('Add Log Entry', fields, async (formData) => {
            try {
                await this.saveLogEntry(incidentId, formData.log_entry, formData.entry_type);
                this.showToast('Log entry added successfully', 'success');
                
                // Refresh the incident details to show the new log entry
                if (this.selectedIncident && this.selectedIncident.id == incidentId) {
                    this.selectIncident(incidentId);
                }
            } catch (error) {
                console.error('Error adding log entry:', error);
                this.showToast('Failed to add log entry', 'error');
            }
        });
    }

    async saveLogEntry(incidentId, logEntry, entryType) {
        try {
            const incident = await this.data.get('incidents', incidentId);
            if (!incident) throw new Error('Incident not found');

            const timestamp = new Date().toISOString();
            const user = this.auth.getCurrentUser()?.email || 'Unknown';
            
            // Create the log entry with timestamp and user info
            const formattedEntry = `[${this.formatDateTime(timestamp)}] [${entryType.toUpperCase()}] ${user}: ${logEntry}`;

            // Get existing log entries
            let logEntries = [];
            if (incident.log_entries) {
                try {
                    logEntries = JSON.parse(incident.log_entries);
                } catch (e) {
                    logEntries = [];
                }
            }

            // Add new log entry
            logEntries.push({
                timestamp: timestamp,
                user: user,
                entry_type: entryType,
                content: logEntry,
                formatted: formattedEntry
            });

            // Also add to dispatch notes for backward compatibility
            const existingNotes = incident.dispatch_notes || '';
            const updatedNotes = existingNotes ? 
                `${existingNotes}\n${formattedEntry}` : 
                formattedEntry;

            // Update the incident with both log entries and dispatch notes in a single operation
            await this.data.update('incidents', incidentId, {
                log_entries: JSON.stringify(logEntries),
                dispatch_notes: updatedNotes,
                updated_at: timestamp,
                updated_by: user
            });

            return true;
        } catch (error) {
            console.error('Error saving log entry:', error);
            throw error;
        }
    }

    async goToDashboard() {
        try {
            // Update current tab first
            this.currentTab = 'dashboard';

            // Update tab selection
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            const dashboardTab = document.querySelector('[data-tab="dashboard"]');
            if (dashboardTab) {
                dashboardTab.classList.add('active');
            } else {
                console.error('Dashboard tab not found');
                return;
            }

            // Navigate to dashboard tab
            await this.loadTabContent('dashboard');

        } catch (error) {
            console.error('Error navigating to dashboard:', error);
            this.ui.setStatus('Error navigating to dashboard', 'error');
        }
    }

    async goToDispatch(incidentId = null) {
        try {
            // Update current tab to incidents (unified system)
            this.currentTab = 'incidents';

            // Update tab selection
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            const incidentsTab = document.querySelector('[data-tab="incidents"]');
            if (incidentsTab) {
                incidentsTab.classList.add('active');
            } else {
                console.error('Incidents tab not found');
                return;
            }

            // Navigate to incidents tab
            await this.loadTabContent('incidents');

            // Switch to dispatch view within incidents
            setTimeout(() => {
                const dispatchViewBtn = document.querySelector('[data-view="dispatch"]');
                if (dispatchViewBtn) {
                    dispatchViewBtn.click();
                }

                // If specific incident ID provided, select it after content loads
                if (incidentId) {
                    setTimeout(() => {
                        this.selectIncident(incidentId);
                    }, 500);
                }
            }, 200);

        } catch (error) {
            console.error('Error navigating to dispatch:', error);
            this.ui.showDialog('Error', 'Failed to navigate to dispatch screen', 'error');
        }
    }

    async createSampleDispatchIncidents() {
        try {
            const sampleIncidents = [
                {
                    incident_number: 'IHARC-25-0001',
                    incident_date: new Date().toISOString().split('T')[0],
                    incident_time: '14:30:00',
                    location: '123 Main Street, Cobourg, ON',
                    narrative: 'Noise complaint from neighbors about loud music',
                    description: 'Noise complaint from neighbors about loud music',
                    incident_type: 'Disturbance',
                    priority: 'medium',
                    status: 'assigned',
                    assigned_ranger: 'Ranger Smith',
                    coordinates: '44.0956,-78.1675',
                    reported_by: '<EMAIL>',
                    dispatch_notes: 'Initial response assigned to Ranger Smith',
                    is_urgent: false,
                    tags: ['noise', 'complaint']
                },
                {
                    incident_number: 'IHARC-25-0002',
                    incident_date: new Date().toISOString().split('T')[0],
                    incident_time: '15:45:00',
                    location: '456 Oak Avenue, Cobourg, ON',
                    narrative: 'Person requiring medical assistance',
                    description: 'Person requiring medical assistance',
                    incident_type: 'Medical Emergency',
                    priority: 'high',
                    status: 'en_route',
                    assigned_ranger: 'Ranger Johnson',
                    coordinates: '44.0975,-78.1650',
                    reported_by: '<EMAIL>',
                    dispatch_notes: 'High priority - medical emergency, EMS notified',
                    is_urgent: true,
                    tags: ['medical', 'emergency']
                },
                {
                    incident_number: 'IHARC-25-0003',
                    incident_date: new Date().toISOString().split('T')[0],
                    incident_time: '16:15:00',
                    location: '789 Pine Street, Cobourg, ON',
                    narrative: 'Requested welfare check on elderly resident',
                    description: 'Requested welfare check on elderly resident',
                    incident_type: 'Welfare Check',
                    priority: 'low',
                    status: 'open',
                    assigned_ranger: null,
                    coordinates: '44.0945,-78.1700',
                    reported_by: '<EMAIL>',
                    dispatch_notes: null,
                    is_urgent: false,
                    tags: ['welfare', 'check']
                }
            ];

            for (const incident of sampleIncidents) {
                await this.data.insert('incidents', incident);
            }

            console.log('Sample dispatch incidents created');
            return true;
        } catch (error) {
            console.error('Error creating sample incidents:', error);
            return false;
        }
    }

    // Dispatch data management methods
    async assignRanger(incidentId, rangerName) {
        try {
            const incident = await this.data.get('incidents', incidentId);
            if (!incident) throw new Error('Incident not found');

            // Update incident with assigned ranger
            await this.data.update('incidents', incidentId, {
                assigned_ranger: rangerName,
                status: incident.status === 'open' ? 'assigned' : incident.status,
                updated_at: new Date().toISOString()
            });

            // Add to status history
            await this.addStatusHistoryEntry(incidentId, `Assigned to ${rangerName}`);

            return true;
        } catch (error) {
            console.error('Error assigning ranger:', error);
            throw error;
        }
    }

    async updateIncidentStatus(incidentId, newStatus) {
        try {
            const incident = await this.data.get('incidents', incidentId);
            if (!incident) throw new Error('Incident not found');

            const updateData = {
                status: newStatus,
                updated_at: new Date().toISOString()
            };

            // If closing, add closed timestamp
            if (newStatus === 'closed') {
                updateData.closed_at = new Date().toISOString();
                updateData.closed_by = this.auth.getCurrentUser()?.email;
            }

            await this.data.update('incidents', incidentId, updateData);

            // Add to status history
            await this.addStatusHistoryEntry(incidentId, `Status changed to ${newStatus}`);

            return true;
        } catch (error) {
            console.error('Error updating status:', error);
            throw error;
        }
    }

    async addDispatchNote(incidentId, note) {
        try {
            const incident = await this.data.get('incidents', incidentId);
            if (!incident) throw new Error('Incident not found');

            // Append to existing dispatch notes
            const existingNotes = incident.dispatch_notes || '';
            const timestamp = new Date().toISOString();
            const user = this.auth.getCurrentUser()?.email || 'Unknown';
            const newNote = `[${timestamp}] ${user}: ${note}`;

            const updatedNotes = existingNotes ?
                `${existingNotes}\n${newNote}` :
                newNote;

            await this.data.update('incidents', incidentId, {
                dispatch_notes: updatedNotes,
                updated_at: timestamp
            });

            return true;
        } catch (error) {
            console.error('Error adding dispatch note:', error);
            throw error;
        }
    }

    async closeIncident(incidentId) {
        try {
            await this.updateIncidentStatus(incidentId, 'closed');
            this.showToast('Incident closed successfully', 'success');
            this.loadDispatchIncidents();

            // Clear selection if this incident was selected
            if (this.selectedIncident === incidentId) {
                this.selectedIncident = null;
                const detailContainer = document.getElementById('dispatch-incident-detail');
                if (detailContainer) {
                    detailContainer.innerHTML = '<div class="no-selection">Select an incident to view details</div>';
                }
            }
        } catch (error) {
            this.showToast('Failed to close incident', 'error');
        }
    }

    async addStatusHistoryEntry(incidentId, entry) {
        try {
            const incident = await this.data.get('incidents', incidentId);
            if (!incident) return;

            const timestamp = new Date().toISOString();
            const user = this.auth.getCurrentUser()?.email || 'System';

            let statusHistory = [];
            if (incident.status_history) {
                try {
                    statusHistory = JSON.parse(incident.status_history);
                } catch (e) {
                    statusHistory = [];
                }
            }

            statusHistory.push({
                timestamp,
                user,
                entry
            });

            await this.data.update('incidents', incidentId, {
                status_history: JSON.stringify(statusHistory)
            });
        } catch (error) {
            console.error('Error adding status history:', error);
        }
    }

    showToast(message, type = 'info') {
        // Remove existing toast
        const existingToast = document.querySelector('.dispatch-toast');
        if (existingToast) {
            existingToast.remove();
        }

        // Create new toast
        const toast = document.createElement('div');
        toast.className = `dispatch-toast ${type}`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    }

    async loadGoogleMapsAPI() {
        // Check if Google Maps API is already loaded
        if (window.google && window.google.maps) {
            console.log('Google Maps API already loaded');
            return true;
        }

        // Check if script is already loading
        if (this.mapsAPILoading) {
            console.log('Google Maps API loading in progress...');
            return this.mapsAPILoading;
        }

        console.log('Loading Google Maps API...');

        // Ensure config is initialized
        if (!this.config) {
            console.error('Config not initialized, creating new instance');
            this.config = new ConfigManager();
        }

        this.mapsAPILoading = new Promise(async (resolve, reject) => {
            const script = document.createElement('script');

            // Get API key from Vault or fallback
            let apiKey;
            try {
                apiKey = await this.config.getGoogleApiKey();
            } catch (error) {
                console.warn('Error accessing Google API key from Vault, using fallback:', error);
                apiKey = this.config.getGoogleApiKeySync();
            }

            // Final fallback
            if (!apiKey) {
                apiKey = 'AIzaSyCcrqh-DxqMdatXKCfKyPTThZdobuZueIk';
            }

            console.log('Google API Key available:', !!apiKey);
            console.log('API Key source:', this.config.vaultManager ? 'Vault' : 'Config');

            if (!apiKey) {
                const error = new Error('Google API key not available');
                console.error('Maps API Error:', error.message);
                reject(error);
                return;
            }

            // Set up timeout
            const timeout = setTimeout(() => {
                console.error('Google Maps API loading timeout');
                this.mapsAPILoading = null;
                if (window.initGoogleMaps) {
                    delete window.initGoogleMaps;
                }
                reject(new Error('Google Maps API loading timeout'));
            }, 10000); // 10 second timeout

            script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initGoogleMaps`;
            script.async = true;
            script.defer = true;

            // Set up global callback
            window.initGoogleMaps = () => {
                console.log('Google Maps API loaded successfully');
                clearTimeout(timeout);
                this.mapsAPILoading = null;
                delete window.initGoogleMaps;
                resolve(true);
            };

            script.onload = () => {
                // Callback will handle resolution
                console.log('Google Maps script loaded');
            };

            script.onerror = (error) => {
                console.error('Failed to load Google Maps script:', error);
                clearTimeout(timeout);
                this.mapsAPILoading = null;
                delete window.initGoogleMaps;
                reject(new Error('Failed to load Google Maps API script'));
            };

            document.head.appendChild(script);
        });

        return this.mapsAPILoading;
    }

    async loadIncidentMap(incidentId) {
        console.log(`Loading map for incident:`, incidentId);
        console.log(`Incident ID type:`, typeof incidentId);

        // Ensure we have a valid incident ID
        if (!incidentId || typeof incidentId === 'object') {
            console.error('Invalid incident ID provided:', incidentId);
            const mapContainer = document.getElementById(`incident-map-${incidentId}`);
            if (mapContainer) {
                mapContainer.innerHTML = '<div class="map-error">Invalid incident ID</div>';
            }
            return;
        }

        try {
            const incident = await this.data.get('incidents', incidentId);
            console.log('Incident data:', incident);

            if (!incident) {
                console.warn('No incident found for ID:', incidentId);
                const mapContainer = document.getElementById(`incident-map-${incidentId}`);
                if (mapContainer) {
                    mapContainer.innerHTML = '<div class="map-error">Incident not found</div>';
                }
                return;
            }

            if (!incident.coordinates || incident.coordinates.trim() === '') {
                console.warn('No coordinates found for incident:', incidentId);
                const mapContainer = document.getElementById(`incident-map-${incidentId}`);
                if (mapContainer) {
                    mapContainer.innerHTML = '<div class="map-error">No coordinates available for this incident</div>';
                }
                return;
            }

            const mapContainer = document.getElementById(`incident-map-${incidentId}`);
            if (!mapContainer) {
                console.warn('Map container not found:', `incident-map-${incidentId}`);
                return;
            }

            console.log('Map container found, loading Google Maps API...');
            mapContainer.innerHTML = '<div class="loading">Loading Google Maps...</div>';

            // Load Google Maps API
            await this.loadGoogleMapsAPI();

            console.log('Google Maps API loaded, parsing coordinates:', incident.coordinates);

            // Parse coordinates
            const [lat, lng] = incident.coordinates.split(',').map(coord => parseFloat(coord.trim()));
            console.log('Parsed coordinates:', { lat, lng });

            if (isNaN(lat) || isNaN(lng)) {
                console.error('Invalid coordinates:', incident.coordinates);
                mapContainer.innerHTML = '<div class="map-error">Invalid coordinates format</div>';
                return;
            }

            console.log('Creating Google Map...');

            // Check if Google Maps API is available
            if (!window.google || !window.google.maps) {
                throw new Error('Google Maps API not loaded properly');
            }

            // Create map
            const map = new google.maps.Map(mapContainer, {
                center: { lat, lng },
                zoom: 15,
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                styles: [
                    {
                        "featureType": "all",
                        "elementType": "all",
                        "stylers": [
                            { "invert_lightness": true },
                            { "saturation": -100 },
                            { "lightness": 33 },
                            { "gamma": 0.5 },
                            { "hue": "#ff0000" }
                        ]
                    }
                ]
            });

            console.log('Map created successfully');

            // Add marker
            console.log('Adding marker to map...');
            const marker = new google.maps.Marker({
                position: { lat, lng },
                map: map,
                title: `Incident: ${incident.incident_number || incident.id || 'Unknown'}`,
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="#ff0000"/>
                            <circle cx="12" cy="9" r="2.5" fill="#ffffff"/>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(24, 24)
                }
            });

            // Add info window
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="color: #000; font-family: monospace; font-size: 12px;">
                        <strong>${incident.incident_number || incident.id || 'Unknown Incident'}</strong><br>
                        ${incident.incident_type || 'Unknown Type'}<br>
                        ${incident.location || 'Unknown Location'}<br>
                        <a href="https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}" target="_blank" style="color: #0066cc;">Get Directions</a>
                    </div>
                `
            });

            marker.addListener('click', () => {
                infoWindow.open(map, marker);
            });

            console.log('Map loaded successfully for incident:', incidentId);

        } catch (error) {
            console.error('Error loading incident map:', error);
            console.error('Error details:', {
                message: error.message,
                stack: error.stack,
                incidentId: incidentId,
                hasGoogleMaps: !!(window.google && window.google.maps)
            });

            const mapContainer = document.getElementById(`incident-map-${incidentId}`);
            if (mapContainer) {
                mapContainer.innerHTML = `
                    <div class="map-error">
                        Failed to load map<br>
                        <small>Error: ${error.message}</small><br>
                        <button class="directions-btn" onclick="app.retryMapLoad('${incidentId}')" style="margin-top: 10px;">
                            Retry
                        </button>
                    </div>
                `;
            } else {
                console.error('Map container not found for error display:', `incident-map-${incidentId}`);
            }
        }
    }

    // Add retry function for map loading
    async retryMapLoad(incidentId) {
        console.log('Retrying map load for incident:', incidentId);
        await this.loadIncidentMap(incidentId);
    }

    showOnMap(coordinates, location) {
        const [lat, lng] = coordinates.split(',').map(coord => parseFloat(coord.trim()));
        const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${lat},${lng}`;
        window.open(mapsUrl, '_blank');
    }

    getDirections(location) {
        if (!location || location === 'Unknown Location') {
            this.ui.showDialog('Error', 'No location available for directions', 'error');
            return;
        }

        const encodedLocation = encodeURIComponent(location);
        const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${encodedLocation}`;
        window.open(directionsUrl, '_blank');
    }

    // Encampments Management Functions
    async loadEncampmentsContent() {
        try {
            const encampments = await this.data.getAll('encampments');

            return `
                <div class="content-section">
                    <div class="section-header">
                        <h2>🏕️ ENCAMPMENTS MANAGEMENT</h2>
                        <div class="header-actions">
                            <button class="primary-button" data-action="add-encampment">
                                <span class="button-icon">➕</span>
                                Add Encampment
                            </button>
                            <button class="secondary-button" data-action="view-encampments-map">
                                <span class="button-icon">🗺️</span>
                                View Map
                            </button>
                        </div>
                    </div>

                    <div class="encampments-list-container">
                        <div class="encampments-count">
                            <span id="encampments-count">${encampments.length}</span> encampments found
                        </div>
                        <div class="encampments-list" id="encampments-list">
                            ${this.renderEncampmentsList(encampments)}
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            console.error('Error loading encampments:', error);
            return `
                <div class="content-section">
                    <div class="section-header">
                        <h2>🏕️ ENCAMPMENTS MANAGEMENT</h2>
                        <div class="header-actions">
                            <button class="primary-button" data-action="add-encampment">
                                <span class="button-icon">➕</span>
                                Add Encampment
                            </button>
                        </div>
                    </div>
                    <div class="error-message">
                        Failed to load encampments: ${error.message}
                    </div>
                </div>
            `;
        }
    }

    renderEncampmentsList(encampments) {
        if (!encampments || encampments.length === 0) {
            return `
                <div class="empty-state">
                    <div class="empty-icon">🏕️</div>
                    <h3>No Encampments Found</h3>
                    <p>Click "Add Encampment" to create your first encampment record.</p>
                </div>
            `;
        }

        return encampments.map(encampment => {
            const statusClass = this.getEncampmentStatusClass(encampment.status);
            const lastVisited = encampment.last_visited ?
                new Date(encampment.last_visited).toLocaleDateString() : 'Never';

            return `
                <div class="encampment-item ${statusClass}" data-encampment-id="${encampment.id}" style="cursor: pointer;">
                    <div class="encampment-header">
                        <div class="encampment-name">${encampment.name}</div>
                        <div class="encampment-status status-${encampment.status}">${encampment.status.toUpperCase()}</div>
                    </div>
                    <div class="encampment-details">
                        <div class="detail-row">
                            <span class="detail-label">📍 Location:</span>
                            <span class="detail-value">${encampment.location}</span>
                        </div>
                        ${encampment.type ? `
                            <div class="detail-row">
                                <span class="detail-label">🏷️ Type:</span>
                                <span class="detail-value">${encampment.type}</span>
                            </div>
                        ` : ''}
                        ${encampment.estimated_population ? `
                            <div class="detail-row">
                                <span class="detail-label">👥 Est. Population:</span>
                                <span class="detail-value">${encampment.estimated_population}</span>
                            </div>
                        ` : ''}
                        <div class="detail-row">
                            <span class="detail-label">🕒 Last Visited:</span>
                            <span class="detail-value">${lastVisited}</span>
                        </div>
                    </div>
                    <div class="encampment-actions">
                        <button class="action-button primary" data-action="view-encampment" data-encampment-id="${encampment.id}">
                            View Details
                        </button>
                        ${encampment.coordinates ? `
                            <button class="action-button secondary" data-action="view-encampment-map" data-encampment-id="${encampment.id}">
                                View on Map
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }).join('');
    }

    getEncampmentStatusClass(status) {
        const statusClasses = {
            'active': 'status-active',
            'inactive': 'status-inactive',
            'cleared': 'status-cleared',
            'monitoring': 'status-monitoring'
        };
        return statusClasses[status] || 'status-unknown';
    }

    async loadAddEncampmentContent() {
        return `
            <div class="content-section">
                <div class="section-header">
                    <h2>ADD NEW ENCAMPMENT</h2>
                    <div class="header-actions">
                        <button class="secondary-button" data-action="back-to-encampments">
                            <span class="button-icon">←</span>
                            Back to Encampments
                        </button>
                    </div>
                </div>

                <div class="form-section">
                    <form id="add-encampment-form" class="encampment-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="encampment-name">Encampment Name *</label>
                                <input type="text" id="encampment-name" name="name" required
                                       placeholder="e.g., Victoria Park Encampment">
                            </div>

                            <div class="form-group">
                                <label for="encampment-status">Status *</label>
                                <select id="encampment-status" name="status" required>
                                    <option value="active">Active</option>
                                    <option value="monitoring">Monitoring</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="cleared">Cleared</option>
                                </select>
                            </div>

                            <div class="form-group full-width">
                                <label for="encampment-location">Location/Address *</label>
                                <input type="text" id="encampment-location" name="location" required
                                       placeholder="e.g., Victoria Park, 123 Main St, Cobourg, ON">
                            </div>

                            <div class="form-group">
                                <label for="encampment-type">Type</label>
                                <select id="encampment-type" name="type">
                                    <option value="">Select type...</option>
                                    <option value="temporary">Temporary</option>
                                    <option value="permanent">Permanent</option>
                                    <option value="seasonal">Seasonal</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="encampment-population">Estimated Population</label>
                                <input type="number" id="encampment-population" name="estimated_population"
                                       placeholder="Number of people" min="0">
                            </div>

                            <div class="form-group full-width">
                                <label for="encampment-coordinates">Coordinates</label>
                                <input type="text" id="encampment-coordinates" name="coordinates" readonly
                                       placeholder="Click on map to set coordinates">
                                <small>Current: <span id="coordinates-display">None set</span></small>
                            </div>

                            <div class="form-group full-width">
                                <label>Location Map</label>
                                <div class="map-widget" id="add-encampment-map" style="width: 100%; height: 300px; border: 1px solid #ccc; cursor: pointer;">
                                    <div class="map-loading">Click to load map and set coordinates</div>
                                </div>
                            </div>

                            <div class="form-group full-width">
                                <label for="encampment-description">Description</label>
                                <textarea id="encampment-description" name="description" rows="3"
                                          placeholder="General description of the encampment..."></textarea>
                            </div>

                            <div class="form-group full-width">
                                <label for="encampment-safety">Safety Concerns</label>
                                <textarea id="encampment-safety" name="safety_concerns" rows="3"
                                          placeholder="Any safety issues or concerns..."></textarea>
                            </div>

                            <div class="form-group full-width">
                                <label for="encampment-services">Services Needed</label>
                                <textarea id="encampment-services" name="services_needed" rows="3"
                                          placeholder="What services are needed..."></textarea>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="secondary-button" data-action="back-to-encampments">
                                Cancel
                            </button>
                            <button type="submit" class="primary-button">
                                Create Encampment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    async loadEncampmentDetailContent(encampmentId) {
        try {
            const encampment = await this.encampmentManager.getEncampmentById(encampmentId);
            if (!encampment) {
                return '<div class="error">Encampment not found</div>';
            }

            this.selectedEncampment = encampment;

            const lastVisited = encampment.last_visited ?
                new Date(encampment.last_visited).toLocaleDateString() : 'Never';
            const createdAt = new Date(encampment.created_at).toLocaleDateString();

            return `
                <div class="content-section">
                    <div class="section-header">
                        <h2>ENCAMPMENT DETAILS</h2>
                        <div class="header-actions">
                            <button class="primary-button" data-action="edit-encampment" data-encampment-id="${encampment.id}">
                                <span class="button-icon">✏️</span>
                                Edit Encampment
                            </button>
                            ${encampment.coordinates ? `
                                <button class="secondary-button" data-action="view-encampment-map" data-encampment-id="${encampment.id}">
                                    <span class="button-icon">🗺️</span>
                                    View on Map
                                </button>
                            ` : ''}
                            <button class="secondary-button" data-action="back-to-encampments">
                                <span class="button-icon">←</span>
                                Back to Encampments
                            </button>
                        </div>
                    </div>

                    <div class="detail-content">
                        <div class="detail-header">
                            <h3>${encampment.name}</h3>
                            <span class="status-badge status-${encampment.status}">${encampment.status.toUpperCase()}</span>
                        </div>

                        <div class="detail-tabs">
                            <div class="tab-nav">
                                <button class="tab-button active" data-tab="overview">Overview</button>
                                <button class="tab-button" data-tab="location">Location</button>
                                <button class="tab-button" data-tab="services">Services</button>
                                <button class="tab-button" data-tab="history">History</button>
                            </div>

                            <div class="tab-content">
                                <div class="tab-pane active" id="overview-tab">
                                    <div class="detail-grid">
                                        <div class="detail-item">
                                            <label>Status:</label>
                                            <span class="status-badge status-${encampment.status}">${encampment.status.toUpperCase()}</span>
                                        </div>
                                        <div class="detail-item">
                                            <label>Type:</label>
                                            <span>${encampment.type || 'Not specified'}</span>
                                        </div>
                                        <div class="detail-item">
                                            <label>Estimated Population:</label>
                                            <span>${encampment.estimated_population || 'Unknown'}</span>
                                        </div>
                                        <div class="detail-item">
                                            <label>Last Visited:</label>
                                            <span>${lastVisited}</span>
                                        </div>
                                        <div class="detail-item">
                                            <label>Created:</label>
                                            <span>${createdAt}</span>
                                        </div>
                                        <div class="detail-item">
                                            <label>Created By:</label>
                                            <span>${encampment.created_by || 'Unknown'}</span>
                                        </div>
                                    </div>

                                    ${encampment.description ? `
                                        <div class="detail-section">
                                            <h4>Description</h4>
                                            <p>${encampment.description}</p>
                                        </div>
                                    ` : ''}
                                </div>

                                <div class="tab-pane" id="location-tab">
                                    <div class="detail-section">
                                        <h4>Location Information</h4>
                                        <div class="detail-grid">
                                            <div class="detail-item">
                                                <label>Address:</label>
                                                <span>${encampment.location}</span>
                                            </div>
                                            <div class="detail-item">
                                                <label>Coordinates:</label>
                                                <span>${this.encampmentManager.formatCoordinates(encampment.coordinates)}</span>
                                            </div>
                                        </div>

                                        ${encampment.coordinates ? `
                                            <div class="location-map">
                                                <div class="map-widget" id="encampment-detail-map-${encampment.id}" style="width: 100%; height: 300px; margin-top: 15px;">
                                                    <div class="map-loading">Loading map...</div>
                                                </div>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>

                                <div class="tab-pane" id="services-tab">
                                    <div class="detail-section">
                                        ${encampment.services_needed ? `
                                            <h4>Services Needed</h4>
                                            <p>${encampment.services_needed}</p>
                                        ` : '<p>No services information recorded.</p>'}

                                        ${encampment.safety_concerns ? `
                                            <h4>Safety Concerns</h4>
                                            <p class="safety-concerns">${encampment.safety_concerns}</p>
                                        ` : ''}
                                    </div>
                                </div>

                                <div class="tab-pane" id="history-tab">
                                    <div class="detail-section">
                                        <h4>Visit History</h4>
                                        <div class="history-actions">
                                            <button class="primary-button" onclick="app.markEncampmentVisited('${encampment.id}')">
                                                Mark as Visited Today
                                            </button>
                                        </div>
                                        <div id="encampment-history">
                                            <p>Visit history tracking will be implemented here.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            console.error('Error loading encampment detail:', error);
            return `<div class="error">Failed to load encampment details: ${error.message}</div>`;
        }
    }

    setupAddEncampmentForm() {
        const form = document.getElementById('add-encampment-form');
        if (!form) return;

        // Set up map widget
        this.setupEncampmentMapWidget('add-encampment-map', 'encampment-coordinates', 'coordinates-display');

        // Handle form submission
        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(form);
            const encampmentData = {};

            for (let [key, value] of formData.entries()) {
                encampmentData[key] = value;
            }

            try {
                // Validate the data
                const validation = this.encampmentManager.validateEncampmentData(encampmentData);
                if (!validation.isValid) {
                    this.ui.showDialog('Validation Error', validation.errors.join('\n'), 'error');
                    return;
                }

                // Create the encampment
                await this.encampmentManager.createEncampment(encampmentData);

                this.ui.showDialog(
                    'Success',
                    `Encampment "${encampmentData.name}" has been created successfully.`,
                    'success'
                );

                // Navigate back to encampments list
                await this.loadTabContent('encampments');
            } catch (error) {
                console.error('Error creating encampment:', error);
                this.ui.showDialog('Error', `Failed to create encampment: ${error.message}`, 'error');
            }
        });
    }

    async setupEncampmentMapWidget(mapId, coordsInputId, displayId) {
        const mapWidget = document.getElementById(mapId);
        const coordsInput = document.getElementById(coordsInputId);
        const display = document.getElementById(displayId);

        if (!mapWidget || !coordsInput) return;

        // Add click handler to load map
        mapWidget.addEventListener('click', async () => {
            if (mapWidget.classList.contains('map-loaded')) return;

            mapWidget.innerHTML = '<div class="map-loading">Loading Google Maps...</div>';

            try {
                // Load Google Maps API
                await this.loadGoogleMapsAPI();

                // Get current coordinates if any
                let initialLat = 43.9589; // Default to Cobourg
                let initialLng = -78.1648;

                if (coordsInput.value) {
                    const coords = coordsInput.value.split(',');
                    if (coords.length === 2) {
                        const lat = parseFloat(coords[0].trim());
                        const lng = parseFloat(coords[1].trim());
                        if (!isNaN(lat) && !isNaN(lng)) {
                            initialLat = lat;
                            initialLng = lng;
                        }
                    }
                }

                // Create map
                const map = new google.maps.Map(mapWidget, {
                    center: { lat: initialLat, lng: initialLng },
                    zoom: 15,
                    mapTypeId: google.maps.MapTypeId.ROADMAP,
                    styles: [
                        {
                            "featureType": "all",
                            "elementType": "all",
                            "stylers": [
                                { "invert_lightness": true },
                                { "saturation": -100 },
                                { "lightness": 33 },
                                { "gamma": 0.5 },
                                { "hue": "#ff0000" }
                            ]
                        }
                    ]
                });

                // Create draggable marker
                const marker = new google.maps.Marker({
                    position: { lat: initialLat, lng: initialLng },
                    map: map,
                    draggable: true,
                    title: 'Drag to set location',
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="#ff6b35"/>
                                <circle cx="12" cy="9" r="2.5" fill="#ffffff"/>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(24, 24)
                    }
                });

                // Update coordinates when marker is moved
                const updateCoordinates = (position) => {
                    const lat = position.lat();
                    const lng = position.lng();
                    const coordString = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;

                    coordsInput.value = coordString;

                    if (display) {
                        display.textContent = coordString;
                    }
                };

                // Add event listeners
                marker.addListener('dragend', () => {
                    updateCoordinates(marker.getPosition());
                });

                map.addListener('click', (event) => {
                    marker.setPosition(event.latLng);
                    updateCoordinates(event.latLng);
                });

                // Update initial coordinates display
                updateCoordinates(marker.getPosition());

                mapWidget.classList.add('map-loaded');

            } catch (error) {
                console.error('Error loading map:', error);
                mapWidget.innerHTML = `<div class="map-error">Failed to load map: ${error.message}</div>`;
            }
        }, { once: true });
    }

    async viewEncampmentDetail(encampmentId) {
        try {
            const encampment = await this.encampmentManager.getEncampmentById(encampmentId);
            if (encampment) {
                this.selectedEncampment = encampment;
                await this.loadTabContent('encampments', 'encampment-detail');

                // Set up tab navigation and load map if needed
                setTimeout(() => {
                    this.setupEncampmentDetailTabs();
                    if (encampment.coordinates) {
                        this.loadEncampmentMap(encampment);
                    }
                }, 100);
            }
        } catch (error) {
            console.error('Error viewing encampment detail:', error);
            this.ui.showDialog('Error', 'Failed to load encampment details', 'error');
        }
    }

    setupEncampmentDetailTabs() {
        const tabs = document.querySelectorAll('.tab-button');
        const panes = document.querySelectorAll('.tab-pane');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and panes
                tabs.forEach(t => t.classList.remove('active'));
                panes.forEach(p => p.classList.remove('active'));

                // Add active class to clicked tab
                tab.classList.add('active');

                // Show corresponding pane
                const tabName = tab.dataset.tab;
                const pane = document.getElementById(`${tabName}-tab`);
                if (pane) {
                    pane.classList.add('active');
                }
            });
        });
    }

    async markEncampmentVisited(encampmentId) {
        try {
            await this.encampmentManager.updateLastVisited(encampmentId);
            this.ui.showDialog('Success', 'Encampment marked as visited today.', 'success');

            // Refresh the detail view
            await this.viewEncampmentDetail(encampmentId);
        } catch (error) {
            console.error('Error marking encampment as visited:', error);
            this.ui.showDialog('Error', 'Failed to update visit record', 'error');
        }
    }

    // Encampment Action Functions (Legacy - keeping for compatibility)
    async addEncampment() {
        const fields = [
            {
                name: 'name',
                type: 'text',
                label: 'Encampment Name',
                required: true,
                placeholder: 'e.g., Victoria Park Encampment'
            },
            {
                name: 'location',
                type: 'text',
                label: 'Location/Address',
                required: true,
                placeholder: 'e.g., Victoria Park, 123 Main St, Cobourg, ON'
            },
            {
                name: 'coordinates',
                type: 'map',
                label: 'Coordinates (lat, lng)',
                placeholder: 'Click map to set coordinates'
            },
            {
                name: 'status',
                type: 'select',
                label: 'Status',
                required: true,
                value: 'active',
                options: [
                    { value: 'active', label: 'Active' },
                    { value: 'monitoring', label: 'Monitoring' },
                    { value: 'inactive', label: 'Inactive' },
                    { value: 'cleared', label: 'Cleared' }
                ]
            },
            {
                name: 'type',
                type: 'select',
                label: 'Type',
                options: [
                    { value: '', label: 'Select type...' },
                    { value: 'temporary', label: 'Temporary' },
                    { value: 'permanent', label: 'Permanent' },
                    { value: 'seasonal', label: 'Seasonal' }
                ]
            },
            {
                name: 'estimated_population',
                type: 'number',
                label: 'Estimated Population',
                placeholder: 'Number of people'
            },
            {
                name: 'description',
                type: 'textarea',
                label: 'Description',
                placeholder: 'General description of the encampment...'
            },
            {
                name: 'safety_concerns',
                type: 'textarea',
                label: 'Safety Concerns',
                placeholder: 'Any safety issues or concerns...'
            },
            {
                name: 'services_needed',
                type: 'textarea',
                label: 'Services Needed',
                placeholder: 'What services are needed...'
            }
        ];

        this.ui.showForm('Add New Encampment', fields, async (formData) => {
            try {
                // Validate the data
                const validation = this.encampmentManager.validateEncampmentData(formData);
                if (!validation.isValid) {
                    this.ui.showDialog('Validation Error', validation.errors.join('\n'), 'error');
                    return;
                }

                // Create the encampment
                await this.encampmentManager.createEncampment(formData);

                this.ui.showDialog(
                    'Success',
                    `Encampment "${formData.name}" has been created successfully.`,
                    'success'
                );

                // Refresh the encampments view
                await this.loadTabContent('encampments');
            } catch (error) {
                console.error('Error creating encampment:', error);
                this.ui.showDialog('Error', `Failed to create encampment: ${error.message}`, 'error');
            }
        });
    }

    async viewEncampmentDetails(encampmentId) {
        try {
            const encampment = await this.encampmentManager.getEncampmentById(encampmentId);
            if (!encampment) {
                this.ui.showDialog('Error', 'Encampment not found', 'error');
                return;
            }

            // Create detailed view content
            const detailsHTML = this.generateEncampmentDetailsHTML(encampment);

            // Show in a dialog or navigate to detail view
            this.ui.showDialog(`Encampment: ${encampment.name}`, detailsHTML, 'info', true);
        } catch (error) {
            console.error('Error viewing encampment details:', error);
            this.ui.showDialog('Error', `Failed to load encampment details: ${error.message}`, 'error');
        }
    }

    generateEncampmentDetailsHTML(encampment) {
        const lastVisited = encampment.last_visited ?
            new Date(encampment.last_visited).toLocaleDateString() : 'Never';
        const createdAt = new Date(encampment.created_at).toLocaleDateString();

        return `
            <div class="encampment-details-view">
                <div class="detail-section">
                    <h3>Basic Information</h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Status:</label>
                            <span class="status-badge status-${encampment.status}">${encampment.status.toUpperCase()}</span>
                        </div>
                        <div class="detail-item">
                            <label>Type:</label>
                            <span>${encampment.type || 'Not specified'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Location:</label>
                            <span>${encampment.location}</span>
                        </div>
                        <div class="detail-item">
                            <label>Coordinates:</label>
                            <span>${this.encampmentManager.formatCoordinates(encampment.coordinates)}</span>
                        </div>
                        <div class="detail-item">
                            <label>Estimated Population:</label>
                            <span>${encampment.estimated_population || 'Unknown'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Last Visited:</label>
                            <span>${lastVisited}</span>
                        </div>
                        <div class="detail-item">
                            <label>Created:</label>
                            <span>${createdAt}</span>
                        </div>
                    </div>
                </div>

                ${encampment.description ? `
                    <div class="detail-section">
                        <h3>Description</h3>
                        <p>${encampment.description}</p>
                    </div>
                ` : ''}

                ${encampment.safety_concerns ? `
                    <div class="detail-section">
                        <h3>Safety Concerns</h3>
                        <p class="safety-concerns">${encampment.safety_concerns}</p>
                    </div>
                ` : ''}

                ${encampment.services_needed ? `
                    <div class="detail-section">
                        <h3>Services Needed</h3>
                        <p>${encampment.services_needed}</p>
                    </div>
                ` : ''}

                <div class="detail-actions">
                    <button onclick="app.editEncampment('${encampment.id}')" class="action-button">Edit Encampment</button>
                    ${encampment.coordinates ? `
                        <button onclick="app.viewEncampmentOnMap('${encampment.id}')" class="action-button">View on Map</button>
                    ` : ''}
                    <button onclick="app.encampmentManager.updateLastVisited('${encampment.id}').then(() => app.viewEncampmentDetails('${encampment.id}'))" class="action-button">Mark as Visited</button>
                </div>
            </div>
        `;
    }

    async editEncampment(encampmentId) {
        try {
            const encampment = await this.encampmentManager.getEncampmentById(encampmentId);
            if (!encampment) {
                this.ui.showDialog('Error', 'Encampment not found', 'error');
                return;
            }

            const fields = [
                {
                    name: 'name',
                    type: 'text',
                    label: 'Encampment Name',
                    required: true,
                    value: encampment.name
                },
                {
                    name: 'location',
                    type: 'text',
                    label: 'Location/Address',
                    required: true,
                    value: encampment.location
                },
                {
                    name: 'coordinates',
                    type: 'map',
                    label: 'Coordinates (lat, lng)',
                    value: encampment.coordinates || '',
                    placeholder: 'Click map to set coordinates'
                },
                {
                    name: 'status',
                    type: 'select',
                    label: 'Status',
                    required: true,
                    value: encampment.status,
                    options: [
                        { value: 'active', label: 'Active' },
                        { value: 'monitoring', label: 'Monitoring' },
                        { value: 'inactive', label: 'Inactive' },
                        { value: 'cleared', label: 'Cleared' }
                    ]
                },
                {
                    name: 'type',
                    type: 'select',
                    label: 'Type',
                    value: encampment.type || '',
                    options: [
                        { value: '', label: 'Select type...' },
                        { value: 'temporary', label: 'Temporary' },
                        { value: 'permanent', label: 'Permanent' },
                        { value: 'seasonal', label: 'Seasonal' }
                    ]
                },
                {
                    name: 'estimated_population',
                    type: 'number',
                    label: 'Estimated Population',
                    value: encampment.estimated_population || ''
                },
                {
                    name: 'description',
                    type: 'textarea',
                    label: 'Description',
                    value: encampment.description || ''
                },
                {
                    name: 'safety_concerns',
                    type: 'textarea',
                    label: 'Safety Concerns',
                    value: encampment.safety_concerns || ''
                },
                {
                    name: 'services_needed',
                    type: 'textarea',
                    label: 'Services Needed',
                    value: encampment.services_needed || ''
                }
            ];

            this.ui.showForm(`Edit Encampment: ${encampment.name}`, fields, async (formData) => {
                try {
                    // Validate the data
                    const validation = this.encampmentManager.validateEncampmentData(formData);
                    if (!validation.isValid) {
                        this.ui.showDialog('Validation Error', validation.errors.join('\n'), 'error');
                        return;
                    }

                    // Update the encampment
                    await this.encampmentManager.updateEncampment(encampmentId, formData);

                    this.ui.showDialog(
                        'Success',
                        `Encampment "${formData.name}" has been updated successfully.`,
                        'success'
                    );

                    // Refresh the encampments view
                    await this.loadTabContent('encampments');
                } catch (error) {
                    console.error('Error updating encampment:', error);
                    this.ui.showDialog('Error', `Failed to update encampment: ${error.message}`, 'error');
                }
            });
        } catch (error) {
            console.error('Error loading encampment for edit:', error);
            this.ui.showDialog('Error', `Failed to load encampment: ${error.message}`, 'error');
        }
    }

    // Encampment Map Functions
    async viewEncampmentOnMap(encampmentId) {
        try {
            const encampment = await this.encampmentManager.getEncampmentById(encampmentId);
            if (!encampment) {
                this.ui.showDialog('Error', 'Encampment not found', 'error');
                return;
            }

            if (!encampment.coordinates) {
                this.ui.showDialog('Error', 'No coordinates available for this encampment', 'error');
                return;
            }

            // Create map dialog
            const mapHTML = `
                <div class="encampment-map-container">
                    <div class="map-placeholder" id="encampment-map-${encampmentId}" style="width: 100%; height: 400px;">
                        Loading map...
                    </div>
                    <div class="map-info">
                        <h4>${encampment.name}</h4>
                        <p><strong>Location:</strong> ${encampment.location}</p>
                        <p><strong>Status:</strong> ${encampment.status.toUpperCase()}</p>
                        <p><strong>Coordinates:</strong> ${this.encampmentManager.formatCoordinates(encampment.coordinates)}</p>
                    </div>
                    <div class="map-controls">
                        <button class="directions-btn" onclick="app.getDirections('${encampment.location.replace(/'/g, "\\'")}')">
                            Get Directions
                        </button>
                        <button class="directions-btn" onclick="app.showOnMap('${encampment.coordinates}', '${encampment.location.replace(/'/g, "\\'")}')">
                            View on Google Maps
                        </button>
                    </div>
                </div>
            `;

            this.ui.showDialog(`Map: ${encampment.name}`, mapHTML, 'info', true);

            // Load the map after dialog is shown
            setTimeout(() => {
                this.loadEncampmentMap(encampment);
            }, 100);

        } catch (error) {
            console.error('Error viewing encampment on map:', error);
            this.ui.showDialog('Error', `Failed to load map: ${error.message}`, 'error');
        }
    }

    async loadEncampmentMap(encampment) {
        try {
            const mapContainer = document.getElementById(`encampment-map-${encampment.id}`);
            if (!mapContainer) {
                console.error('Map container not found:', `encampment-map-${encampment.id}`);
                return;
            }

            if (!encampment.coordinates) {
                mapContainer.innerHTML = '<div class="map-error">No coordinates available</div>';
                return;
            }

            console.log('Loading map for encampment:', encampment.id, encampment.coordinates);
            mapContainer.innerHTML = '<div class="loading">Loading Google Maps...</div>';

            // Load Google Maps API
            await this.loadGoogleMapsAPI();

            // Parse coordinates
            const [lat, lng] = encampment.coordinates.split(',').map(coord => parseFloat(coord.trim()));

            if (isNaN(lat) || isNaN(lng)) {
                console.error('Invalid coordinates:', encampment.coordinates);
                mapContainer.innerHTML = '<div class="map-error">Invalid coordinates format</div>';
                return;
            }

            // Create map
            const map = new google.maps.Map(mapContainer, {
                center: { lat, lng },
                zoom: 16,
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                styles: [
                    {
                        "featureType": "all",
                        "elementType": "all",
                        "stylers": [
                            { "invert_lightness": true },
                            { "saturation": -100 },
                            { "lightness": 33 },
                            { "gamma": 0.5 },
                            { "hue": "#ff0000" }
                        ]
                    }
                ]
            });

            // Create marker with status-based color
            const markerColor = this.getEncampmentMarkerColor(encampment.status);
            const marker = new google.maps.Marker({
                position: { lat, lng },
                map: map,
                title: `Encampment: ${encampment.name}`,
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="${markerColor}"/>
                            <circle cx="12" cy="9" r="2.5" fill="#ffffff"/>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(24, 24)
                }
            });

            // Add info window
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="color: #000; font-family: monospace; font-size: 12px;">
                        <strong>${encampment.name}</strong><br>
                        Status: ${encampment.status.toUpperCase()}<br>
                        ${encampment.type ? `Type: ${encampment.type}<br>` : ''}
                        ${encampment.estimated_population ? `Population: ${encampment.estimated_population}<br>` : ''}
                        Location: ${encampment.location}<br>
                        <a href="https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}" target="_blank" style="color: #0066cc;">Get Directions</a>
                    </div>
                `
            });

            marker.addListener('click', () => {
                infoWindow.open(map, marker);
            });

            console.log('Encampment map loaded successfully:', encampment.id);

        } catch (error) {
            console.error('Error loading encampment map:', error);
            const mapContainer = document.getElementById(`encampment-map-${encampment.id}`);
            if (mapContainer) {
                mapContainer.innerHTML = `
                    <div class="map-error">
                        Failed to load map<br>
                        <small>Error: ${error.message}</small>
                    </div>
                `;
            }
        }
    }

    getEncampmentMarkerColor(status) {
        const colors = {
            'active': '#ff6b35',      // Orange-red for active
            'monitoring': '#f7931e',  // Orange for monitoring
            'inactive': '#6c757d',    // Gray for inactive
            'cleared': '#28a745'      // Green for cleared
        };
        return colors[status] || '#6c757d';
    }

    async viewAllEncampmentsMap() {
        try {
            const encampments = await this.encampmentManager.getAllEncampments();
            const encampmentsWithCoords = encampments.filter(e => e.coordinates);

            if (encampmentsWithCoords.length === 0) {
                this.ui.showDialog('No Map Data', 'No encampments have coordinates set for mapping.', 'info');
                return;
            }

            // Create master map dialog
            const mapHTML = `
                <div class="encampments-master-map-container">
                    <div class="map-placeholder" id="encampments-master-map" style="width: 100%; height: 500px;">
                        Loading map...
                    </div>
                    <div class="map-legend">
                        <h4>Legend</h4>
                        <div class="legend-item">
                            <span class="legend-marker" style="background-color: #ff6b35;"></span>
                            Active (${encampments.filter(e => e.status === 'active').length})
                        </div>
                        <div class="legend-item">
                            <span class="legend-marker" style="background-color: #f7931e;"></span>
                            Monitoring (${encampments.filter(e => e.status === 'monitoring').length})
                        </div>
                        <div class="legend-item">
                            <span class="legend-marker" style="background-color: #6c757d;"></span>
                            Inactive (${encampments.filter(e => e.status === 'inactive').length})
                        </div>
                        <div class="legend-item">
                            <span class="legend-marker" style="background-color: #28a745;"></span>
                            Cleared (${encampments.filter(e => e.status === 'cleared').length})
                        </div>
                    </div>
                </div>
            `;

            this.ui.showDialog('All Encampments Map', mapHTML, 'info', true);

            // Load the map after dialog is shown
            setTimeout(() => {
                this.loadAllEncampmentsMap(encampmentsWithCoords);
            }, 100);

        } catch (error) {
            console.error('Error viewing all encampments map:', error);
            this.ui.showDialog('Error', `Failed to load map: ${error.message}`, 'error');
        }
    }

    async loadAllEncampmentsMap(encampments) {
        try {
            const mapContainer = document.getElementById('encampments-master-map');
            if (!mapContainer) {
                console.error('Master map container not found');
                return;
            }

            mapContainer.innerHTML = '<div class="loading">Loading Google Maps...</div>';

            // Load Google Maps API
            await this.loadGoogleMapsAPI();

            // Calculate center point and bounds
            let bounds = new google.maps.LatLngBounds();
            const validEncampments = [];

            encampments.forEach(encampment => {
                const [lat, lng] = encampment.coordinates.split(',').map(coord => parseFloat(coord.trim()));
                if (!isNaN(lat) && !isNaN(lng)) {
                    validEncampments.push({ ...encampment, lat, lng });
                    bounds.extend({ lat, lng });
                }
            });

            if (validEncampments.length === 0) {
                mapContainer.innerHTML = '<div class="map-error">No valid coordinates found</div>';
                return;
            }

            // Create map
            const map = new google.maps.Map(mapContainer, {
                zoom: 12,
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                styles: [
                    {
                        "featureType": "all",
                        "elementType": "all",
                        "stylers": [
                            { "invert_lightness": true },
                            { "saturation": -100 },
                            { "lightness": 33 },
                            { "gamma": 0.5 },
                            { "hue": "#ff0000" }
                        ]
                    }
                ]
            });

            // Add markers for each encampment
            validEncampments.forEach(encampment => {
                const markerColor = this.getEncampmentMarkerColor(encampment.status);
                const marker = new google.maps.Marker({
                    position: { lat: encampment.lat, lng: encampment.lng },
                    map: map,
                    title: encampment.name,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="${markerColor}"/>
                                <circle cx="12" cy="9" r="2.5" fill="#ffffff"/>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(20, 20)
                    }
                });

                // Add info window
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="color: #000; font-family: monospace; font-size: 11px;">
                            <strong>${encampment.name}</strong><br>
                            Status: ${encampment.status.toUpperCase()}<br>
                            ${encampment.type ? `Type: ${encampment.type}<br>` : ''}
                            ${encampment.estimated_population ? `Population: ${encampment.estimated_population}<br>` : ''}
                            Location: ${encampment.location}<br>
                            <button onclick="app.viewEncampmentDetails('${encampment.id}')" style="margin-top: 5px; font-size: 10px;">View Details</button>
                        </div>
                    `
                });

                marker.addListener('click', () => {
                    infoWindow.open(map, marker);
                });
            });

            // Fit map to show all markers
            map.fitBounds(bounds);

            console.log('Master encampments map loaded successfully');

        } catch (error) {
            console.error('Error loading master encampments map:', error);
            const mapContainer = document.getElementById('encampments-master-map');
            if (mapContainer) {
                mapContainer.innerHTML = `
                    <div class="map-error">
                        Failed to load map<br>
                        <small>Error: ${error.message}</small>
                    </div>
                `;
            }
        }
    }

    // Property Management Functions
    async loadPropertyContent() {
        return `
            <div class="property-container">
                <div class="property-header">
                    <h2>📦 Property Management</h2>
                </div>
                <div class="property-menu">
                    <div class="menu-grid">
                        <div class="menu-item" data-action="list-found-property">
                            <div class="menu-icon">📦</div>
                            <div class="menu-title">List Found Property</div>
                            <div class="menu-desc">View all found/recovered property</div>
                        </div>
                        <div class="menu-item" data-action="list-missing-property">
                            <div class="menu-icon">🔍</div>
                            <div class="menu-title">List Missing Property</div>
                            <div class="menu-desc">View all reported missing/stolen property</div>
                        </div>
                        <div class="menu-item" data-action="log-property-recovery">
                            <div class="menu-icon">✅</div>
                            <div class="menu-title">Log Property Recovery Transaction</div>
                            <div class="menu-desc">Record found property for return</div>
                        </div>
                        <div class="menu-item" data-action="create-missing-report">
                            <div class="menu-icon">📝</div>
                            <div class="menu-title">Create Missing Property Report</div>
                            <div class="menu-desc">Report missing/stolen property</div>
                        </div>
                    </div>
                </div>
                <div class="property-content" id="property-content">
                    <!-- Content will be loaded here based on selection -->
                </div>
            </div>
        `;
    }



    async initializeAdmin() {
        try {
            console.log('Initializing admin interface...');
            
            // Initialize admin module if not already done
            if (!this.adminManager) {
                console.log('Loading AdminManager module...');
                const { AdminManager } = await import('./admin.js');
                this.adminManager = new AdminManager(this.data, this.ui);
                console.log('AdminManager created successfully');
            }

            // Initialize admin interface
            console.log('Calling adminManager.initialize()...');
            await this.adminManager.initialize();
            console.log('Admin interface initialized successfully');

        } catch (error) {
            console.error('Error initializing admin tab:', error);
            
            // Try to show error in the content area
            const contentArea = document.getElementById('content-area');
            if (contentArea) {
                contentArea.innerHTML = `
                    <div class="error">
                        <h3>Error loading admin interface</h3>
                        <p>${error.message}</p>
                        <p>Please check the browser console for more details.</p>
                        <button onclick="location.reload()" class="primary-button">Reload Page</button>
                    </div>
                `;
            }
        }
    }





    formatPropertyStatus(status) {
        const statusMap = {
            'found': 'Found',
            'investigating': 'Investigating',
            'owner_identified': 'Owner Identified',
            'returned': 'Returned',
            'handed_to_police': 'Handed to Police',
            'disposed': 'Disposed'
        };
        return statusMap[status] || status;
    }





    async viewPropertyDetails(propertyId) {
        try {
            const property = await this.propertyManager.getProperty(propertyId);
            const actions = await this.propertyManager.getPropertyActions(propertyId);

            if (!property) {
                this.showToast('Property not found', 'error');
                return;
            }

            const detailsHTML = `
                <div class="property-details">
                    <h3>Property ${property.property_number}</h3>
                    <div class="property-info-grid">
                        <div class="info-section">
                            <h4>Basic Information</h4>
                            <div class="info-row"><strong>Type:</strong> ${property.property_type}</div>
                            <div class="info-row"><strong>Category:</strong> ${property.category}</div>
                            <div class="info-row"><strong>Description:</strong> ${property.description}</div>
                            <div class="info-row"><strong>Brand:</strong> ${property.brand || 'N/A'}</div>
                            <div class="info-row"><strong>Model:</strong> ${property.model || 'N/A'}</div>
                            <div class="info-row"><strong>Serial Number:</strong> ${property.serial_number || 'N/A'}</div>
                            <div class="info-row"><strong>Color:</strong> ${property.color || 'N/A'}</div>
                            <div class="info-row"><strong>Estimated Value:</strong> $${property.estimated_value || '0.00'}</div>
                            <div class="info-row"><strong>Condition:</strong> ${property.condition}</div>
                        </div>
                        <div class="info-section">
                            <h4>Found Information</h4>
                            <div class="info-row"><strong>Location:</strong> ${property.found_location}</div>
                            <div class="info-row"><strong>Date Found:</strong> ${this.formatDate(property.found_date)}</div>
                            <div class="info-row"><strong>Time Found:</strong> ${property.found_time}</div>
                            <div class="info-row"><strong>Found By:</strong> ${property.found_by}</div>
                            <div class="info-row"><strong>Status:</strong> <span class="status-${property.status}">${this.formatPropertyStatus(property.status)}</span></div>
                        </div>
                        ${property.status === 'returned' ? `
                            <div class="info-section">
                                <h4>Return Information</h4>
                                <div class="info-row"><strong>Returned To:</strong> ${property.returned_to}</div>
                                <div class="info-row"><strong>Return Location:</strong> ${property.return_location}</div>
                                <div class="info-row"><strong>Return Date:</strong> ${this.formatDate(property.returned_date)}</div>
                            </div>
                        ` : ''}
                        ${property.status === 'handed_to_police' ? `
                            <div class="info-section">
                                <h4>Police Information</h4>
                                <div class="info-row"><strong>Police Officer:</strong> ${property.police_officer}</div>
                                <div class="info-row"><strong>File Number:</strong> ${property.police_file_number}</div>
                                <div class="info-row"><strong>Date Handed:</strong> ${this.formatDate(property.handed_to_police_date)}</div>
                            </div>
                        ` : ''}
                    </div>
                    ${property.investigation_notes ? `
                        <div class="info-section">
                            <h4>Investigation Notes</h4>
                            <div class="notes-content">${property.investigation_notes}</div>
                        </div>
                    ` : ''}
                    <div class="info-section">
                        <h4>Action History</h4>
                        <div class="action-history">
                            ${actions.map(action => `
                                <div class="action-item">
                                    <div class="action-date">${this.formatDateTime(action.performed_at)}</div>
                                    <div class="action-description">${action.action_description}</div>
                                    <div class="action-by">by ${action.performed_by}</div>
                                    ${action.notes ? `<div class="action-notes">${action.notes}</div>` : ''}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;

            // Create dialog with action buttons
            const dialogHTML = `
                ${detailsHTML}
                <div class="property-actions-panel">
                    <h4>Quick Actions</h4>
                    <div class="action-buttons">
                        <button class="primary-button" onclick="app.updatePropertyStatus('${propertyId}')">Update Status</button>
                        <button class="secondary-button" onclick="app.updateInvestigationNotes('${propertyId}')">Update Investigation</button>
                        ${property.status !== 'returned' ? `<button class="success-button" onclick="app.markPropertyAsReturned('${propertyId}')">Mark as Returned</button>` : ''}
                        ${property.status !== 'handed_to_police' ? `<button class="warning-button" onclick="app.handPropertyToPolice('${propertyId}')">Hand to Police</button>` : ''}
                    </div>
                </div>
            `;

            this.ui.showDialog(`Property Details - ${property.property_number}`, dialogHTML, 'info');

        } catch (error) {
            console.error('Error viewing property details:', error);
            this.showToast('Failed to load property details', 'error');
        }
    }

    async updatePropertyStatus(propertyId) {
        try {
            const property = await this.propertyManager.getProperty(propertyId);

            if (!property) {
                this.showToast('Property not found', 'error');
                return;
            }

            const fields = [
                {
                    name: 'status',
                    type: 'select',
                    label: 'New Status',
                    value: property.status,
                    options: [
                        { value: 'found', label: 'Found' },
                        { value: 'investigating', label: 'Investigating' },
                        { value: 'owner_identified', label: 'Owner Identified' },
                        { value: 'returned', label: 'Returned' },
                        { value: 'handed_to_police', label: 'Handed to Police' },
                        { value: 'disposed', label: 'Disposed' }
                    ],
                    required: true
                },
                {
                    name: 'notes',
                    type: 'textarea',
                    label: 'Notes (Optional)',
                    placeholder: 'Additional notes about this status change...'
                }
            ];

            this.ui.showForm(`Update Status - ${property.property_number}`, fields, async (formData) => {
                try {
                    await this.propertyManager.updateStatus(propertyId, formData.status, formData.notes);
                    this.showToast('Property status updated successfully', 'success');

                    // Property refreshing is now handled by the new menu system

                } catch (error) {
                    console.error('Error updating property status:', error);
                    this.showToast('Failed to update property status', 'error');
                }
            });

        } catch (error) {
            console.error('Error showing update status form:', error);
            this.showToast('Failed to open status update form', 'error');
        }
    }

    async updateInvestigationNotes(propertyId) {
        try {
            const property = await this.propertyManager.getProperty(propertyId);

            if (!property) {
                this.showToast('Property not found', 'error');
                return;
            }

            const fields = [
                {
                    name: 'investigation_notes',
                    type: 'textarea',
                    label: 'Investigation Notes',
                    value: property.investigation_notes || '',
                    placeholder: 'Enter detailed investigation findings...',
                    required: true
                }
            ];

            this.ui.showForm(`Update Investigation - ${property.property_number}`, fields, async (formData) => {
                try {
                    await this.propertyManager.updateInvestigation(propertyId, formData.investigation_notes);
                    this.showToast('Investigation notes updated successfully', 'success');

                    // Property refreshing is now handled by the new menu system

                } catch (error) {
                    console.error('Error updating investigation notes:', error);
                    this.showToast('Failed to update investigation notes', 'error');
                }
            });

        } catch (error) {
            console.error('Error showing investigation update form:', error);
            this.showToast('Failed to open investigation form', 'error');
        }
    }

    async markPropertyAsReturned(propertyId) {
        try {
            const property = await this.propertyManager.getProperty(propertyId);

            if (!property) {
                this.showToast('Property not found', 'error');
                return;
            }

            const now = new Date();
            const fields = [
                {
                    name: 'returned_to',
                    type: 'text',
                    label: 'Returned To',
                    placeholder: 'Name of person/organization receiving property',
                    required: true
                },
                {
                    name: 'return_location',
                    type: 'text',
                    label: 'Return Location',
                    placeholder: 'Address or location where property was returned',
                    required: true
                },
                {
                    name: 'returned_date',
                    type: 'date',
                    label: 'Return Date',
                    value: now.toISOString().split('T')[0],
                    required: true
                },
                {
                    name: 'returned_time',
                    type: 'time',
                    label: 'Return Time',
                    value: now.toTimeString().split(' ')[0].substring(0, 5),
                    required: true
                },
                {
                    name: 'notes',
                    type: 'textarea',
                    label: 'Return Notes',
                    placeholder: 'Additional notes about the return process...'
                }
            ];

            this.ui.showForm(`Mark as Returned - ${property.property_number}`, fields, async (formData) => {
                try {
                    await this.propertyManager.markAsReturned(propertyId, formData);
                    this.showToast('Property marked as returned successfully', 'success');

                    // Property refreshing is now handled by the new menu system

                } catch (error) {
                    console.error('Error marking property as returned:', error);
                    this.showToast('Failed to mark property as returned', 'error');
                }
            });

        } catch (error) {
            console.error('Error showing return form:', error);
            this.showToast('Failed to open return form', 'error');
        }
    }

    async handPropertyToPolice(propertyId) {
        try {
            const property = await this.propertyManager.getProperty(propertyId);

            if (!property) {
                this.showToast('Property not found', 'error');
                return;
            }

            const now = new Date();
            const fields = [
                {
                    name: 'police_officer',
                    type: 'text',
                    label: 'Police Officer Name',
                    placeholder: 'Name of receiving police officer',
                    required: true
                },
                {
                    name: 'police_file_number',
                    type: 'text',
                    label: 'Police File Number',
                    placeholder: 'Police file or case number',
                    required: true
                },
                {
                    name: 'handed_date',
                    type: 'date',
                    label: 'Date Handed to Police',
                    value: now.toISOString().split('T')[0],
                    required: true
                },
                {
                    name: 'notes',
                    type: 'textarea',
                    label: 'Handover Notes',
                    placeholder: 'Additional notes about handing property to police...'
                }
            ];

            this.ui.showForm(`Hand to Police - ${property.property_number}`, fields, async (formData) => {
                try {
                    await this.propertyManager.handToPolice(propertyId, formData);
                    this.showToast('Property handed to police successfully', 'success');

                    // Property refreshing is now handled by the new menu system

                } catch (error) {
                    console.error('Error handing property to police:', error);
                    this.showToast('Failed to hand property to police', 'error');
                }
            });

        } catch (error) {
            console.error('Error showing police handover form:', error);
            this.showToast('Failed to open police handover form', 'error');
        }
    }

    // Test method to verify dispatch functionality
    async testDispatchSystem() {
        try {
            console.log('Testing dispatch system...');

            // Test 1: Create a test incident
            const testIncident = {
                incident_number: 'TEST-25-9999',
                incident_date: new Date().toISOString().split('T')[0],
                incident_time: new Date().toTimeString().split(' ')[0],
                location: 'Test Location, Cobourg, ON',
                incident_type: 'Test Incident',
                description: 'This is a test incident for dispatch system verification',
                priority: 'high',
                status: 'open',
                coordinates: '44.0956,-78.1675',
                reported_by: 'system-test'
            };

            const result = await this.data.insert('incidents', testIncident);
            console.log('Test incident created:', result);

            // Test 2: Navigate to dispatch
            await this.goToDispatch();

            // Test 3: Show success message
            this.showToast('Dispatch system test completed successfully!', 'success');

            return true;
        } catch (error) {
            console.error('Dispatch system test failed:', error);
            this.showToast('Dispatch system test failed', 'error');
            return false;
        }
    }

    // Comprehensive system validation
    async validateDispatchSystem() {
        const results = {
            navigation: false,
            dataLayer: false,
            ui: false,
            maps: false,
            notifications: false,
            shortcuts: false
        };

        try {
            console.log('🔍 Starting dispatch system validation...');

            // Test 1: Navigation
            try {
                const dispatchTab = document.querySelector('[data-tab="dispatch"]');
                results.navigation = !!dispatchTab;
                console.log(`✅ Navigation: ${results.navigation ? 'PASS' : 'FAIL'}`);
            } catch (error) {
                console.error('❌ Navigation test failed:', error);
            }

            // Test 2: Data Layer
            try {
                const incidents = await this.data.search('incidents', {});
                results.dataLayer = Array.isArray(incidents);
                console.log(`✅ Data Layer: ${results.dataLayer ? 'PASS' : 'FAIL'}`);
            } catch (error) {
                console.error('❌ Data layer test failed:', error);
            }

            // Test 3: UI Components
            try {
                await this.goToDispatch();
                const incidentList = document.getElementById('dispatch-incident-list');
                const incidentDetail = document.getElementById('dispatch-incident-detail');
                results.ui = !!(incidentList && incidentDetail);
                console.log(`✅ UI Components: ${results.ui ? 'PASS' : 'FAIL'}`);
            } catch (error) {
                console.error('❌ UI components test failed:', error);
            }

            // Test 4: Google Maps API
            try {
                const apiKey = this.config.get('google.apiKey');
                results.maps = !!apiKey;
                console.log(`✅ Maps API Key: ${results.maps ? 'PASS' : 'FAIL'}`);
            } catch (error) {
                console.error('❌ Maps test failed:', error);
            }

            // Test 5: Notifications
            try {
                results.notifications = typeof this.showToast === 'function' &&
                                      typeof this.showDesktopNotification === 'function';
                console.log(`✅ Notifications: ${results.notifications ? 'PASS' : 'FAIL'}`);
            } catch (error) {
                console.error('❌ Notifications test failed:', error);
            }

            // Test 6: Keyboard Shortcuts
            try {
                results.shortcuts = typeof this.setupDispatchKeyboardShortcuts === 'function';
                console.log(`✅ Shortcuts: ${results.shortcuts ? 'PASS' : 'FAIL'}`);
            } catch (error) {
                console.error('❌ Shortcuts test failed:', error);
            }

            const passCount = Object.values(results).filter(Boolean).length;
            const totalTests = Object.keys(results).length;

            console.log(`\n🎯 Dispatch System Validation Complete: ${passCount}/${totalTests} tests passed`);

            if (passCount === totalTests) {
                this.showToast('✅ All dispatch system tests passed!', 'success');
                return true;
            } else {
                this.showToast(`⚠️ ${totalTests - passCount} dispatch tests failed`, 'warning');
                return false;
            }

        } catch (error) {
            console.error('❌ System validation failed:', error);
            this.showToast('❌ System validation failed', 'error');
            return false;
        }
    }

    async loadWeatherData() {
        const weatherContent = document.getElementById('weather-content');
        if (!weatherContent) return;

        try {
            console.log('Dashboard: Starting weather data load...');
            // Show loading state
            weatherContent.innerHTML = '<div class="loading">Loading weather data...</div>';

            console.log('Dashboard: Calling weather service...');
            // Get current weather data
            const weatherData = await this.weather.getCurrentWeather();
            console.log('Dashboard: Weather data received:', weatherData);
            const formattedData = this.weather.formatWeatherData(weatherData);
            const weatherIcon = this.weather.getWeatherIcon(weatherData.condition);
            const alertSeverity = this.weather.getAlertSeverity(weatherData.alert);

            // Build compact weather display HTML
            let weatherHTML = `
                <div class="weather-display">
                    <div class="weather-main">
                        <span class="weather-icon">${weatherIcon}</span>
                        <span class="weather-temp">${formattedData.temperature}</span>
                        <span class="weather-condition">${formattedData.condition}</span>
                    </div>
                    <div class="weather-details">
                        <div class="weather-detail">
                            <span class="detail-label">Feels:</span>
                            <span class="detail-value">${formattedData.feelsLike}</span>
                        </div>
                        <div class="weather-detail">
                            <span class="detail-label">Wind:</span>
                            <span class="detail-value">${formattedData.wind}</span>
                        </div>
                        <div class="weather-detail">
                            <span class="detail-label">Humid:</span>
                            <span class="detail-value">${formattedData.humidity}</span>
                        </div>
                    </div>
            `;

            // Add weather alert if present (compact version)
            if (formattedData.alert) {
                const alertClass = `weather-alert alert-${alertSeverity}`;
                weatherHTML += `
                    <div class="${alertClass}">
                        ⚠️ ${formattedData.alert}
                    </div>
                `;
            }

            // Close the weather display div
            weatherHTML += `</div>`;

            weatherContent.innerHTML = weatherHTML;

        } catch (error) {
            console.error('Error loading weather data:', error);
            weatherContent.innerHTML = `
                <div class="error">
                    <div>Weather data unavailable</div>
                    <small>Error: ${error.message}</small>
                </div>
            `;
        }
    }

    async loadTaskQueue() {
        const taskContent = document.getElementById('task-queue-content');
        const taskCount = document.getElementById('task-count');
        if (!taskContent) return;

        try {
            // Load assigned incidents/tasks for the current user
            const currentUser = this.auth.getCurrentUser();
            const userEmail = currentUser?.email;

            // Search for incidents assigned to this user
            const incidents = await this.data.search('incidents', {});
            const assignedIncidents = incidents.filter(incident =>
                incident.assigned_to === userEmail &&
                incident.status !== 'closed' &&
                incident.status !== 'resolved'
            );

            if (taskCount) {
                taskCount.textContent = assignedIncidents.length;
            }

            if (assignedIncidents.length === 0) {
                taskContent.innerHTML = '<div class="no-tasks">No pending tasks</div>';
                return;
            }

            const tasksHTML = assignedIncidents.slice(0, 5).map(incident => `
                <div class="task-item" data-incident-id="${incident.id}">
                    <div class="task-header">
                        <span class="task-number">${incident.incident_number}</span>
                        <span class="task-priority priority-${incident.priority || 'medium'}">${(incident.priority || 'medium').toUpperCase()}</span>
                    </div>
                    <div class="task-description">${incident.description}</div>
                    <div class="task-location">📍 ${incident.location}</div>
                    <div class="task-time">⏰ ${this.formatDate(incident.incident_date)}</div>
                </div>
            `).join('');

            taskContent.innerHTML = tasksHTML;

            // Add click handlers for tasks
            taskContent.querySelectorAll('.task-item').forEach(item => {
                item.addEventListener('click', () => {
                    const incidentId = item.dataset.incidentId;
                    // Navigate to incident details
                    this.loadTabContent('incidents');
                    // TODO: Highlight specific incident
                });
            });

        } catch (error) {
            console.error('Error loading task queue:', error);
            taskContent.innerHTML = '<div class="error">Failed to load tasks</div>';
        }
    }

    async loadOpenIncidents() {
        const incidentsContent = document.getElementById('incidents-content');
        const incidentCount = document.getElementById('incident-count');
        if (!incidentsContent) return;

        try {
            const incidents = await this.data.search('incidents', {});
            const openIncidents = incidents.filter(incident =>
                incident.status !== 'closed' && incident.status !== 'resolved'
            );

            if (incidentCount) {
                incidentCount.textContent = openIncidents.length;
            }

            if (openIncidents.length === 0) {
                incidentsContent.innerHTML = '<div class="no-incidents">No active incidents</div>';
                return;
            }

            // Sort by priority for dashboard display
            openIncidents.sort((a, b) => {
                const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
                const aPriority = priorityOrder[a.priority] || 2;
                const bPriority = priorityOrder[b.priority] || 2;
                return bPriority - aPriority; // High priority first
            });

            const incidentsHTML = openIncidents.slice(0, 3).map(incident => `
                <div class="incident-item" data-incident-id="${incident.id}">
                    <div class="incident-header">
                        <span class="incident-number">${incident.incident_number || 'N/A'}</span>
                        <span class="incident-priority priority-${(incident.priority || 'medium').toLowerCase()}">${(incident.priority || 'M').charAt(0).toUpperCase()}</span>
                    </div>
                    <div class="incident-type">${incident.incident_type || 'Unknown'}</div>
                    <div class="incident-assigned">👤 ${incident.assigned_ranger || 'Unassigned'}</div>
                    <div class="incident-location">📍 ${incident.location}</div>
                </div>
            `).join('');

            incidentsContent.innerHTML = incidentsHTML;

            // Add click handlers to navigate to dispatch
            incidentsContent.querySelectorAll('.incident-item').forEach(item => {
                item.addEventListener('click', async () => {
                    const incidentId = item.dataset.incidentId;
                    await this.goToDispatch(incidentId);
                });
            });

        } catch (error) {
            console.error('Error loading open incidents:', error);
            incidentsContent.innerHTML = '<div class="error">Failed to load incidents</div>';
        }
    }

    async loadRecentActivities() {
        const activitiesContent = document.getElementById('activities-content');
        if (!activitiesContent) return;

        try {
            // Load recent activities from all activity tables
            const peopleActivities = await this.data.search('people_activities', {});
            const addressActivities = await this.data.search('address_activities', {});
            const vehicleActivities = await this.data.search('vehicle_activities', {});

            // Combine and sort by date
            const allActivities = [
                ...peopleActivities.map(a => ({ ...a, type: 'people' })),
                ...addressActivities.map(a => ({ ...a, type: 'address' })),
                ...vehicleActivities.map(a => ({ ...a, type: 'vehicle' }))
            ].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

            if (allActivities.length === 0) {
                activitiesContent.innerHTML = '<div class="no-activities">No recent activities</div>';
                return;
            }

            const activitiesHTML = allActivities.slice(0, 5).map(activity => `
                <div class="activity-item">
                    <div class="activity-header">
                        <span class="activity-type">${activity.activity_type.replace('_', ' ').toUpperCase()}</span>
                        <span class="activity-date">${this.formatDate(activity.activity_date)}</span>
                    </div>
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-staff">By: ${activity.staff_member}</div>
                </div>
            `).join('');

            activitiesContent.innerHTML = activitiesHTML;

        } catch (error) {
            console.error('Error loading recent activities:', error);
            activitiesContent.innerHTML = '<div class="error">Failed to load activities</div>';
        }
    }

    async loadTodaysStats() {
        try {
            const today = new Date().toISOString().split('T')[0];

            // Load today's activities
            const peopleActivities = await this.data.search('people_activities', {});
            const todaysActivities = peopleActivities.filter(activity =>
                activity.activity_date === today
            );

            // Calculate stats
            const peopleContacted = todaysActivities.filter(a =>
                ['visit', 'contact', 'welfare_check'].includes(a.activity_type)
            ).length;

            const visitsCompleted = todaysActivities.filter(a =>
                a.activity_type === 'visit'
            ).length;

            const referralsMade = todaysActivities.filter(a =>
                a.activity_type === 'service_referral'
            ).length;

            const followupsDue = peopleActivities.filter(a =>
                a.follow_up_required && a.follow_up_date === today
            ).length;

            // Update the display
            // Load property statistics
            const propertyStats = await this.propertyManager.getPropertyStats();
            const propertyPending = propertyStats.pending || 0;

            const peopleContactedEl = document.getElementById('people-contacted');
            const visitsCompletedEl = document.getElementById('visits-completed');
            const referralsMadeEl = document.getElementById('referrals-made');
            const followupsDueEl = document.getElementById('followups-due');
            const propertyPendingEl = document.getElementById('property-pending');

            if (peopleContactedEl) peopleContactedEl.textContent = peopleContacted;
            if (visitsCompletedEl) visitsCompletedEl.textContent = visitsCompleted;
            if (referralsMadeEl) referralsMadeEl.textContent = referralsMade;
            if (followupsDueEl) followupsDueEl.textContent = followupsDue;
            if (propertyPendingEl) propertyPendingEl.textContent = propertyPending;

        } catch (error) {
            console.error('Error loading today\'s stats:', error);
        }
    }

    setupQuickActions() {
        const actionButtons = document.querySelectorAll('.action-btn');
        actionButtons.forEach(button => {
            button.addEventListener('click', async () => {
                const action = button.dataset.action;
                try {
                    await this.commands.executeCommand(action);
                } catch (error) {
                    console.error('Error executing quick action:', error);
                }
            });
        });
    }

    formatDate(dateString) {
        if (!dateString) return 'Unknown';
        try {
            return new Date(dateString).toLocaleDateString();
        } catch {
            return dateString;
        }
    }

    // New Property Management Page Content Methods
    async loadFoundPropertyListContent() {
        try {
            const properties = await this.propertyManager.getAllProperties({ category: 'found' });

            if (!properties || properties.length === 0) {
                return `
                    <div class="content-section">
                        <div class="section-header">
                            <h2>FOUND PROPERTY</h2>
                            <div class="header-actions">
                                <button class="primary-button" onclick="app.loadTabContent('property', 'log-property-recovery')">
                                    <span class="button-icon">✅</span>
                                    Log Found Property
                                </button>
                                <button class="secondary-button" onclick="app.loadTabContent('property')">
                                    <span class="button-icon">←</span>
                                    Back to Property Menu
                                </button>
                            </div>
                        </div>
                        <div class="no-data">
                            <h3>No found property records</h3>
                            <p>No found property has been logged yet.</p>
                            <button class="primary-button" onclick="app.loadTabContent('property', 'log-property-recovery')">Log Found Property</button>
                        </div>
                    </div>
                `;
            }

            return `
                <div class="content-section">
                    <div class="section-header">
                        <h2>FOUND PROPERTY (${properties.length})</h2>
                        <div class="header-actions">
                            <button class="primary-button" onclick="app.loadTabContent('property', 'log-property-recovery')">
                                <span class="button-icon">✅</span>
                                Log Found Property
                            </button>
                            <button class="secondary-button" onclick="app.loadTabContent('property')">
                                <span class="button-icon">←</span>
                                Back to Property Menu
                            </button>
                        </div>
                    </div>

                    <div class="search-section">
                        <div class="search-bar">
                            <input type="text" id="found-property-search" placeholder="Search found property..." class="search-input" onkeyup="app.searchFoundProperties(this.value)">
                            <button class="search-button" id="search-found-properties-btn">🔍</button>
                        </div>
                    </div>

                    <div class="property-list" id="found-property-list">
                        ${properties.map(property => `
                            <div class="property-card" data-property-id="${property.id}">
                                <div class="property-header">
                                    <div class="property-number">${property.property_number}</div>
                                    <div class="property-status status-${property.status}">${this.formatPropertyStatus(property.status)}</div>
                                </div>
                                <div class="property-details">
                                    <div class="property-description">${property.description}</div>
                                    <div class="property-meta">
                                        <span>Found: ${this.formatDate(property.found_date)}</span>
                                        <span>Location: ${property.found_location}</span>
                                        ${property.brand ? `<span>Brand: ${property.brand}</span>` : ''}
                                    </div>
                                </div>
                                <div class="property-actions">
                                    <button class="action-btn" onclick="app.viewPropertyDetails('${property.id}')" title="View Details">👁️</button>
                                    <button class="action-btn" onclick="app.updatePropertyStatus('${property.id}')" title="Update Status">📝</button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;

        } catch (error) {
            console.error('Error loading found property list:', error);
            return '<div class="error">Error loading found property records</div>';
        }
    }

    // Legacy method for search functionality - now redirects to the page
    async showFoundPropertyList() {
        await this.loadTabContent('property', 'found-property-list');
    }

    async loadMissingPropertyListContent() {
        try {
            // Get missing property from both general property records and bikes
            const missingProperties = await this.propertyManager.getAllProperties({ category: 'stolen' });
            const stolenBikes = await this.bikeManager.getAllBikes({ is_stolen: true });

            const totalMissing = (missingProperties?.length || 0) + (stolenBikes?.length || 0);

            if (totalMissing === 0) {
                return `
                    <div class="content-section">
                        <div class="section-header">
                            <h2>MISSING/STOLEN PROPERTY</h2>
                            <div class="header-actions">
                                <button class="primary-button" onclick="app.loadTabContent('property', 'create-missing-report')">
                                    <span class="button-icon">📝</span>
                                    Report Missing Property
                                </button>
                                <button class="secondary-button" onclick="app.loadTabContent('property')">
                                    <span class="button-icon">←</span>
                                    Back to Property Menu
                                </button>
                            </div>
                        </div>
                        <div class="no-data">
                            <h3>No missing property reports</h3>
                            <p>No missing or stolen property has been reported yet.</p>
                            <button class="primary-button" onclick="app.loadTabContent('property', 'create-missing-report')">Report Missing Property</button>
                        </div>
                    </div>
                `;
            }

            return `
                <div class="content-section">
                    <div class="section-header">
                        <h2>MISSING/STOLEN PROPERTY (${totalMissing})</h2>
                        <div class="header-actions">
                            <button class="primary-button" onclick="app.loadTabContent('property', 'create-missing-report')">
                                <span class="button-icon">📝</span>
                                Report Missing Property
                            </button>
                            <button class="secondary-button" onclick="app.loadTabContent('property')">
                                <span class="button-icon">←</span>
                                Back to Property Menu
                            </button>
                        </div>
                    </div>

                    <div class="search-section">
                        <div class="search-bar">
                            <input type="text" id="missing-property-search" placeholder="Search missing property..." class="search-input" onkeyup="app.searchMissingProperties(this.value)">
                            <button class="search-button" id="search-missing-properties-btn">🔍</button>
                        </div>
                    </div>

                    <div class="missing-property-sections" id="missing-property-list">
                        ${missingProperties && missingProperties.length > 0 ? `
                            <div class="missing-section">
                                <h4>General Property (${missingProperties.length})</h4>
                                <div class="property-list">
                                    ${missingProperties.map(property => `
                                        <div class="property-card missing" data-property-id="${property.id}">
                                            <div class="property-header">
                                                <div class="property-number">${property.property_number}</div>
                                                <div class="property-type">General Property</div>
                                            </div>
                                            <div class="property-details">
                                                <div class="property-description">${property.description}</div>
                                                <div class="property-meta">
                                                    <span>Reported: ${this.formatDate(property.found_date)}</span>
                                                    ${property.brand ? `<span>Brand: ${property.brand}</span>` : ''}
                                                    ${property.serial_number ? `<span>Serial: ${property.serial_number}</span>` : ''}
                                                </div>
                                            </div>
                                            <div class="property-actions">
                                                <button class="action-btn" onclick="app.viewPropertyDetails('${property.id}')" title="View Details">👁️</button>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                        ${stolenBikes && stolenBikes.length > 0 ? `
                            <div class="missing-section">
                                <h4>Stolen Bikes (${stolenBikes.length})</h4>
                                <div class="property-list">
                                    ${stolenBikes.map(bike => `
                                        <div class="property-card missing bike" data-bike-id="${bike.id}">
                                            <div class="property-header">
                                                <div class="property-number">BIKE-${bike.serial_number}</div>
                                                <div class="property-type">Bicycle</div>
                                            </div>
                                            <div class="property-details">
                                                <div class="property-description">${bike.make} ${bike.model} - ${bike.color}</div>
                                                <div class="property-meta">
                                                    <span>Owner: ${bike.owner_name}</span>
                                                    <span>Stolen: ${bike.theft_date ? this.formatDate(bike.theft_date) : 'Unknown'}</span>
                                                    <span>Serial: ${bike.serial_number}</span>
                                                </div>
                                            </div>
                                            <div class="property-actions">
                                                <button class="action-btn" onclick="app.viewBikeDetails('${bike.id}')" title="View Details">👁️</button>
                                                <button class="action-btn" onclick="app.markBikeRecovered('${bike.id}')" title="Mark Recovered">✅</button>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;

        } catch (error) {
            console.error('Error loading missing property list:', error);
            return '<div class="error">Error loading missing property records</div>';
        }
    }

    async showMissingPropertyList() {
        await this.loadTabContent('property', 'missing-property-list');
    }

    async loadLogPropertyRecoveryContent() {
        return `
            <div class="content-section">
                <div class="section-header">
                    <h2>LOG PROPERTY RECOVERY</h2>
                    <div class="header-actions">
                        <button class="secondary-button" onclick="app.loadTabContent('property')">
                            <span class="button-icon">←</span>
                            Back to Property Menu
                        </button>
                    </div>
                </div>

                <div class="form-container">
                    <form id="property-recovery-form" class="property-form">
                        <div class="form-section">
                            <h3>Property Information</h3>

                            <div class="form-group">
                                <label for="property_type">Property Type *</label>
                                <select id="property_type" name="property_type" required>
                                    <option value="">Select property type...</option>
                                    <option value="electronics">Electronics</option>
                                    <option value="clothing">Clothing</option>
                                    <option value="jewelry">Jewelry</option>
                                    <option value="documents">Documents</option>
                                    <option value="tools">Tools</option>
                                    <option value="sporting_goods">Sporting Goods</option>
                                    <option value="bike">Bicycle</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="description">Description *</label>
                                <textarea id="description" name="description" placeholder="Detailed description of the found property..." required></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="brand">Brand (if applicable)</label>
                                    <input type="text" id="brand" name="brand" placeholder="e.g., Apple, Samsung, etc.">
                                </div>
                                <div class="form-group">
                                    <label for="model">Model (if applicable)</label>
                                    <input type="text" id="model" name="model" placeholder="e.g., iPhone 12, etc.">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="serial_number">Serial Number (if visible)</label>
                                    <input type="text" id="serial_number" name="serial_number" placeholder="Serial number or identifying marks">
                                </div>
                                <div class="form-group">
                                    <label for="color">Color</label>
                                    <input type="text" id="color" name="color" placeholder="Primary color(s)">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="estimated_value">Estimated Value ($)</label>
                                    <input type="number" id="estimated_value" name="estimated_value" placeholder="0.00" step="0.01" min="0">
                                </div>
                                <div class="form-group">
                                    <label for="condition">Condition *</label>
                                    <select id="condition" name="condition" required>
                                        <option value="">Select condition...</option>
                                        <option value="excellent">Excellent</option>
                                        <option value="good">Good</option>
                                        <option value="fair">Fair</option>
                                        <option value="poor">Poor</option>
                                        <option value="damaged">Damaged</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>Recovery Details</h3>

                            <div class="form-group">
                                <label for="found_location">Found Location *</label>
                                <input type="text" id="found_location" name="found_location" placeholder="Where was this property found?" required>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="found_date">Found Date *</label>
                                    <input type="date" id="found_date" name="found_date" value="${new Date().toISOString().split('T')[0]}" required>
                                </div>
                                <div class="form-group">
                                    <label for="found_time">Found Time *</label>
                                    <input type="time" id="found_time" name="found_time" value="${new Date().toTimeString().split(' ')[0].substring(0, 5)}" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="secondary-button" onclick="app.loadTabContent('property')">Cancel</button>
                            <button type="submit" class="primary-button">Log Property Recovery</button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    async showLogPropertyRecoveryForm() {
        await this.loadTabContent('property', 'log-property-recovery');

        // Set up form handler after page loads
        setTimeout(() => {
            const form = document.getElementById('property-recovery-form');
            if (form) {
                form.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.handlePropertyRecoverySubmit(form);
                });
            }
        }, 100);
    }

    async handlePropertyRecoverySubmit(form) {
        try {
            const formData = new FormData(form);
            const data = {};

            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }

            // Set category as 'found' for recovery transactions
            data.category = 'found';

            await this.propertyManager.logProperty(data);
            this.showToast('Property recovery logged successfully', 'success');

            // Navigate back to found property list
            await this.loadTabContent('property', 'found-property-list');

        } catch (error) {
            console.error('Error logging property recovery:', error);
            this.showToast('Failed to log property recovery', 'error');
        }
    }

    setupPropertyRecoveryForm() {
        setTimeout(() => {
            const form = document.getElementById('property-recovery-form');
            if (form) {
                form.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.handlePropertyRecoverySubmit(form);
                });
            }
        }, 100);
    }

    setupMissingGeneralForm() {
        setTimeout(() => {
            const form = document.getElementById('missing-general-form');
            if (form) {
                form.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.handleMissingGeneralSubmit(form);
                });
            }
        }, 100);
    }

    setupMissingBikeForm() {
        setTimeout(() => {
            const form = document.getElementById('missing-bike-form');
            if (form) {
                form.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.handleMissingBikeSubmit(form);
                });
            }
        }, 100);
    }

    async handleMissingGeneralSubmit(form) {
        try {
            const formData = new FormData(form);
            const data = {};

            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }

            // Set category as 'stolen' for missing property reports
            data.category = 'stolen';
            data.status = 'investigating';

            await this.propertyManager.logProperty(data);
            this.showToast('Missing property report created successfully', 'success');

            // Navigate back to missing property list
            await this.loadTabContent('property', 'missing-property-list');

        } catch (error) {
            console.error('Error creating missing property report:', error);
            this.showToast('Failed to create missing property report', 'error');
        }
    }

    async handleMissingBikeSubmit(form) {
        try {
            const formData = new FormData(form);
            const data = {};

            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }

            // First register the bike, then mark it as stolen
            const bikeResult = await this.bikeManager.registerBike(data);

            if (bikeResult && bikeResult.id) {
                await this.bikeManager.reportStolen(bikeResult.id, data);
                this.showToast('Stolen bike report created successfully', 'success');

                // Navigate back to missing property list
                await this.loadTabContent('property', 'missing-property-list');
            }

        } catch (error) {
            console.error('Error creating stolen bike report:', error);
            this.showToast('Failed to create stolen bike report', 'error');
        }
    }

    // Legacy method - keeping for compatibility but converting to use new form approach
    async showLogPropertyRecoveryFormOld() {
        try {
            const fields = [
                {
                    name: 'property_type',
                    type: 'select',
                    label: 'Property Type',
                    options: [
                        { value: 'electronics', label: 'Electronics' },
                        { value: 'clothing', label: 'Clothing' },
                        { value: 'jewelry', label: 'Jewelry' },
                        { value: 'documents', label: 'Documents' },
                        { value: 'tools', label: 'Tools' },
                        { value: 'sporting_goods', label: 'Sporting Goods' },
                        { value: 'bike', label: 'Bicycle' },
                        { value: 'other', label: 'Other' }
                    ],
                    required: true
                },
                {
                    name: 'description',
                    type: 'textarea',
                    label: 'Description',
                    placeholder: 'Detailed description of the found property...',
                    required: true
                },
                {
                    name: 'brand',
                    type: 'text',
                    label: 'Brand (if applicable)',
                    placeholder: 'e.g., Apple, Samsung, etc.'
                },
                {
                    name: 'model',
                    type: 'text',
                    label: 'Model (if applicable)',
                    placeholder: 'e.g., iPhone 12, etc.'
                },
                {
                    name: 'serial_number',
                    type: 'text',
                    label: 'Serial Number (if visible)',
                    placeholder: 'Serial number or identifying marks'
                },
                {
                    name: 'color',
                    type: 'text',
                    label: 'Color',
                    placeholder: 'Primary color(s)'
                },
                {
                    name: 'estimated_value',
                    type: 'number',
                    label: 'Estimated Value ($)',
                    placeholder: '0.00'
                },
                {
                    name: 'condition',
                    type: 'select',
                    label: 'Condition',
                    options: [
                        { value: 'excellent', label: 'Excellent' },
                        { value: 'good', label: 'Good' },
                        { value: 'fair', label: 'Fair' },
                        { value: 'poor', label: 'Poor' },
                        { value: 'damaged', label: 'Damaged' }
                    ],
                    required: true
                },
                {
                    name: 'found_location',
                    type: 'text',
                    label: 'Found Location',
                    placeholder: 'Where was this property found?',
                    required: true
                },
                {
                    name: 'found_date',
                    type: 'date',
                    label: 'Found Date',
                    value: new Date().toISOString().split('T')[0],
                    required: true
                },
                {
                    name: 'found_time',
                    type: 'time',
                    label: 'Found Time',
                    value: new Date().toTimeString().split(' ')[0].substring(0, 5),
                    required: true
                }
            ];

            this.ui.showForm('Log Property Recovery', fields, async (formData) => {
                try {
                    // Set category as 'found' for recovery transactions
                    formData.category = 'found';

                    await this.propertyManager.logProperty(formData);
                    this.showToast('Property recovery logged successfully', 'success');

                    // Refresh the found property list
                    await this.showFoundPropertyList();

                } catch (error) {
                    console.error('Error logging property recovery:', error);
                    this.showToast('Failed to log property recovery', 'error');
                }
            });

        } catch (error) {
            console.error('Error showing log property recovery form:', error);
            this.showToast('Failed to open property recovery form', 'error');
        }
    }

    async loadCreateMissingReportContent() {
        return `
            <div class="content-section">
                <div class="section-header">
                    <h2>CREATE MISSING PROPERTY REPORT</h2>
                    <div class="header-actions">
                        <button class="secondary-button" onclick="app.loadTabContent('property')">
                            <span class="button-icon">←</span>
                            Back to Property Menu
                        </button>
                    </div>
                </div>

                <div class="property-type-selection">
                    <h3>Select Property Type</h3>
                    <p>Choose the type of property you want to report as missing or stolen:</p>

                    <div class="property-type-grid">
                        <div class="property-type-card" onclick="app.loadTabContent('property', 'create-missing-general')">
                            <div class="type-icon">📦</div>
                            <div class="type-title">General Property</div>
                            <div class="type-description">Electronics, clothing, jewelry, documents, tools, and other items</div>
                        </div>

                        <div class="property-type-card" onclick="app.loadTabContent('property', 'create-missing-bike')">
                            <div class="type-icon">🚲</div>
                            <div class="type-title">Bicycle</div>
                            <div class="type-description">Report a stolen or missing bicycle</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async loadCreateMissingGeneralContent() {
        return `
            <div class="content-section">
                <div class="section-header">
                    <h2>REPORT MISSING GENERAL PROPERTY</h2>
                    <div class="header-actions">
                        <button class="secondary-button" onclick="app.loadTabContent('property', 'create-missing-report')">
                            <span class="button-icon">←</span>
                            Back to Property Type Selection
                        </button>
                    </div>
                </div>

                <div class="form-container">
                    <form id="missing-general-form" class="property-form">
                        <div class="form-section">
                            <h3>Property Information</h3>

                            <div class="form-group">
                                <label for="property_type">Property Type *</label>
                                <select id="property_type" name="property_type" required>
                                    <option value="">Select property type...</option>
                                    <option value="electronics">Electronics</option>
                                    <option value="clothing">Clothing</option>
                                    <option value="jewelry">Jewelry</option>
                                    <option value="documents">Documents</option>
                                    <option value="tools">Tools</option>
                                    <option value="sporting_goods">Sporting Goods</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="description">Description *</label>
                                <textarea id="description" name="description" placeholder="Detailed description of the missing property..." required></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="brand">Brand (if applicable)</label>
                                    <input type="text" id="brand" name="brand" placeholder="e.g., Apple, Samsung, etc.">
                                </div>
                                <div class="form-group">
                                    <label for="model">Model (if applicable)</label>
                                    <input type="text" id="model" name="model" placeholder="e.g., iPhone 12, etc.">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="serial_number">Serial Number (if known)</label>
                                    <input type="text" id="serial_number" name="serial_number" placeholder="Serial number or identifying marks">
                                </div>
                                <div class="form-group">
                                    <label for="color">Color</label>
                                    <input type="text" id="color" name="color" placeholder="Primary color(s)">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="estimated_value">Estimated Value ($)</label>
                                <input type="number" id="estimated_value" name="estimated_value" placeholder="0.00" step="0.01" min="0">
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>Owner Information</h3>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="owner_name">Owner Name *</label>
                                    <input type="text" id="owner_name" name="owner_name" placeholder="Name of property owner" required>
                                </div>
                                <div class="form-group">
                                    <label for="owner_contact">Owner Contact *</label>
                                    <input type="text" id="owner_contact" name="owner_contact" placeholder="Phone number or email" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="owner_address">Owner Address</label>
                                <input type="text" id="owner_address" name="owner_address" placeholder="Owner address">
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>Missing Details</h3>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="found_location">Last Known Location *</label>
                                    <input type="text" id="found_location" name="found_location" placeholder="Where was this property last seen?" required>
                                </div>
                                <div class="form-group">
                                    <label for="found_date">Date Reported Missing *</label>
                                    <input type="date" id="found_date" name="found_date" value="${new Date().toISOString().split('T')[0]}" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="investigation_notes">Additional Notes</label>
                                <textarea id="investigation_notes" name="investigation_notes" placeholder="Any additional details about the missing property"></textarea>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="secondary-button" onclick="app.loadTabContent('property', 'create-missing-report')">Cancel</button>
                            <button type="submit" class="primary-button">Report Missing Property</button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    async loadCreateMissingBikeContent() {
        return `
            <div class="content-section">
                <div class="section-header">
                    <h2>REPORT STOLEN BIKE</h2>
                    <div class="header-actions">
                        <button class="secondary-button" onclick="app.loadTabContent('property', 'create-missing-report')">
                            <span class="button-icon">←</span>
                            Back to Property Type Selection
                        </button>
                    </div>
                </div>

                <div class="form-container">
                    <form id="missing-bike-form" class="property-form">
                        <div class="form-section">
                            <h3>Bike Information</h3>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="make">Make/Brand *</label>
                                    <input type="text" id="make" name="make" placeholder="e.g., Trek, Giant, Specialized" required>
                                </div>
                                <div class="form-group">
                                    <label for="model">Model *</label>
                                    <input type="text" id="model" name="model" placeholder="Bike model" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="serial_number">Serial Number *</label>
                                    <input type="text" id="serial_number" name="serial_number" placeholder="Bike serial number" required>
                                </div>
                                <div class="form-group">
                                    <label for="color">Color *</label>
                                    <input type="text" id="color" name="color" placeholder="Primary color(s)" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="value">Estimated Value ($)</label>
                                <input type="number" id="value" name="value" placeholder="0.00" step="0.01" min="0">
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>Owner Information</h3>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="owner_name">Owner Name *</label>
                                    <input type="text" id="owner_name" name="owner_name" placeholder="Full name of bike owner" required>
                                </div>
                                <div class="form-group">
                                    <label for="owner_email">Owner Email</label>
                                    <input type="email" id="owner_email" name="owner_email" placeholder="Contact email">
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>Theft Details</h3>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="theft_date">Date Stolen *</label>
                                    <input type="date" id="theft_date" name="theft_date" required>
                                </div>
                                <div class="form-group">
                                    <label for="theft_time">Time Stolen (approximate)</label>
                                    <input type="time" id="theft_time" name="theft_time">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="theft_location">Location Stolen From *</label>
                                <input type="text" id="theft_location" name="theft_location" placeholder="Where was the bike stolen?" required>
                            </div>

                            <div class="form-group">
                                <label for="suspect_details">Suspect Details (if any)</label>
                                <textarea id="suspect_details" name="suspect_details" placeholder="Description of suspect or suspicious activity"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="theft_notes">Additional Notes</label>
                                <textarea id="theft_notes" name="theft_notes" placeholder="Any additional details about the theft"></textarea>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="secondary-button" onclick="app.loadTabContent('property', 'create-missing-report')">Cancel</button>
                            <button type="submit" class="primary-button">Report Stolen Bike</button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    async showCreateMissingReportForm() {
        await this.loadTabContent('property', 'create-missing-report');
    }

    // Legacy method - keeping for compatibility
    async showCreateMissingReportFormOld() {
        try {
            // First, ask user to select property type
            const typeFields = [
                {
                    name: 'property_category',
                    type: 'select',
                    label: 'Property Category',
                    options: [
                        { value: 'general', label: 'General Property' },
                        { value: 'bike', label: 'Bicycle' }
                    ],
                    required: true
                }
            ];

            this.ui.showForm('Select Property Type', typeFields, async (typeData) => {
                if (typeData.property_category === 'bike') {
                    await this.showCreateMissingBikeForm();
                } else {
                    await this.showCreateMissingGeneralPropertyForm();
                }
            });

        } catch (error) {
            console.error('Error showing create missing report form:', error);
            this.showToast('Failed to open missing report form', 'error');
        }
    }

    async showCreateMissingBikeForm() {
        try {
            const fields = [
                {
                    name: 'owner_name',
                    type: 'text',
                    label: 'Owner Name',
                    placeholder: 'Full name of bike owner',
                    required: true
                },
                {
                    name: 'owner_email',
                    type: 'email',
                    label: 'Owner Email',
                    placeholder: 'Contact email'
                },
                {
                    name: 'serial_number',
                    type: 'text',
                    label: 'Serial Number',
                    placeholder: 'Bike serial number',
                    required: true
                },
                {
                    name: 'make',
                    type: 'text',
                    label: 'Make/Brand',
                    placeholder: 'e.g., Trek, Giant, Specialized',
                    required: true
                },
                {
                    name: 'model',
                    type: 'text',
                    label: 'Model',
                    placeholder: 'Bike model',
                    required: true
                },
                {
                    name: 'color',
                    type: 'text',
                    label: 'Color',
                    placeholder: 'Primary color(s)',
                    required: true
                },
                {
                    name: 'value',
                    type: 'number',
                    label: 'Estimated Value ($)',
                    placeholder: '0.00'
                },
                {
                    name: 'theft_date',
                    type: 'date',
                    label: 'Date Stolen',
                    required: true
                },
                {
                    name: 'theft_time',
                    type: 'time',
                    label: 'Time Stolen (approximate)'
                },
                {
                    name: 'theft_location',
                    type: 'text',
                    label: 'Location Stolen From',
                    placeholder: 'Where was the bike stolen?',
                    required: true
                },
                {
                    name: 'suspect_details',
                    type: 'textarea',
                    label: 'Suspect Details (if any)',
                    placeholder: 'Description of suspect or suspicious activity'
                },
                {
                    name: 'theft_notes',
                    type: 'textarea',
                    label: 'Additional Notes',
                    placeholder: 'Any additional details about the theft'
                }
            ];

            this.ui.showForm('Report Stolen Bike', fields, async (formData) => {
                try {
                    // First register the bike, then mark it as stolen
                    const bikeResult = await this.bikeManager.registerBike(formData);

                    if (bikeResult && bikeResult.id) {
                        await this.bikeManager.reportStolen(bikeResult.id, formData);
                        this.showToast('Stolen bike report created successfully', 'success');

                        // Refresh the missing property list
                        await this.showMissingPropertyList();
                    }

                } catch (error) {
                    console.error('Error creating stolen bike report:', error);
                    this.showToast('Failed to create stolen bike report', 'error');
                }
            });

        } catch (error) {
            console.error('Error showing create missing bike form:', error);
            this.showToast('Failed to open missing bike form', 'error');
        }
    }

    async showCreateMissingGeneralPropertyForm() {
        try {
            const fields = [
                {
                    name: 'property_type',
                    type: 'select',
                    label: 'Property Type',
                    options: [
                        { value: 'electronics', label: 'Electronics' },
                        { value: 'clothing', label: 'Clothing' },
                        { value: 'jewelry', label: 'Jewelry' },
                        { value: 'documents', label: 'Documents' },
                        { value: 'tools', label: 'Tools' },
                        { value: 'sporting_goods', label: 'Sporting Goods' },
                        { value: 'other', label: 'Other' }
                    ],
                    required: true
                },
                {
                    name: 'description',
                    type: 'textarea',
                    label: 'Description',
                    placeholder: 'Detailed description of the missing property...',
                    required: true
                },
                {
                    name: 'brand',
                    type: 'text',
                    label: 'Brand (if applicable)',
                    placeholder: 'e.g., Apple, Samsung, etc.'
                },
                {
                    name: 'model',
                    type: 'text',
                    label: 'Model (if applicable)',
                    placeholder: 'e.g., iPhone 12, etc.'
                },
                {
                    name: 'serial_number',
                    type: 'text',
                    label: 'Serial Number (if known)',
                    placeholder: 'Serial number or identifying marks'
                },
                {
                    name: 'color',
                    type: 'text',
                    label: 'Color',
                    placeholder: 'Primary color(s)'
                },
                {
                    name: 'estimated_value',
                    type: 'number',
                    label: 'Estimated Value ($)',
                    placeholder: '0.00'
                },
                {
                    name: 'owner_name',
                    type: 'text',
                    label: 'Owner Name',
                    placeholder: 'Name of property owner',
                    required: true
                },
                {
                    name: 'owner_contact',
                    type: 'text',
                    label: 'Owner Contact',
                    placeholder: 'Phone number or email',
                    required: true
                },
                {
                    name: 'owner_address',
                    type: 'text',
                    label: 'Owner Address',
                    placeholder: 'Owner address'
                },
                {
                    name: 'found_location',
                    type: 'text',
                    label: 'Last Known Location',
                    placeholder: 'Where was this property last seen?',
                    required: true
                },
                {
                    name: 'found_date',
                    type: 'date',
                    label: 'Date Reported Missing',
                    value: new Date().toISOString().split('T')[0],
                    required: true
                },
                {
                    name: 'investigation_notes',
                    type: 'textarea',
                    label: 'Additional Notes',
                    placeholder: 'Any additional details about the missing property'
                }
            ];

            this.ui.showForm('Report Missing Property', fields, async (formData) => {
                try {
                    // Set category as 'stolen' for missing property reports
                    formData.category = 'stolen';
                    formData.status = 'investigating';

                    await this.propertyManager.logProperty(formData);
                    this.showToast('Missing property report created successfully', 'success');

                    // Refresh the missing property list
                    await this.showMissingPropertyList();

                } catch (error) {
                    console.error('Error creating missing property report:', error);
                    this.showToast('Failed to create missing property report', 'error');
                }
            });

        } catch (error) {
            console.error('Error showing create missing general property form:', error);
            this.showToast('Failed to open missing property form', 'error');
        }
    }

    // Bike Management Methods
    async addBike() {
        try {
            const fields = [
                {
                    name: 'owner_name',
                    type: 'text',
                    label: 'Owner Name',
                    placeholder: 'Full name of bike owner',
                    required: true
                },
                {
                    name: 'owner_email',
                    type: 'email',
                    label: 'Owner Email',
                    placeholder: 'Contact email'
                },
                {
                    name: 'serial_number',
                    type: 'text',
                    label: 'Serial Number',
                    placeholder: 'Bike serial number',
                    required: true
                },
                {
                    name: 'make',
                    type: 'text',
                    label: 'Make/Brand',
                    placeholder: 'e.g., Trek, Giant, Specialized',
                    required: true
                },
                {
                    name: 'model',
                    type: 'text',
                    label: 'Model',
                    placeholder: 'Bike model',
                    required: true
                },
                {
                    name: 'color',
                    type: 'text',
                    label: 'Color',
                    placeholder: 'Primary color(s)',
                    required: true
                },
                {
                    name: 'value',
                    type: 'number',
                    label: 'Estimated Value ($)',
                    placeholder: '0.00'
                }
            ];

            this.ui.showForm('Register Bike', fields, async (formData) => {
                try {
                    await this.bikeManager.registerBike(formData);
                    this.showToast('Bike registered successfully', 'success');

                    // Refresh the bikes list
                    await this.loadTabContent('records', 'bikes-management');

                } catch (error) {
                    console.error('Error registering bike:', error);
                    this.showToast('Failed to register bike', 'error');
                }
            });

        } catch (error) {
            console.error('Error showing add bike form:', error);
            this.showToast('Failed to open bike registration form', 'error');
        }
    }

    async viewBikeDetails(bikeId) {
        try {
            const bike = await this.bikeManager.getBike(bikeId);
            const activities = await this.bikeManager.getBikeActivities(bikeId);

            if (!bike) {
                this.showToast('Bike not found', 'error');
                return;
            }

            const detailsHTML = `
                <div class="bike-details-container">
                    <div class="bike-details-header">
                        <h3>Bike Details - ${bike.make} ${bike.model}</h3>
                        <div class="bike-status ${bike.is_stolen ? 'stolen' : 'registered'}">
                            ${bike.is_stolen ? 'STOLEN' : 'REGISTERED'}
                        </div>
                    </div>

                    <div class="bike-info-grid">
                        <div class="info-section">
                            <h4>Basic Information</h4>
                            <div class="info-item"><strong>Serial Number:</strong> ${bike.serial_number}</div>
                            <div class="info-item"><strong>Make:</strong> ${bike.make}</div>
                            <div class="info-item"><strong>Model:</strong> ${bike.model}</div>
                            <div class="info-item"><strong>Color:</strong> ${bike.color}</div>
                            ${bike.value ? `<div class="info-item"><strong>Value:</strong> $${bike.value}</div>` : ''}
                        </div>

                        <div class="info-section">
                            <h4>Owner Information</h4>
                            <div class="info-item"><strong>Owner:</strong> ${bike.owner_name}</div>
                            ${bike.owner_email ? `<div class="info-item"><strong>Email:</strong> ${bike.owner_email}</div>` : ''}
                            <div class="info-item"><strong>Registered:</strong> ${this.formatDate(bike.registered_at)}</div>
                        </div>

                        ${bike.is_stolen ? `
                            <div class="info-section theft-info">
                                <h4>Theft Information</h4>
                                ${bike.theft_date ? `<div class="info-item"><strong>Date Stolen:</strong> ${this.formatDate(bike.theft_date)}</div>` : ''}
                                ${bike.theft_time ? `<div class="info-item"><strong>Time Stolen:</strong> ${bike.theft_time}</div>` : ''}
                                ${bike.theft_location ? `<div class="info-item"><strong>Location:</strong> ${bike.theft_location}</div>` : ''}
                                ${bike.suspect_details ? `<div class="info-item"><strong>Suspect Details:</strong> ${bike.suspect_details}</div>` : ''}
                                ${bike.theft_notes ? `<div class="info-item"><strong>Notes:</strong> ${bike.theft_notes}</div>` : ''}
                            </div>
                        ` : ''}
                    </div>

                    <div class="bike-actions-section">
                        <button class="primary-button" onclick="app.editBike('${bike.id}')">Edit Bike</button>
                        ${bike.is_stolen ?
                            `<button class="success-button" onclick="app.markBikeRecovered('${bike.id}')">Mark Recovered</button>` :
                            `<button class="warning-button" onclick="app.reportBikeStolen('${bike.id}')">Report Stolen</button>`
                        }
                        <button class="secondary-button" onclick="app.loadTabContent('records', 'bikes-management')">Back to Bikes</button>
                    </div>

                    <div class="bike-activities-section">
                        <h4>Activity History</h4>
                        <div class="activities-list">
                            ${activities.length > 0 ? activities.map(activity => `
                                <div class="activity-item">
                                    <div class="activity-header">
                                        <span class="activity-type">${activity.activity_type}</span>
                                        <span class="activity-date">${this.formatDate(activity.activity_date)}</span>
                                    </div>
                                    <div class="activity-title">${activity.title}</div>
                                    ${activity.description ? `<div class="activity-description">${activity.description}</div>` : ''}
                                </div>
                            `).join('') : '<div class="no-activities">No activities recorded</div>'}
                        </div>
                    </div>
                </div>
            `;

            this.ui.showDialog('Bike Details', detailsHTML, 'info', true);

        } catch (error) {
            console.error('Error viewing bike details:', error);
            this.showToast('Failed to load bike details', 'error');
        }
    }

    async reportBikeStolen(bikeId) {
        try {
            const bike = await this.bikeManager.getBike(bikeId);
            if (!bike) {
                this.showToast('Bike not found', 'error');
                return;
            }

            const fields = [
                {
                    name: 'theft_date',
                    type: 'date',
                    label: 'Date Stolen',
                    value: new Date().toISOString().split('T')[0],
                    required: true
                },
                {
                    name: 'theft_time',
                    type: 'time',
                    label: 'Time Stolen (approximate)',
                    value: new Date().toTimeString().split(' ')[0].substring(0, 5)
                },
                {
                    name: 'theft_location',
                    type: 'text',
                    label: 'Location Stolen From',
                    placeholder: 'Where was the bike stolen?',
                    required: true
                },
                {
                    name: 'suspect_details',
                    type: 'textarea',
                    label: 'Suspect Details (if any)',
                    placeholder: 'Description of suspect or suspicious activity'
                },
                {
                    name: 'theft_notes',
                    type: 'textarea',
                    label: 'Additional Notes',
                    placeholder: 'Any additional details about the theft'
                }
            ];

            this.ui.showForm(`Report Stolen - ${bike.make} ${bike.model}`, fields, async (formData) => {
                try {
                    await this.bikeManager.reportStolen(bikeId, formData);
                    this.showToast('Bike reported as stolen', 'success');

                    // Refresh the bikes list
                    await this.loadTabContent('records', 'bikes-management');

                } catch (error) {
                    console.error('Error reporting bike stolen:', error);
                    this.showToast('Failed to report bike stolen', 'error');
                }
            });

        } catch (error) {
            console.error('Error showing report stolen form:', error);
            this.showToast('Failed to open report stolen form', 'error');
        }
    }

    async markBikeRecovered(bikeId) {
        try {
            const bike = await this.bikeManager.getBike(bikeId);
            if (!bike) {
                this.showToast('Bike not found', 'error');
                return;
            }

            const fields = [
                {
                    name: 'location',
                    type: 'text',
                    label: 'Recovery Location',
                    placeholder: 'Where was the bike recovered?',
                    required: true
                },
                {
                    name: 'recovery_notes',
                    type: 'textarea',
                    label: 'Recovery Notes',
                    placeholder: 'Details about the recovery'
                }
            ];

            this.ui.showForm(`Mark Recovered - ${bike.make} ${bike.model}`, fields, async (formData) => {
                try {
                    await this.bikeManager.markRecovered(bikeId, formData);
                    this.showToast('Bike marked as recovered', 'success');

                    // Refresh the bikes list or missing property list depending on current view
                    if (this.currentScreen === 'bikes-management') {
                        await this.loadTabContent('records', 'bikes-management');
                    } else {
                        await this.showMissingPropertyList();
                    }

                } catch (error) {
                    console.error('Error marking bike recovered:', error);
                    this.showToast('Failed to mark bike recovered', 'error');
                }
            });

        } catch (error) {
            console.error('Error showing mark recovered form:', error);
            this.showToast('Failed to open mark recovered form', 'error');
        }
    }

    async editBike(bikeId) {
        try {
            const bike = await this.bikeManager.getBike(bikeId);
            if (!bike) {
                this.showToast('Bike not found', 'error');
                return;
            }

            const fields = [
                {
                    name: 'owner_name',
                    type: 'text',
                    label: 'Owner Name',
                    value: bike.owner_name,
                    required: true
                },
                {
                    name: 'owner_email',
                    type: 'email',
                    label: 'Owner Email',
                    value: bike.owner_email || ''
                },
                {
                    name: 'make',
                    type: 'text',
                    label: 'Make/Brand',
                    value: bike.make,
                    required: true
                },
                {
                    name: 'model',
                    type: 'text',
                    label: 'Model',
                    value: bike.model,
                    required: true
                },
                {
                    name: 'color',
                    type: 'text',
                    label: 'Color',
                    value: bike.color,
                    required: true
                },
                {
                    name: 'value',
                    type: 'number',
                    label: 'Estimated Value ($)',
                    value: bike.value || ''
                }
            ];

            this.ui.showForm(`Edit Bike - ${bike.make} ${bike.model}`, fields, async (formData) => {
                try {
                    await this.bikeManager.updateBike(bikeId, formData);
                    this.showToast('Bike updated successfully', 'success');

                    // Refresh the current view
                    if (this.currentScreen === 'bikes-management') {
                        await this.loadTabContent('records', 'bikes-management');
                    }

                } catch (error) {
                    console.error('Error updating bike:', error);
                    this.showToast('Failed to update bike', 'error');
                }
            });

        } catch (error) {
            console.error('Error showing edit bike form:', error);
            this.showToast('Failed to open edit bike form', 'error');
        }
    }

    // Property search methods for the new structure
    async searchFoundProperties(searchTerm) {
        try {
            if (!searchTerm || searchTerm.trim() === '') {
                await this.showFoundPropertyList();
                return;
            }

            const allProperties = await this.propertyManager.getAllProperties({ category: 'found' });
            const filteredProperties = allProperties.filter(property =>
                property.property_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                property.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                (property.brand && property.brand.toLowerCase().includes(searchTerm.toLowerCase())) ||
                (property.model && property.model.toLowerCase().includes(searchTerm.toLowerCase())) ||
                (property.serial_number && property.serial_number.toLowerCase().includes(searchTerm.toLowerCase())) ||
                property.found_location.toLowerCase().includes(searchTerm.toLowerCase())
            );

            const listContainer = document.getElementById('found-property-list');
            if (!listContainer) return;

            if (filteredProperties.length === 0) {
                listContainer.innerHTML = `
                    <div class="no-data">
                        <h3>No found property matches "${searchTerm}"</h3>
                        <p>Try a different search term or <button class="link-button" onclick="app.loadTabContent('property', 'found-property-list')">view all found property</button>.</p>
                    </div>
                `;
                return;
            }

            listContainer.innerHTML = filteredProperties.map(property => `
                <div class="property-card" data-property-id="${property.id}">
                    <div class="property-header">
                        <div class="property-number">${property.property_number}</div>
                        <div class="property-status status-${property.status}">${this.formatPropertyStatus(property.status)}</div>
                    </div>
                    <div class="property-details">
                        <div class="property-description">${property.description}</div>
                        <div class="property-meta">
                            <span>Found: ${this.formatDate(property.found_date)}</span>
                            <span>Location: ${property.found_location}</span>
                            ${property.brand ? `<span>Brand: ${property.brand}</span>` : ''}
                        </div>
                    </div>
                    <div class="property-actions">
                        <button class="action-btn" onclick="app.viewPropertyDetails('${property.id}')" title="View Details">👁️</button>
                        <button class="action-btn" onclick="app.updatePropertyStatus('${property.id}')" title="Update Status">📝</button>
                    </div>
                </div>
            `).join('');

        } catch (error) {
            console.error('Error searching found properties:', error);
            this.showToast('Error searching found properties', 'error');
        }
    }

    async searchMissingProperties(searchTerm) {
        try {
            if (!searchTerm || searchTerm.trim() === '') {
                await this.showMissingPropertyList();
                return;
            }

            const missingProperties = await this.propertyManager.getAllProperties({ category: 'stolen' });
            const stolenBikes = await this.bikeManager.getAllBikes({ is_stolen: true });

            const filteredProperties = missingProperties.filter(property =>
                property.property_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                property.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                (property.brand && property.brand.toLowerCase().includes(searchTerm.toLowerCase())) ||
                (property.model && property.model.toLowerCase().includes(searchTerm.toLowerCase())) ||
                (property.serial_number && property.serial_number.toLowerCase().includes(searchTerm.toLowerCase())) ||
                (property.owner_name && property.owner_name.toLowerCase().includes(searchTerm.toLowerCase()))
            );

            const filteredBikes = stolenBikes.filter(bike =>
                bike.serial_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                bike.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
                bike.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
                bike.color.toLowerCase().includes(searchTerm.toLowerCase()) ||
                bike.owner_name.toLowerCase().includes(searchTerm.toLowerCase())
            );

            const totalFiltered = filteredProperties.length + filteredBikes.length;

            const listContainer = document.getElementById('missing-property-list');
            if (!listContainer) return;

            if (totalFiltered === 0) {
                listContainer.innerHTML = `
                    <div class="no-data">
                        <h3>No missing property matches "${searchTerm}"</h3>
                        <p>Try a different search term or <button class="link-button" onclick="app.loadTabContent('property', 'missing-property-list')">view all missing property</button>.</p>
                    </div>
                `;
                return;
            }

            listContainer.innerHTML = `
                ${filteredProperties.length > 0 ? `
                    <div class="missing-section">
                        <h4>General Property (${filteredProperties.length})</h4>
                        <div class="property-list">
                            ${filteredProperties.map(property => `
                                <div class="property-card missing" data-property-id="${property.id}">
                                    <div class="property-header">
                                        <div class="property-number">${property.property_number}</div>
                                        <div class="property-type">General Property</div>
                                    </div>
                                    <div class="property-details">
                                        <div class="property-description">${property.description}</div>
                                        <div class="property-meta">
                                            <span>Reported: ${this.formatDate(property.found_date)}</span>
                                            ${property.brand ? `<span>Brand: ${property.brand}</span>` : ''}
                                            ${property.serial_number ? `<span>Serial: ${property.serial_number}</span>` : ''}
                                        </div>
                                    </div>
                                    <div class="property-actions">
                                        <button class="action-btn" onclick="app.viewPropertyDetails('${property.id}')" title="View Details">👁️</button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
                ${filteredBikes.length > 0 ? `
                    <div class="missing-section">
                        <h4>Stolen Bikes (${filteredBikes.length})</h4>
                        <div class="property-list">
                            ${filteredBikes.map(bike => `
                                <div class="property-card missing bike" data-bike-id="${bike.id}">
                                    <div class="property-header">
                                        <div class="property-number">BIKE-${bike.serial_number}</div>
                                        <div class="property-type">Bicycle</div>
                                    </div>
                                    <div class="property-details">
                                        <div class="property-description">${bike.make} ${bike.model} - ${bike.color}</div>
                                        <div class="property-meta">
                                            <span>Owner: ${bike.owner_name}</span>
                                            <span>Stolen: ${bike.theft_date ? this.formatDate(bike.theft_date) : 'Unknown'}</span>
                                            <span>Serial: ${bike.serial_number}</span>
                                        </div>
                                    </div>
                                    <div class="property-actions">
                                        <button class="action-btn" onclick="app.viewBikeDetails('${bike.id}')" title="View Details">👁️</button>
                                        <button class="action-btn" onclick="app.markBikeRecovered('${bike.id}')" title="Mark Recovered">✅</button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            `;

        } catch (error) {
            console.error('Error searching missing properties:', error);
            this.showToast('Error searching missing properties', 'error');
        }
    }

    /**
     * Show security status (admin only)
     */
    showSecurityStatus() {
        if (!this.auth.hasPermission('admin.security_monitoring')) {
            this.ui.showDialog('Access Denied', 'Permission denied: admin.security_monitoring required.', 'error');
            return;
        }

        const stats = this.securityMonitor.getMonitoringStats();
        const recentActivities = this.securityMonitor.getRecentActivities(5);
        const errorCounts = this.securityErrorHandler.getErrorCounts();

        const statusHtml = `
            <div class="security-status">
                <h3>🛡️ Security Monitoring Status</h3>

                <div class="security-stats">
                    <h4>Monitoring Statistics</h4>
                    <ul>
                        <li>Status: ${stats.active ? '✅ Active' : '❌ Inactive'}</li>
                        <li>Uptime: ${Math.round(stats.uptime / 60000)} minutes</li>
                        <li>Suspicious Activities: ${stats.suspiciousActivities}</li>
                        <li>Memory Usage: ${Math.round(stats.baseline?.memory / 1024 / 1024) || 0} MB</li>
                    </ul>
                </div>

                <div class="recent-activities">
                    <h4>Recent Suspicious Activities</h4>
                    ${recentActivities.length > 0 ?
                        recentActivities.map(activity =>
                            `<div class="activity-item">
                                <strong>${activity.type}</strong> - ${new Date(activity.timestamp).toLocaleString()}
                                <br><small>${JSON.stringify(activity.details)}</small>
                            </div>`
                        ).join('') :
                        '<p>No recent suspicious activities detected.</p>'
                    }
                </div>

                <div class="error-counts">
                    <h4>Error Counts</h4>
                    ${errorCounts.size > 0 ?
                        Array.from(errorCounts.entries()).map(([type, count]) =>
                            `<div>${type}: ${count}</div>`
                        ).join('') :
                        '<p>No errors recorded.</p>'
                    }
                </div>
            </div>
        `;

        this.ui.showDialog('Security Status', statusHtml, 'info');
    }

    /**
     * Export security report (admin only)
     */
    exportSecurityReport() {
        if (!this.auth.hasPermission('admin.security_monitoring')) {
            this.ui.showDialog('Access Denied', 'Permission denied: admin.security_monitoring required.', 'error');
            return;
        }

        const report = this.securityMonitor.exportSecurityReport();
        const reportJson = JSON.stringify(report, null, 2);

        // Create download link
        const blob = new Blob([reportJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `security-report-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.ui.showDialog('Success', 'Security report exported successfully.', 'success');
    }
}

// Add CSS for content sections
const additionalCSS = `
.content-section {
    padding: 20px;
}

.content-section h2 {
    color: #ff0000;
    font-size: 20px;
    margin-bottom: 30px;
    border-bottom: 2px solid #ff0000;
    padding-bottom: 10px;
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.menu-item {
    border: 2px solid #ff0000;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #000000;
}

.menu-item:hover {
    background: #330000;
    border-color: #ff4444;
}

.menu-icon {
    font-size: 24px;
    margin-bottom: 10px;
    filter: grayscale(100%) sepia(100%) hue-rotate(0deg) brightness(0.8) saturate(2);
}

.menu-title {
    font-size: 16px;
    font-weight: bold;
    color: #ff0000;
    margin-bottom: 5px;
}

.menu-desc {
    font-size: 12px;
    color: #ff4444;
}
`;

// Inject additional CSS
const style = document.createElement('style');
style.textContent = additionalCSS;
document.head.appendChild(style);

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new SteviRetroApp();
});
