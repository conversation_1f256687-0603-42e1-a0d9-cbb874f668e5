// Command System for S.T.E.V.I Retro Electron App
import { OutreachTransactionManager } from './outreach-transactions.js';

export class CommandManager {
    constructor(app) {
        this.app = app;
        this.auth = app.auth;
        this.data = app.data;
        this.ui = app.ui;
        
        this.commands = new Map();
        this.initializeCommands();
    }

    initializeCommands() {
        // Register all commands
        this.commands.set('report-incident', new ReportIncidentCommand(this));
        this.commands.set('search-incidents', new SearchIncidentsCommand(this));
        this.commands.set('add-person', new AddPersonCommand(this));
        this.commands.set('add-address', new AddAddressCommand(this));
        this.commands.set('add-plate', new AddPlateCommand(this));
        this.commands.set('add-activity', new AddActivityCommand(this));
        this.commands.set('search-records', new SearchRecordsCommand(this));
        this.commands.set('items', new ItemsCommand(this));
        this.commands.set('generate-report', new GenerateReportCommand(this));
        this.commands.set('export-data', new ExportDataCommand(this));
        this.commands.set('sync-data', new SyncDataCommand(this));
        this.commands.set('settings', new SettingsCommand(this));
        this.commands.set('about', new AboutCommand(this));
        this.commands.set('emergency-contacts', new EmergencyContactsCommand(this));
        this.commands.set('user-profile', new UserProfileCommand(this));
        this.commands.set('check-updates', new CheckUpdatesCommand(this));
        this.commands.set('update', new UpdateCommand(this));
        this.commands.set('release-notes', new ReleaseNotesCommand(this));
        this.commands.set('cleanup-updates', new CleanupUpdatesCommand(this));
        this.commands.set('create-test-incident', new CreateTestIncidentCommand(this));
        this.commands.set('outreach-transactions', new OutreachTransactionCommand(this));
        this.commands.set('log-property', new LogPropertyCommand(this));
        this.commands.set('property-report', new PropertyReportCommand(this));
        this.commands.set('create-test-data', new CreateTestDataCommand(this));
        this.commands.set('remove-test-data', new RemoveTestDataCommand(this));
        this.commands.set('clear-data', new ClearDataCommand(this));
    }

    async executeCommand(commandName, args = []) {
        const command = this.commands.get(commandName);
        if (!command) {
            throw new Error(`Unknown command: ${commandName}`);
        }

        try {
            return await command.execute(args);
        } catch (error) {
            console.error(`Error executing command ${commandName}:`, error);
            this.ui.showDialog('Error', error.message, 'error');
            throw error;
        }
    }

    getCommand(commandName) {
        return this.commands.get(commandName);
    }

    getAllCommands() {
        return Array.from(this.commands.keys());
    }
}

// Base Command Class
class BaseCommand {
    constructor(manager) {
        this.manager = manager;
        this.auth = manager.auth;
        this.data = manager.data;
        this.ui = manager.ui;
        this.app = manager.app;
        this.schema = manager.app.schema;
    }

    async execute(args) {
        throw new Error('Command must implement execute method');
    }
}

// Report Incident Command
class ReportIncidentCommand extends BaseCommand {
    async execute(args) {
        // Navigate to the new incident creation screen instead of showing modal
        if (window.app && window.app.loadTabContent) {
            await window.app.loadTabContent('incidents', 'create-incident');
            return Promise.resolve({ redirected: true });
        }

        // Fallback to old modal if app navigation isn't available
        const fields = [
            {
                name: 'use_current_time',
                label: 'Use current date and time?',
                type: 'confirm',
                value: true
            },
            {
                name: 'incident_date',
                label: 'Incident Date',
                type: 'date',
                required: true,
                condition: (data) => !data.use_current_time
            },
            {
                name: 'incident_time',
                label: 'Incident Time',
                type: 'time',
                required: true,
                condition: (data) => !data.use_current_time
            },
            {
                name: 'location',
                label: 'Location',
                type: 'text',
                required: true,
                placeholder: 'Street address or intersection'
            },
            {
                name: 'incident_type',
                label: 'Incident Type',
                type: 'select',
                required: true,
                options: [
                    'Assault',
                    'Theft',
                    'Vandalism',
                    'Drug Activity',
                    'Mental Health Crisis',
                    'Medical Emergency',
                    'Disturbance',
                    'Other'
                ]
            },
            {
                name: 'priority',
                label: 'Priority',
                type: 'select',
                required: true,
                options: ['low', 'medium', 'high'],
                value: 'medium'
            },
            {
                name: 'description',
                label: 'Description',
                type: 'textarea',
                required: true,
                placeholder: 'Detailed description of the incident'
            },
            {
                name: 'people_involved',
                label: 'People Involved',
                type: 'textarea',
                placeholder: 'Names, descriptions, or IDs of people involved'
            },
            {
                name: 'police_notified',
                label: 'Police Notified?',
                type: 'confirm',
                value: false
            },
            {
                name: 'police_file_number',
                label: 'Police File Number',
                type: 'text',
                condition: (data) => data.police_notified
            },
            {
                name: 'follow_up_required',
                label: 'Follow-up Required?',
                type: 'confirm',
                value: false
            },
            {
                name: 'follow_up_notes',
                label: 'Follow-up Notes',
                type: 'textarea',
                condition: (data) => data.follow_up_required
            }
        ];

        return new Promise((resolve) => {
            this.ui.showForm('Report New Incident', fields, async (formData) => {
                try {
                    // Process the form data
                    const incidentData = {
                        ...formData,
                        incident_number: this.generateIncidentNumber(),
                        reported_by: this.auth.getCurrentUser()?.email,
                        status: 'open',
                        created_at: new Date().toISOString(),
                        // Ensure required dispatch fields are present
                        narrative: formData.description, // Map description to narrative for database
                        priority: formData.priority || 'medium',
                        is_urgent: formData.priority === 'high',
                        tags: []
                    };

                    // Set incident date/time
                    if (formData.use_current_time) {
                        const now = new Date();
                        incidentData.incident_date = now.toISOString().split('T')[0];
                        incidentData.incident_time = now.toTimeString().split(' ')[0];
                    }

                    // Save to database
                    const result = await this.data.insert('incidents', incidentData);
                    
                    this.ui.showDialog(
                        'Incident Reported',
                        `Incident ${incidentData.incident_number} has been successfully reported.`,
                        'success'
                    );

                    resolve(result);
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to report incident: ${error.message}`, 'error');
                    resolve(null);
                }
            });
        });
    }

    generateIncidentNumber() {
        const now = new Date();
        const year = now.getFullYear().toString().slice(-2);
        const sequence = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
        return `IHARC-${year}-${sequence}`;
    }
}

// Add Person Command
class AddPersonCommand extends BaseCommand {
    async execute(args) {
        // Generate fields from schema
        const fields = this.data.schema.generateFormFields('people');

        return new Promise((resolve) => {
            this.ui.showFullScreenForm('Add Person Record', fields, async (formData) => {
                try {
                    // Convert form data to database format
                    const dbData = this.data.schema.convertFormToDatabase('people', formData);

                    const personData = {
                        ...dbData,
                        created_by: this.auth.getCurrentUser()?.email,
                        created_at: new Date().toISOString()
                    };

                    const result = await this.data.insert('people', personData);

                    this.ui.showDialog(
                        'Person Added',
                        `Person record for ${formData.first_name} ${formData.last_name} has been created.`,
                        'success'
                    );

                    resolve(result);
                    return true; // Indicate success for form navigation
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to add person: ${error.message}`, 'error');
                    resolve(null);
                    return false; // Indicate failure to stay on form
                }
            });
        });
    }
}

// Search Records Command
class SearchRecordsCommand extends BaseCommand {
    async execute(args) {
        return new Promise((resolve) => {
            this.showSearchInterface(resolve);
        });
    }

    showSearchInterface(resolve) {
        // Create search modal
        const searchModal = document.createElement('div');
        searchModal.className = 'modal-overlay';

        searchModal.innerHTML = `
            <div class="modal-dialog search-modal">
                <div class="modal-header">
                    <h3>Search Records</h3>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-field">
                            <label for="search-type">Search In:</label>
                            <select id="search-type" name="search-type">
                                <option value="all">All Records</option>
                                <option value="people">People</option>
                                <option value="addresses">Addresses</option>
                                <option value="license_plates">License Plates</option>
                            </select>
                        </div>
                        <div class="form-field">
                            <label for="search-query">Search For:</label>
                            <input type="text" id="search-query" name="search-query" placeholder="Enter name, address, plate number, etc.">
                        </div>
                        <div class="form-field">
                            <label for="search-field">Search Field:</label>
                            <select id="search-field" name="search-field">
                                <option value="all">All Fields</option>
                                <option value="first_name">First Name</option>
                                <option value="last_name">Last Name</option>
                                <option value="email">Email</option>
                                <option value="phone">Phone</option>
                                <option value="street_address">Street Address</option>
                                <option value="city">City</option>
                                <option value="plate_number">Plate Number</option>
                                <option value="vehicle_make">Vehicle Make</option>
                                <option value="vehicle_model">Vehicle Model</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="secondary-button" id="search-cancel">Cancel</button>
                            <button type="button" class="primary-button" id="search-submit">Search</button>
                        </div>
                    </div>
                    <div class="search-results" id="search-results" style="display: none;">
                        <h4>Search Results</h4>
                        <div class="results-container" id="results-container"></div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(searchModal);

        // Set up event handlers
        this.setupSearchHandlers(searchModal, resolve);

        // Focus search input
        const searchInput = searchModal.querySelector('#search-query');
        searchInput.focus();
    }

    setupSearchHandlers(modal, resolve) {
        const searchType = modal.querySelector('#search-type');
        const searchField = modal.querySelector('#search-field');
        const searchQuery = modal.querySelector('#search-query');
        const searchSubmit = modal.querySelector('#search-submit');
        const searchCancel = modal.querySelector('#search-cancel');
        const searchResults = modal.querySelector('#search-results');
        const resultsContainer = modal.querySelector('#results-container');

        // Update available fields based on search type
        searchType.addEventListener('change', () => {
            this.updateSearchFields(searchType.value, searchField);
        });

        // Handle search submission
        const performSearch = async () => {
            const type = searchType.value;
            const query = searchQuery.value.trim();
            const field = searchField.value;

            if (!query) {
                this.ui.showDialog('Search Error', 'Please enter a search term.', 'error');
                return;
            }

            try {
                searchSubmit.disabled = true;
                searchSubmit.textContent = 'Searching...';

                const results = await this.performSearch(type, query, field);
                this.displayResults(results, resultsContainer, searchResults);

            } catch (error) {
                this.ui.showDialog('Search Error', `Search failed: ${error.message}`, 'error');
            } finally {
                searchSubmit.disabled = false;
                searchSubmit.textContent = 'Search';
            }
        };

        // Event listeners
        searchSubmit.addEventListener('click', performSearch);
        searchCancel.addEventListener('click', () => {
            modal.remove();
            resolve(null);
        });

        // Enter key to search
        searchQuery.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Escape key to cancel
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                modal.remove();
                resolve(null);
            }
        });
    }

    updateSearchFields(searchType, searchField) {
        // Clear current options
        searchField.innerHTML = '<option value="all">All Fields</option>';

        const fieldOptions = {
            people: [
                { value: 'first_name', text: 'First Name' },
                { value: 'last_name', text: 'Last Name' },
                { value: 'email', text: 'Email' },
                { value: 'phone', text: 'Phone' },
                { value: 'emergency_contact', text: 'Emergency Contact' }
            ],
            addresses: [
                { value: 'street_address', text: 'Street Address' },
                { value: 'city', text: 'City' },
                { value: 'province', text: 'Province' },
                { value: 'postal_code', text: 'Postal Code' }
            ],
            license_plates: [
                { value: 'plate_number', text: 'Plate Number' },
                { value: 'province', text: 'Province' },
                { value: 'vehicle_make', text: 'Vehicle Make' },
                { value: 'vehicle_model', text: 'Vehicle Model' },
                { value: 'vehicle_color', text: 'Vehicle Color' },
                { value: 'owner_name', text: 'Owner Name' }
            ]
        };

        if (searchType === 'all') {
            // Add all fields for all types
            Object.values(fieldOptions).forEach(fields => {
                fields.forEach(field => {
                    const option = document.createElement('option');
                    option.value = field.value;
                    option.textContent = field.text;
                    searchField.appendChild(option);
                });
            });
        } else if (fieldOptions[searchType]) {
            fieldOptions[searchType].forEach(field => {
                const option = document.createElement('option');
                option.value = field.value;
                option.textContent = field.text;
                searchField.appendChild(option);
            });
        }
    }

    async performSearch(type, query, field) {
        const results = {
            people: [],
            addresses: [],
            license_plates: []
        };

        const searchTables = type === 'all' ? ['people', 'addresses', 'license_plates'] : [type];

        for (const table of searchTables) {
            try {
                let searchCriteria = {};

                if (field === 'all') {
                    // Search across multiple fields for the table
                    const tableResults = await this.searchAllFields(table, query);
                    results[table] = tableResults;
                } else {
                    // Search specific field
                    searchCriteria[field] = query;
                    const tableResults = await this.data.search(table, searchCriteria);
                    results[table] = tableResults || [];
                }
            } catch (error) {
                console.error(`Error searching ${table}:`, error);
                results[table] = [];
            }
        }

        return results;
    }

    async searchAllFields(table, query) {
        try {
            // Get all records and filter client-side for comprehensive search
            const allRecords = await this.data.search(table, {});

            if (!allRecords || allRecords.length === 0) {
                return [];
            }

            const lowerQuery = query.toLowerCase();

            return allRecords.filter(record => {
                // Search across all string fields
                return Object.values(record).some(value => {
                    if (typeof value === 'string') {
                        return value.toLowerCase().includes(lowerQuery);
                    }
                    return false;
                });
            });
        } catch (error) {
            console.error(`Error in searchAllFields for ${table}:`, error);
            return [];
        }
    }

    displayResults(results, container, resultsSection) {
        container.innerHTML = '';

        let totalResults = 0;
        Object.values(results).forEach(tableResults => {
            totalResults += tableResults.length;
        });

        if (totalResults === 0) {
            container.innerHTML = '<div class="no-results">No records found matching your search criteria.</div>';
            resultsSection.style.display = 'block';
            return;
        }

        // Display results by category
        Object.entries(results).forEach(([table, records]) => {
            if (records.length > 0) {
                const section = document.createElement('div');
                section.className = 'results-section';

                const header = document.createElement('h5');
                header.textContent = `${this.formatTableName(table)} (${records.length})`;
                section.appendChild(header);

                const recordsList = document.createElement('div');
                recordsList.className = 'records-list';

                records.forEach(record => {
                    const recordDiv = document.createElement('div');
                    recordDiv.className = 'record-item';
                    recordDiv.innerHTML = this.formatRecord(table, record);

                    // Add click handler to open record detail view
                    recordDiv.addEventListener('click', () => {
                        this.openRecordDetail(table, record);
                    });

                    recordsList.appendChild(recordDiv);
                });

                section.appendChild(recordsList);
                container.appendChild(section);
            }
        });

        resultsSection.style.display = 'block';
    }

    formatTableName(table) {
        const names = {
            people: 'People',
            addresses: 'Addresses',
            license_plates: 'License Plates'
        };
        return names[table] || table;
    }

    formatRecord(table, record) {
        switch (table) {
            case 'people':
                return `
                    <div class="record-header">${record.first_name || ''} ${record.last_name || ''}</div>
                    <div class="record-details">
                        ${record.email ? `<span>Email: ${record.email}</span>` : ''}
                        ${record.phone ? `<span>Phone: ${record.phone}</span>` : ''}
                        ${record.housing_status ? `<span>Housing: ${record.housing_status}</span>` : ''}
                    </div>
                    <div class="record-meta">ID: ${record.id} | Created: ${this.formatDate(record.created_at)}</div>
                `;

            case 'addresses':
                return `
                    <div class="record-header">${record.street_address || ''}</div>
                    <div class="record-details">
                        <span>${record.city || ''}, ${record.province || ''} ${record.postal_code || ''}</span>
                        ${record.country ? `<span>Country: ${record.country}</span>` : ''}
                    </div>
                    <div class="record-meta">ID: ${record.id} | Created: ${this.formatDate(record.created_at)}</div>
                `;

            case 'license_plates':
                return `
                    <div class="record-header">${record.plate_number || ''} (${record.province || ''})</div>
                    <div class="record-details">
                        ${record.vehicle_make ? `<span>${record.vehicle_make} ${record.vehicle_model || ''}</span>` : ''}
                        ${record.vehicle_year ? `<span>Year: ${record.vehicle_year}</span>` : ''}
                        ${record.vehicle_color ? `<span>Color: ${record.vehicle_color}</span>` : ''}
                        ${record.owner_name ? `<span>Owner: ${record.owner_name}</span>` : ''}
                    </div>
                    <div class="record-meta">ID: ${record.id} | Created: ${this.formatDate(record.created_at)}</div>
                `;

            default:
                return `<div class="record-header">Record ID: ${record.id}</div>`;
        }
    }

    formatDate(dateString) {
        if (!dateString) return 'Unknown';
        try {
            return new Date(dateString).toLocaleDateString();
        } catch {
            return dateString;
        }
    }

    openRecordDetail(table, record) {
        try {
            console.log('Opening record detail for:', table, record);

            // Close search modal first
            const searchModal = document.querySelector('.modal-overlay');
            if (searchModal) {
                searchModal.remove();
            }

            // Handle people records differently - show summary view first
            if (table === 'people') {
                this.showPersonSummaryView(record);
            } else {
                // For other record types, use the old modal system
                this.showRecordDetailView(table, record);
            }
        } catch (error) {
            console.error('Error opening record detail:', error);
            console.error('Error stack:', error.stack);
            this.ui.showDialog('Error', `Failed to open record: ${error.message}`, 'error');
        }
    }

    showPersonSummaryView(person) {
        try {
            const summaryModal = document.createElement('div');
            summaryModal.className = 'modal-overlay';

            summaryModal.innerHTML = `
                <div class="modal-dialog person-summary-modal">
                    <div class="modal-header">
                        <h3>Person Summary</h3>
                        <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="person-summary-container">
                            <div class="person-summary-header">
                                <div class="person-avatar-summary">
                                    <div class="avatar-placeholder-summary">
                                        ${(person.first_name?.[0] || '?').toUpperCase()}${(person.last_name?.[0] || '').toUpperCase()}
                                    </div>
                                </div>
                                <div class="person-summary-info">
                                    <h4 class="person-summary-name">
                                        ${person.first_name || ''} ${person.last_name || ''}
                                    </h4>
                                    <div class="person-summary-id">ID: ${person.id}</div>
                                </div>
                            </div>

                            <div class="person-summary-details">
                                <div class="summary-section">
                                    <h5>Contact Information</h5>
                                    <div class="summary-grid">
                                        <div class="summary-item">
                                            <span class="summary-label">Email:</span>
                                            <span class="summary-value">${person.email || 'Not provided'}</span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-label">Phone:</span>
                                            <span class="summary-value">${person.phone || 'Not provided'}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="summary-section">
                                    <h5>Personal Information</h5>
                                    <div class="summary-grid">
                                        <div class="summary-item">
                                            <span class="summary-label">Date of Birth:</span>
                                            <span class="summary-value">${person.date_of_birth || 'Not provided'}</span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-label">Housing Status:</span>
                                            <span class="summary-value">${person.housing_status || 'Not provided'}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="summary-section">
                                    <h5>Record Information</h5>
                                    <div class="summary-grid">
                                        <div class="summary-item">
                                            <span class="summary-label">Created:</span>
                                            <span class="summary-value">${this.formatDate(person.created_at)}</span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-label">Created By:</span>
                                            <span class="summary-value">${person.created_by || 'System'}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="primary-button" id="view-full-record-btn">View Full Record</button>
                        <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Close</button>
                    </div>
                </div>
            `;

            document.body.appendChild(summaryModal);

            // Handle "View Full Record" button click
            const viewFullRecordBtn = summaryModal.querySelector('#view-full-record-btn');
            viewFullRecordBtn.addEventListener('click', async () => {
                summaryModal.remove();

                // Navigate to the full person detail page
                if (window.app) {
                    await window.app.viewPersonDetail(person.id);
                }
            });

        } catch (error) {
            console.error('Error showing person summary view:', error);
            this.ui.showDialog('Error', `Failed to show person summary: ${error.message}`, 'error');
        }
    }

    showRecordDetailView(table, record) {
        try {
            console.log('Creating record detail view for:', table, record);

            const detailModal = document.createElement('div');
            detailModal.className = 'modal-overlay';

        detailModal.innerHTML = `
            <div class="modal-dialog record-detail-modal">
                <div class="modal-header">
                    <h3>${this.formatTableName(table)} - ${this.getRecordTitle(table, record)}</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="record-view-container">
                        <!-- Profile Picture Section -->
                        <div class="profile-section">
                            <div class="profile-picture">
                                <div class="ascii-avatar">
┌─────────────┐
│             │
│    ┌───┐    │
│   ╱     ╲   │
│  │  ● ●  │  │
│  │   ▽   │  │
│   ╲ \_/ ╱   │
│    └───┘    │
│      │      │
│   ┌──┴──┐   │
│   │     │   │
│   └─────┘   │
└─────────────┘
                                </div>
                                <div class="profile-name">${this.getRecordTitle(table, record)}</div>
                            </div>
                            <div class="record-actions">
                                <button class="primary-button" id="edit-record-btn">
                                    <span class="edit-icon">✏️</span> Edit Record
                                </button>
                                <button class="primary-button" id="add-activity-btn">
                                    <span class="activity-icon">📝</span> Add Activity
                                </button>
                            </div>
                        </div>

                        <!-- Record Details Section -->
                        <div class="details-section">
                            <h4>Record Details</h4>
                            <div class="record-details-view" id="record-details">
                                ${this.formatEditableRecordDetails(table, record)}
                            </div>
                        </div>

                        <!-- Activity Log Section -->
                        <div class="activities-section">
                            <h4>Activity Log</h4>
                            <div class="activities-list" id="activities-list">
                                Loading activities...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(detailModal);

        // Set up event handlers
        this.setupRecordDetailHandlers(detailModal, table, record);

        // Load activities
        this.loadRecordActivities(table, record.id);

        } catch (error) {
            console.error('Error showing record detail view:', error);
            console.error('Error stack:', error.stack);
            this.ui.showDialog('Error', `Failed to show record details: ${error.message}`, 'error');
        }
    }

    getActivityTableName(recordTable) {
        const mapping = {
            'people': 'people_activities',
            'addresses': 'address_activities',
            'license_plates': 'vehicle_activities'
        };
        return mapping[recordTable] || 'activities';
    }

    getRecordTitle(table, record) {
        switch (table) {
            case 'people':
                return `${record.first_name || ''} ${record.last_name || ''}`.trim() || 'Unknown Person';
            case 'addresses':
                return record.street_address || 'Unknown Address';
            case 'license_plates':
                return `${record.plate_number || ''} (${record.province || ''})`.trim() || 'Unknown Vehicle';
            default:
                return `Record ${record.id}`;
        }
    }

    formatRecordDetails(table, record) {
        const details = [];

        Object.entries(record).forEach(([key, value]) => {
            if (key !== 'id' && value !== null && value !== undefined && value !== '') {
                const label = this.formatFieldLabel(key);
                const formattedValue = this.formatFieldValue(key, value);
                details.push(`
                    <div class="detail-row">
                        <span class="detail-label">${label}:</span>
                        <span class="detail-value">${formattedValue}</span>
                    </div>
                `);
            }
        });

        return details.join('');
    }

    formatEditableRecordDetails(table, record) {
        try {
            console.log('Formatting editable record details for:', table, record);

            const details = [];

            // Get schema for this table to determine field types
            if (!this.schema) {
                throw new Error('Schema manager not available');
            }

            const schema = this.schema.getTableSchema(table);
            if (!schema) {
                throw new Error(`Schema not found for table: ${table}`);
            }

            console.log('Schema loaded:', schema);

            if (!record || typeof record !== 'object') {
                throw new Error('Invalid record object');
            }

        Object.entries(record).forEach(([key, value]) => {
            if (key !== 'id' && key !== 'created_at' && key !== 'updated_at' && key !== 'created_by') {
                const label = this.formatFieldLabel(key);
                const fieldSchema = schema[key];
                const displayValue = this.formatFieldValue(key, value);

                details.push(`
                    <div class="detail-row" data-field="${key}">
                        <span class="detail-label">${label}:</span>
                        <span class="detail-value" data-original-value="${value || ''}">${displayValue || '<em>Not set</em>'}</span>
                        <span class="edit-controls" style="display: none;">
                            ${this.createInlineEditField(key, value, fieldSchema)}
                            <button class="save-field-btn" data-field="${key}">✓</button>
                            <button class="cancel-field-btn" data-field="${key}">✗</button>
                        </span>
                    </div>
                `);
            }
        });

        return details.join('');

        } catch (error) {
            console.error('Error formatting editable record details:', error);
            console.error('Error stack:', error.stack);
            return `<div class="error">Error loading record details: ${error.message}</div>`;
        }
    }

    createInlineEditField(fieldName, value, fieldSchema) {
        const fieldType = fieldSchema?.type || 'text';

        if (fieldType === 'boolean') {
            const checked = value ? 'checked' : '';
            return `<input type="checkbox" class="edit-input" data-field="${fieldName}" ${checked}>`;
        } else if (fieldType === 'date') {
            const dateValue = value ? this.ui.formatDateForInput(value) : '';
            return this.ui.createCustomDateInput(fieldName + '_edit', dateValue, '');
        } else if (fieldType === 'time') {
            return `<input type="time" class="edit-input" data-field="${fieldName}" value="${value || ''}">`;
        } else if (fieldName.includes('description') || fieldName.includes('notes')) {
            return `<textarea class="edit-input" data-field="${fieldName}" rows="2">${value || ''}</textarea>`;
        } else {
            return `<input type="text" class="edit-input" data-field="${fieldName}" value="${value || ''}">`;
        }
    }

    formatFieldLabel(fieldName) {
        return fieldName
            .replace(/_/g, ' ')
            .replace(/\b\w/g, l => l.toUpperCase());
    }

    formatFieldValue(fieldName, value) {
        // Handle null, undefined, or empty values
        if (value === null || value === undefined || value === '') {
            return '';
        }

        if (fieldName.includes('date') && value) {
            try {
                return new Date(value).toLocaleDateString();
            } catch {
                return value;
            }
        }

        if (fieldName.includes('time') && value) {
            return value;
        }

        if (typeof value === 'boolean') {
            return value ? 'Yes' : 'No';
        }

        // Safely convert to string
        return String(value);
    }

    setupRecordDetailHandlers(modal, table, record) {
        const editRecordBtn = modal.querySelector('#edit-record-btn');
        const addActivityBtn = modal.querySelector('#add-activity-btn');

        // Edit record button - toggles edit mode
        if (editRecordBtn) {
            editRecordBtn.addEventListener('click', () => {
                this.toggleEditMode(modal, table, record);
            });
        }

        // Add activity button
        if (addActivityBtn) {
            addActivityBtn.addEventListener('click', () => {
                this.showAddActivityForm(table, record);
            });
        }

        // Set up field-level edit handlers
        this.setupFieldEditHandlers(modal, table, record);
    }

    toggleEditMode(modal, table, record) {
        const editBtn = modal.querySelector('#edit-record-btn');
        const detailRows = modal.querySelectorAll('.detail-row');
        const isEditing = editBtn.textContent.includes('Cancel');

        if (isEditing) {
            // Cancel edit mode
            editBtn.innerHTML = '<span class="edit-icon">✏️</span> Edit Record';
            detailRows.forEach(row => {
                const valueSpan = row.querySelector('.detail-value');
                const editControls = row.querySelector('.edit-controls');
                if (valueSpan && editControls) {
                    valueSpan.style.display = 'inline';
                    editControls.style.display = 'none';
                }
            });
        } else {
            // Enter edit mode
            editBtn.innerHTML = '<span class="edit-icon">✗</span> Cancel Edit';
            detailRows.forEach(row => {
                const valueSpan = row.querySelector('.detail-value');
                const editControls = row.querySelector('.edit-controls');
                if (valueSpan && editControls) {
                    valueSpan.style.display = 'none';
                    editControls.style.display = 'inline-flex';
                }
            });
        }
    }

    setupFieldEditHandlers(modal, table, record) {
        // Save field buttons
        modal.addEventListener('click', async (e) => {
            if (e.target.classList.contains('save-field-btn')) {
                const fieldName = e.target.dataset.field;
                await this.saveFieldEdit(modal, table, record, fieldName);
            }

            if (e.target.classList.contains('cancel-field-btn')) {
                const fieldName = e.target.dataset.field;
                this.cancelFieldEdit(modal, fieldName);
            }
        });

        // Set up custom date inputs
        this.ui.setupCustomDateInputs(modal);
    }

    async saveFieldEdit(modal, table, record, fieldName) {
        try {
            const detailRow = modal.querySelector(`[data-field="${fieldName}"]`);
            const editInput = detailRow.querySelector('.edit-input');
            let newValue;

            if (editInput.type === 'checkbox') {
                newValue = editInput.checked;
            } else if (editInput.type === 'hidden' && fieldName.includes('date')) {
                // Custom date input
                newValue = editInput.value;
            } else {
                newValue = editInput.value;
            }

            // Update the record
            await this.data.update(table, record.id, { [fieldName]: newValue });

            // Update the display
            const valueSpan = detailRow.querySelector('.detail-value');
            const formattedValue = this.formatFieldValue(fieldName, newValue);
            valueSpan.textContent = formattedValue || 'Not set';
            valueSpan.dataset.originalValue = newValue;

            // Update the record object
            record[fieldName] = newValue;

            // Hide edit controls, show value
            const editControls = detailRow.querySelector('.edit-controls');
            valueSpan.style.display = 'inline';
            editControls.style.display = 'none';

            this.ui.showDialog('Success', 'Field updated successfully!', 'success');

        } catch (error) {
            console.error('Error saving field:', error);
            this.ui.showDialog('Error', `Failed to update field: ${error.message}`, 'error');
        }
    }

    cancelFieldEdit(modal, fieldName) {
        const detailRow = modal.querySelector(`[data-field="${fieldName}"]`);
        const valueSpan = detailRow.querySelector('.detail-value');
        const editControls = detailRow.querySelector('.edit-controls');
        const editInput = detailRow.querySelector('.edit-input');

        // Restore original value
        const originalValue = valueSpan.dataset.originalValue;
        if (editInput.type === 'checkbox') {
            editInput.checked = originalValue === 'true';
        } else {
            editInput.value = originalValue;
        }

        // Hide edit controls, show value
        valueSpan.style.display = 'inline';
        editControls.style.display = 'none';
    }

    async loadRecordActivities(table, recordId) {
        try {
            console.log(`Loading activities for ${table} record ${recordId}`);

            const activityTable = this.getActivityTableName(table);
            const foreignKey = this.getForeignKeyName(table);

            console.log(`Activity table: ${activityTable}, Foreign key: ${foreignKey}`);

            const activities = await this.data.search(activityTable, {
                [foreignKey]: recordId
            });

            console.log(`Found ${activities ? activities.length : 0} activities`);

            const activitiesList = document.getElementById('activities-list');
            if (!activitiesList) {
                console.error('Activities list element not found');
                return;
            }

            if (!activities || activities.length === 0) {
                activitiesList.innerHTML = '<div class="no-activities">No activities recorded yet.</div>';
                return;
            }

            // Sort activities by date (newest first)
            activities.sort((a, b) => {
                const dateA = new Date(a.activity_date + ' ' + (a.activity_time || '00:00'));
                const dateB = new Date(b.activity_date + ' ' + (b.activity_time || '00:00'));
                return dateB - dateA;
            });

            const activitiesHTML = activities.map(activity => this.formatActivity(activity)).join('');
            activitiesList.innerHTML = activitiesHTML;

            // Load supply provision details for supply provision activities
            const supplyActivities = activities.filter(activity => activity.activity_type === 'supply_provision');
            for (const activity of supplyActivities) {
                await this.loadSupplyProvisionDetails(activity.id);
            }

        } catch (error) {
            console.error('Error loading activities:', error);
            console.error('Error stack:', error.stack);
            const activitiesList = document.getElementById('activities-list');
            if (activitiesList) {
                activitiesList.innerHTML = `<div class="error">Failed to load activities: ${error.message}</div>`;
            }
        }
    }

    async loadSupplyProvisionDetails(activityId) {
        try {
            // Get supply provisions for this activity
            const provisions = await this.data.search('supply_provisions', {
                activity_id: activityId
            });

            if (!provisions || provisions.length === 0) {
                const suppliesElement = document.getElementById(`supplies-${activityId}`);
                if (suppliesElement) {
                    suppliesElement.innerHTML = `
                        <h5>📦 Supplies Provided:</h5>
                        <div class="no-supplies">No supply details found.</div>
                    `;
                }
                return;
            }

            // Get item details for each provision
            const suppliesWithDetails = [];
            for (const provision of provisions) {
                const item = await this.data.get('items', provision.item_id);
                if (item) {
                    suppliesWithDetails.push({
                        ...provision,
                        item_name: item.name,
                        unit_type: item.unit_type,
                        category: item.category
                    });
                }
            }

            // Generate supplies HTML
            const suppliesHTML = suppliesWithDetails.map(supply => `
                <li>
                    <span class="supply-quantity">${supply.quantity_provided}</span>
                    ${supply.unit_type} of
                    <strong>${supply.item_name}</strong>
                    <span class="supply-category">(${supply.category.replace('_', ' ')})</span>
                </li>
            `).join('');

            // Update the supplies element
            const suppliesElement = document.getElementById(`supplies-${activityId}`);
            if (suppliesElement) {
                suppliesElement.innerHTML = `
                    <h5>📦 Supplies Provided:</h5>
                    <ul class="supply-list">
                        ${suppliesHTML}
                    </ul>
                `;
            }

        } catch (error) {
            console.error('Error loading supply provision details:', error);
            const suppliesElement = document.getElementById(`supplies-${activityId}`);
            if (suppliesElement) {
                suppliesElement.innerHTML = `
                    <h5>📦 Supplies Provided:</h5>
                    <div class="error">Failed to load supply details.</div>
                `;
            }
        }
    }

    getForeignKeyName(table) {
        const mapping = {
            'people': 'person_id',
            'addresses': 'address_id',
            'license_plates': 'vehicle_id'
        };
        return mapping[table] || 'record_id';
    }

    formatActivity(activity) {
        const date = new Date(activity.activity_date).toLocaleDateString();
        const time = activity.activity_time || '';
        const priority = activity.priority ? `<span class="priority priority-${activity.priority}">${activity.priority.toUpperCase()}</span>` : '';

        return `
            <div class="activity-item">
                <div class="activity-header">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-meta">
                        <span class="activity-type">${activity.activity_type.replace('_', ' ').toUpperCase()}</span>
                        ${priority}
                        <span class="activity-date">${date} ${time}</span>
                    </div>
                </div>
                <div class="activity-content">
                    ${activity.description ? `<p>${activity.description}</p>` : ''}
                    ${activity.location ? `<div class="activity-location"><strong>Location:</strong> ${activity.location}</div>` : ''}
                    ${activity.outcome ? `<div class="activity-outcome"><strong>Outcome:</strong> ${activity.outcome}</div>` : ''}
                    ${activity.findings ? `<div class="activity-findings"><strong>Findings:</strong> ${activity.findings}</div>` : ''}
                    ${activity.action_taken ? `<div class="activity-action"><strong>Action Taken:</strong> ${activity.action_taken}</div>` : ''}
                    ${activity.activity_type === 'supply_provision' ? this.formatSupplyProvisionDetails(activity.id) : ''}
                </div>
                <div class="activity-footer">
                    <span class="activity-staff">By: ${activity.staff_member}</span>
                    ${activity.follow_up_required ? `<span class="follow-up-required">Follow-up required: ${activity.follow_up_date || 'TBD'}</span>` : ''}
                </div>
            </div>
        `;
    }

    formatSupplyProvisionDetails(activityId) {
        // This will be populated asynchronously
        return `<div class="activity-supplies" id="supplies-${activityId}">
            <h5>📦 Supplies Provided:</h5>
            <div class="loading-supplies">Loading supply details...</div>
        </div>`;
    }

    showAddActivityForm(table, record) {
        try {
            console.log(`Showing add activity form for ${table} record:`, record);

            const activityTable = this.getActivityTableName(table);
            const foreignKey = this.getForeignKeyName(table);

            console.log(`Activity table: ${activityTable}, Foreign key: ${foreignKey}`);

            // Get current user info
            const currentUser = this.auth.getCurrentUser();

            // Pre-fill some fields
            const defaultData = {
                [foreignKey]: record.id,
                activity_date: new Date().toISOString().split('T')[0], // Today's date
                staff_member: currentUser?.name || currentUser?.email || 'Unknown Staff',
                created_by: currentUser?.email || 'unknown'
            };

            console.log('Default data:', defaultData);

            // Check if schema is available
            if (!this.schema) {
                throw new Error('Schema manager not available');
            }

            // Generate form fields for the activity table, excluding system fields and foreign keys
            const excludeFields = ['id', 'created_at', 'created_by', foreignKey];
            const fields = this.schema.generateFormFields(activityTable, excludeFields);

            // Set default values in the fields
            fields.forEach(field => {
                if (defaultData[field.name] !== undefined) {
                    field.value = defaultData[field.name];
                }
            });

            console.log(`Generated ${fields.length} fields for activity form`);

            // Create and show the form
            this.ui.showFullScreenForm(
                `Add Activity - ${this.getRecordTitle(table, record)}`,
                fields,
                async (formData) => {
                    try {
                        // Check if this is a supply provision activity
                        if (formData.activity_type === 'supply_provision') {
                            const result = await this.handleSupplyProvisionActivity(table, record, formData, defaultData);
                            return result;
                        }

                        // Merge default data with form data
                        const activityData = { ...defaultData, ...formData };

                        await this.data.insert(activityTable, activityData);
                        this.ui.showDialog('Success', 'Activity added successfully!', 'success');

                        // Reload activities list
                        this.loadRecordActivities(table, record.id);

                        return true;
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to add activity: ${error.message}`, 'error');
                        return false;
                    }
                }
            );
        } catch (error) {
            console.error('Error showing add activity form:', error);
            this.ui.showDialog('Error', `Failed to show activity form: ${error.message}`, 'error');
        }
    }

    async handleSupplyProvisionActivity(table, record, formData, defaultData) {
        try {
            // Show supply selection form
            return await this.showSupplySelectionForm(table, record, formData, defaultData);
        } catch (error) {
            console.error('Error handling supply provision activity:', error);
            this.ui.showDialog('Error', `Failed to handle supply provision: ${error.message}`, 'error');
            return false;
        }
    }

    async showSupplySelectionForm(table, record, activityData, defaultData) {
        try {
            // Get available items
            const items = await this.data.search('items', { active: true });

            if (items.length === 0) {
                this.ui.showDialog('No Items Available', 'No active items found in inventory. Please add items first.', 'warning');
                return false;
            }

            // Create supply selection modal
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';

            const itemsHTML = items.map(item => `
                <div class="supply-item" data-item-id="${item.id}">
                    <div class="supply-item-header">
                        <div class="supply-item-name">${item.name}</div>
                        <div class="supply-item-stock">Stock: ${item.current_stock} ${item.unit_type}</div>
                    </div>
                    <div class="supply-item-description">${item.description || ''}</div>
                    <div class="supply-item-category">${item.category.replace('_', ' ').toUpperCase()}</div>
                    <div class="supply-item-controls">
                        <label>Quantity:</label>
                        <input type="number" class="quantity-input" min="0" max="${item.current_stock}" value="0" data-item-id="${item.id}">
                        <span class="unit-label">${item.unit_type}</span>
                    </div>
                </div>
            `).join('');

            modal.innerHTML = `
                <div class="modal-dialog large-modal">
                    <div class="modal-header">
                        <h3>Supply Provision - ${this.getRecordTitle(table, record)}</h3>
                        <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="activity-summary">
                            <p><strong>Activity:</strong> ${activityData.title}</p>
                            <p><strong>Date:</strong> ${activityData.activity_date}</p>
                            <p><strong>Staff:</strong> ${activityData.staff_member}</p>
                            ${activityData.description ? `<p><strong>Description:</strong> ${activityData.description}</p>` : ''}
                        </div>
                        <hr>
                        <h4>Select Items to Provide:</h4>
                        <div class="supply-items-list">
                            ${itemsHTML}
                        </div>
                        <div class="supply-notes">
                            <label for="supply-notes">Additional Notes:</label>
                            <textarea id="supply-notes" placeholder="Any additional notes about the supplies provided..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                        <button class="primary-button" id="save-supply-provision">Save Supply Provision</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Handle save button
            const saveButton = modal.querySelector('#save-supply-provision');
            saveButton.addEventListener('click', async () => {
                await this.saveSupplyProvision(modal, table, record, activityData, defaultData);
            });

            return true;
        } catch (error) {
            console.error('Error showing supply selection form:', error);
            this.ui.showDialog('Error', `Failed to show supply selection: ${error.message}`, 'error');
            return false;
        }
    }

    async saveSupplyProvision(modal, table, record, activityData, defaultData) {
        try {
            // Get all quantity inputs
            const quantityInputs = modal.querySelectorAll('.quantity-input');
            const selectedItems = [];

            quantityInputs.forEach(input => {
                const quantity = parseInt(input.value) || 0;
                if (quantity > 0) {
                    selectedItems.push({
                        item_id: input.dataset.itemId,
                        quantity: quantity
                    });
                }
            });

            if (selectedItems.length === 0) {
                this.ui.showDialog('No Items Selected', 'Please select at least one item with a quantity greater than 0.', 'warning');
                return;
            }

            // Get additional notes
            const notes = modal.querySelector('#supply-notes').value;

            // Create the activity record first
            const activityTable = this.getActivityTableName(table);
            const mergedActivityData = { ...defaultData, ...activityData };

            // Add supply provision specific notes
            if (notes) {
                mergedActivityData.description = (mergedActivityData.description || '') +
                    (mergedActivityData.description ? '\n\nSupply Notes: ' : 'Supply Notes: ') + notes;
            }

            const activityResult = await this.data.insert(activityTable, mergedActivityData);
            const activityId = activityResult.id;

            // Create supply provision records and update inventory
            for (const item of selectedItems) {
                // Create supply provision record
                await this.data.insert('supply_provisions', {
                    activity_id: activityId,
                    item_id: item.item_id,
                    quantity_provided: item.quantity,
                    notes: notes,
                    created_at: new Date().toISOString()
                });

                // Update item stock
                const currentItem = await this.data.get('items', item.item_id);
                const newStock = currentItem.current_stock - item.quantity;

                await this.data.update('items', item.item_id, {
                    current_stock: newStock,
                    updated_at: new Date().toISOString()
                });
            }

            // Close modal and show success
            modal.remove();
            this.ui.showDialog('Success',
                `Supply provision activity created successfully! ${selectedItems.length} item(s) provided.`,
                'success'
            );

            // Reload activities list
            this.loadRecordActivities(table, record.id);

            return true;
        } catch (error) {
            console.error('Error saving supply provision:', error);
            this.ui.showDialog('Error', `Failed to save supply provision: ${error.message}`, 'error');
            return false;
        }
    }


}

// Search Incidents Command
class SearchIncidentsCommand extends BaseCommand {
    async execute(args) {
        return new Promise((resolve) => {
            this.showIncidentSearchInterface(resolve);
        });
    }

    showIncidentSearchInterface(resolve) {
        // Create search modal
        const searchModal = document.createElement('div');
        searchModal.className = 'modal-overlay';

        searchModal.innerHTML = `
            <div class="modal-dialog large-modal">
                <div class="modal-header">
                    <h3>🔍 Search Incidents</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="incident-search-field">Search Field:</label>
                                <select id="incident-search-field" class="form-control">
                                    <option value="all">All Fields</option>
                                    <option value="incident_number">Incident Number</option>
                                    <option value="description">Description</option>
                                    <option value="location">Location</option>
                                    <option value="incident_type">Incident Type</option>
                                    <option value="people_involved">People Involved</option>
                                    <option value="status">Status</option>
                                    <option value="reported_by">Reported By</option>
                                    <option value="police_file_number">Police File Number</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="incident-search-query">Search Term:</label>
                                <input type="text" id="incident-search-query" class="form-control" placeholder="Enter search term...">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="incident-status-filter">Status Filter:</label>
                                <select id="incident-status-filter" class="form-control">
                                    <option value="all">All Statuses</option>
                                    <option value="open">Open</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="resolved">Resolved</option>
                                    <option value="closed">Closed</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="incident-date-from">Date From:</label>
                                <input type="date" id="incident-date-from" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="incident-date-to">Date To:</label>
                                <input type="date" id="incident-date-to" class="form-control">
                            </div>
                        </div>
                        <div class="form-actions">
                            <button id="incident-search-submit" class="primary-button">🔍 Search Incidents</button>
                            <button id="incident-search-clear" class="secondary-button">Clear</button>
                        </div>
                    </div>
                    <div id="incident-search-results" class="search-results" style="display: none;">
                        <div class="results-header">
                            <h4>Search Results</h4>
                            <div class="results-count"></div>
                        </div>
                        <div class="results-container"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Close</button>
                </div>
            </div>
        `;

        document.body.appendChild(searchModal);

        // Get form elements
        const searchField = document.getElementById('incident-search-field');
        const searchQuery = document.getElementById('incident-search-query');
        const statusFilter = document.getElementById('incident-status-filter');
        const dateFrom = document.getElementById('incident-date-from');
        const dateTo = document.getElementById('incident-date-to');
        const searchSubmit = document.getElementById('incident-search-submit');
        const searchClear = document.getElementById('incident-search-clear');
        const searchResults = document.getElementById('incident-search-results');
        const resultsContainer = searchResults.querySelector('.results-container');
        const resultsCount = searchResults.querySelector('.results-count');

        // Handle search submission
        const performSearch = async () => {
            const field = searchField.value;
            const query = searchQuery.value.trim();
            const status = statusFilter.value;
            const fromDate = dateFrom.value;
            const toDate = dateTo.value;

            try {
                searchSubmit.disabled = true;
                searchSubmit.textContent = '🔍 Searching...';

                const results = await this.performIncidentSearch(field, query, status, fromDate, toDate);
                this.displayIncidentResults(results, resultsContainer, resultsCount);
                searchResults.style.display = 'block';

            } catch (error) {
                this.ui.showDialog('Search Error', `Search failed: ${error.message}`, 'error');
            } finally {
                searchSubmit.disabled = false;
                searchSubmit.textContent = '🔍 Search Incidents';
            }
        };

        // Handle clear
        const clearSearch = () => {
            searchQuery.value = '';
            statusFilter.value = 'all';
            dateFrom.value = '';
            dateTo.value = '';
            searchField.value = 'all';
            searchResults.style.display = 'none';
        };

        // Event listeners
        searchSubmit.addEventListener('click', performSearch);
        searchClear.addEventListener('click', clearSearch);

        // Enter key to search
        searchQuery.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Auto-focus search input
        setTimeout(() => searchQuery.focus(), 100);

        // Handle modal close
        searchModal.addEventListener('click', (e) => {
            if (e.target === searchModal) {
                searchModal.remove();
                resolve(null);
            }
        });
    }

    async performIncidentSearch(field, query, status, fromDate, toDate) {
        try {
            // Get all incidents first
            let incidents = await this.data.search('incidents', {});

            if (!incidents || incidents.length === 0) {
                return [];
            }

            // Apply text search filter
            if (query) {
                const lowerQuery = query.toLowerCase();

                if (field === 'all') {
                    // Search across all text fields
                    incidents = incidents.filter(incident => {
                        return Object.entries(incident).some(([key, value]) => {
                            if (typeof value === 'string') {
                                return value.toLowerCase().includes(lowerQuery);
                            }
                            return false;
                        });
                    });
                } else {
                    // Search specific field
                    incidents = incidents.filter(incident => {
                        const fieldValue = incident[field];
                        if (typeof fieldValue === 'string') {
                            return fieldValue.toLowerCase().includes(lowerQuery);
                        }
                        return false;
                    });
                }
            }

            // Apply status filter
            if (status !== 'all') {
                incidents = incidents.filter(incident =>
                    (incident.status || 'open').toLowerCase() === status.toLowerCase()
                );
            }

            // Apply date range filter
            if (fromDate || toDate) {
                incidents = incidents.filter(incident => {
                    const incidentDate = incident.incident_date;
                    if (!incidentDate) return false;

                    const date = new Date(incidentDate);
                    const from = fromDate ? new Date(fromDate) : null;
                    const to = toDate ? new Date(toDate) : null;

                    if (from && date < from) return false;
                    if (to && date > to) return false;

                    return true;
                });
            }

            // Sort by date (newest first)
            incidents.sort((a, b) => {
                const dateA = new Date(a.incident_date || a.created_at);
                const dateB = new Date(b.incident_date || b.created_at);
                return dateB - dateA;
            });

            return incidents;

        } catch (error) {
            console.error('Error performing incident search:', error);
            throw new Error(`Search failed: ${error.message}`);
        }
    }

    displayIncidentResults(incidents, container, countElement) {
        if (!incidents || incidents.length === 0) {
            container.innerHTML = '<div class="no-results">No incidents found matching your search criteria.</div>';
            countElement.textContent = '0 incidents found';
            return;
        }

        countElement.textContent = `${incidents.length} incident${incidents.length === 1 ? '' : 's'} found`;

        const resultsHTML = incidents.map(incident => {
            const statusClass = this.getStatusClass(incident.status);
            const priorityClass = this.getPriorityClass(incident.priority);

            return `
                <div class="incident-result-item" data-incident-id="${incident.id}">
                    <div class="incident-result-header">
                        <div class="incident-number">${incident.incident_number || 'N/A'}</div>
                        <div class="incident-badges">
                            <span class="status-badge ${statusClass}">${incident.status || 'Open'}</span>
                            ${incident.priority ? `<span class="priority-badge ${priorityClass}">${incident.priority}</span>` : ''}
                        </div>
                    </div>
                    <div class="incident-result-content">
                        <div class="incident-description">
                            <strong>Description:</strong> ${incident.description || 'No description'}
                        </div>
                        <div class="incident-details">
                            <div class="detail-item">
                                <span class="detail-label">📅 Date:</span>
                                <span class="detail-value">${this.formatDate(incident.incident_date)} ${incident.incident_time || ''}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">📍 Location:</span>
                                <span class="detail-value">${incident.location || 'Not specified'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">🏷️ Type:</span>
                                <span class="detail-value">${incident.incident_type || 'Not specified'}</span>
                            </div>
                            ${incident.people_involved ? `
                                <div class="detail-item">
                                    <span class="detail-label">👥 People:</span>
                                    <span class="detail-value">${incident.people_involved}</span>
                                </div>
                            ` : ''}
                            ${incident.police_file_number ? `
                                <div class="detail-item">
                                    <span class="detail-label">🚔 Police File:</span>
                                    <span class="detail-value">${incident.police_file_number}</span>
                                </div>
                            ` : ''}
                            <div class="detail-item">
                                <span class="detail-label">👤 Reported By:</span>
                                <span class="detail-value">${incident.reported_by || 'Unknown'}</span>
                            </div>
                        </div>
                    </div>
                    <div class="incident-result-actions">
                        <button class="action-button view-incident" data-incident-id="${incident.id}">👁️ View Details</button>
                        <button class="action-button edit-incident" data-incident-id="${incident.id}">✏️ Edit</button>
                        ${incident.status !== 'closed' ? `<button class="action-button close-incident" data-incident-id="${incident.id}">✅ Close</button>` : ''}
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = resultsHTML;

        // Add event listeners for action buttons
        this.attachIncidentActionListeners(container);
    }

    attachIncidentActionListeners(container) {
        // View incident details
        container.querySelectorAll('.view-incident').forEach(button => {
            button.addEventListener('click', async (e) => {
                const incidentId = e.target.dataset.incidentId;
                await this.viewIncidentDetails(incidentId);
            });
        });

        // Edit incident
        container.querySelectorAll('.edit-incident').forEach(button => {
            button.addEventListener('click', async (e) => {
                const incidentId = e.target.dataset.incidentId;
                await this.editIncident(incidentId);
            });
        });

        // Close incident
        container.querySelectorAll('.close-incident').forEach(button => {
            button.addEventListener('click', async (e) => {
                const incidentId = e.target.dataset.incidentId;
                await this.closeIncident(incidentId);
            });
        });
    }

    async viewIncidentDetails(incidentId) {
        try {
            const incidents = await this.data.search('incidents', { id: incidentId });
            const incident = incidents[0];

            if (!incident) {
                this.ui.showDialog('Error', 'Incident not found.', 'error');
                return;
            }

            const detailsHTML = `
                <div class="incident-details-view">
                    <h4>Incident ${incident.incident_number}</h4>
                    <div class="details-grid">
                        <div class="detail-row">
                            <strong>Date/Time:</strong> ${this.formatDate(incident.incident_date)} ${incident.incident_time || ''}
                        </div>
                        <div class="detail-row">
                            <strong>Location:</strong> ${incident.location}
                        </div>
                        <div class="detail-row">
                            <strong>Type:</strong> ${incident.incident_type || 'Not specified'}
                        </div>
                        <div class="detail-row">
                            <strong>Status:</strong> ${incident.status || 'Open'}
                        </div>
                        <div class="detail-row">
                            <strong>Description:</strong><br>${incident.description}
                        </div>
                        ${incident.people_involved ? `
                            <div class="detail-row">
                                <strong>People Involved:</strong><br>${incident.people_involved}
                            </div>
                        ` : ''}
                        ${incident.police_notified ? `
                            <div class="detail-row">
                                <strong>Police Notified:</strong> Yes
                                ${incident.police_file_number ? `<br><strong>File Number:</strong> ${incident.police_file_number}` : ''}
                            </div>
                        ` : ''}
                        ${incident.follow_up_required ? `
                            <div class="detail-row">
                                <strong>Follow-up Required:</strong> Yes
                                ${incident.follow_up_notes ? `<br><strong>Notes:</strong> ${incident.follow_up_notes}` : ''}
                            </div>
                        ` : ''}
                        <div class="detail-row">
                            <strong>Reported By:</strong> ${incident.reported_by || 'Unknown'}
                        </div>
                        <div class="detail-row">
                            <strong>Created:</strong> ${this.formatDateTime(incident.created_at)}
                        </div>
                    </div>
                </div>
            `;

            this.ui.showDialog(`Incident Details - ${incident.incident_number}`, detailsHTML, 'info');

        } catch (error) {
            this.ui.showDialog('Error', `Failed to load incident details: ${error.message}`, 'error');
        }
    }

    async editIncident(incidentId) {
        try {
            const incidents = await this.data.search('incidents', { id: incidentId });
            const incident = incidents[0];

            if (!incident) {
                this.ui.showDialog('Error', 'Incident not found.', 'error');
                return;
            }

            // Generate form fields for editing
            const fields = this.data.schema.generateFormFields('incidents');

            // Pre-populate with existing data
            Object.keys(fields).forEach(fieldName => {
                if (incident[fieldName] !== undefined) {
                    fields[fieldName].value = incident[fieldName];
                }
            });

            this.ui.showForm(`Edit Incident ${incident.incident_number}`, fields, async (formData) => {
                try {
                    // Update the incident
                    await this.data.update('incidents', incidentId, formData);

                    this.ui.showDialog(
                        'Incident Updated',
                        `Incident ${incident.incident_number} has been successfully updated.`,
                        'success'
                    );

                    // Refresh the search results
                    // Note: In a real implementation, you might want to refresh the current search

                } catch (error) {
                    this.ui.showDialog('Error', `Failed to update incident: ${error.message}`, 'error');
                }
            });

        } catch (error) {
            this.ui.showDialog('Error', `Failed to load incident for editing: ${error.message}`, 'error');
        }
    }

    async closeIncident(incidentId) {
        try {
            const incidents = await this.data.search('incidents', { id: incidentId });
            const incident = incidents[0];

            if (!incident) {
                this.ui.showDialog('Error', 'Incident not found.', 'error');
                return;
            }

            // Confirm closure
            const confirmed = confirm(`Are you sure you want to close incident ${incident.incident_number}?`);
            if (!confirmed) return;

            // Update status to closed
            await this.data.update('incidents', incidentId, {
                status: 'closed',
                closed_at: new Date().toISOString(),
                closed_by: this.auth.getCurrentUser()?.email
            });

            this.ui.showDialog(
                'Incident Closed',
                `Incident ${incident.incident_number} has been closed.`,
                'success'
            );

            // Remove the incident from the current view
            const incidentElement = document.querySelector(`[data-incident-id="${incidentId}"]`);
            if (incidentElement) {
                incidentElement.remove();
            }

        } catch (error) {
            this.ui.showDialog('Error', `Failed to close incident: ${error.message}`, 'error');
        }
    }

    getStatusClass(status) {
        const statusMap = {
            'open': 'status-open',
            'in_progress': 'status-progress',
            'resolved': 'status-resolved',
            'closed': 'status-closed'
        };
        return statusMap[status?.toLowerCase()] || 'status-open';
    }

    getPriorityClass(priority) {
        const priorityMap = {
            'low': 'priority-low',
            'medium': 'priority-medium',
            'high': 'priority-high',
            'urgent': 'priority-urgent'
        };
        return priorityMap[priority?.toLowerCase()] || 'priority-medium';
    }

    formatDate(dateString) {
        if (!dateString) return 'Not specified';
        try {
            return new Date(dateString).toLocaleDateString();
        } catch {
            return dateString;
        }
    }

    formatDateTime(dateString) {
        if (!dateString) return 'Not specified';
        try {
            return new Date(dateString).toLocaleString();
        } catch {
            return dateString;
        }
    }
}

class AddAddressCommand extends BaseCommand {
    async execute(args) {
        // Generate fields from schema
        const fields = this.data.schema.generateFormFields('addresses');

        return new Promise((resolve) => {
            this.ui.showFullScreenForm('Add Address Record', fields, async (formData) => {
                try {
                    // Convert form data to database format
                    const dbData = this.data.schema.convertFormToDatabase('addresses', formData);

                    const addressData = {
                        ...dbData,
                        created_by: this.auth.getCurrentUser()?.email,
                        created_at: new Date().toISOString()
                    };

                    const result = await this.data.insert('addresses', addressData);

                    this.ui.showDialog(
                        'Address Added',
                        `Address record for ${formData.street_address} has been created.`,
                        'success'
                    );

                    resolve(result);
                    return true; // Indicate success for form navigation
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to add address: ${error.message}`, 'error');
                    resolve(null);
                    return false; // Indicate failure to stay on form
                }
            });
        });
    }
}

class AddPlateCommand extends BaseCommand {
    async execute(args) {
        // Generate fields from schema
        const fields = this.data.schema.generateFormFields('license_plates');

        return new Promise((resolve) => {
            this.ui.showFullScreenForm('Add License Plate Record', fields, async (formData) => {
                try {
                    // Convert form data to database format
                    const dbData = this.data.schema.convertFormToDatabase('license_plates', formData);

                    const plateData = {
                        ...dbData,
                        created_by: this.auth.getCurrentUser()?.email,
                        created_at: new Date().toISOString()
                    };

                    const result = await this.data.insert('license_plates', plateData);

                    this.ui.showDialog(
                        'License Plate Added',
                        `License plate record for ${formData.plate_number} has been created.`,
                        'success'
                    );

                    resolve(result);
                    return true; // Indicate success for form navigation
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to add license plate: ${error.message}`, 'error');
                    resolve(null);
                    return false; // Indicate failure to stay on form
                }
            });
        });
    }
}

class GenerateReportCommand extends BaseCommand {
    async execute(args) {
        this.ui.showDialog('Generate Report', 'Report generation functionality coming soon!', 'info');
    }
}

class ExportDataCommand extends BaseCommand {
    async execute(args) {
        this.ui.showDialog('Export Data', 'Data export functionality coming soon!', 'info');
    }
}

class SyncDataCommand extends BaseCommand {
    async execute(args) {
        try {
            await this.data.syncPendingData();
            this.ui.showDialog('Sync Complete', 'Data synchronization completed successfully.', 'success');
        } catch (error) {
            this.ui.showDialog('Sync Error', `Failed to sync data: ${error.message}`, 'error');
        }
    }
}

class SettingsCommand extends BaseCommand {
    async execute(args) {
        return new Promise((resolve) => {
            this.showSettingsMenu(resolve);
        });
    }

    showSettingsMenu(resolve) {
        const settingsModal = document.createElement('div');
        settingsModal.className = 'modal-overlay';

        settingsModal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>System Settings</h3>
                </div>
                <div class="modal-body">
                    <div class="settings-menu">
                        <div class="menu-item" data-action="seed-data">
                            <div class="menu-title">Add Sample Data</div>
                            <div class="menu-desc">Add sample records for testing search functionality</div>
                        </div>
                        <div class="menu-item" data-action="clear-data">
                            <div class="menu-title">Clear All Data</div>
                            <div class="menu-desc">Remove all local records (use with caution)</div>
                        </div>
                        <div class="menu-item" data-action="sync-status">
                            <div class="menu-title">Sync Status</div>
                            <div class="menu-desc">Check data synchronization status</div>
                        </div>
                        <div class="menu-item" data-action="cleanup-duplicates">
                            <div class="menu-title">Clean Up Duplicates</div>
                            <div class="menu-desc">Remove duplicate records from local storage</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="primary-button" onclick="this.closest('.modal-overlay').remove()">Close</button>
                </div>
            </div>
        `;

        document.body.appendChild(settingsModal);

        // Handle menu clicks
        settingsModal.addEventListener('click', async (e) => {
            const menuItem = e.target.closest('.menu-item');
            if (menuItem) {
                const action = menuItem.dataset.action;

                switch (action) {
                    case 'seed-data':
                        await this.seedSampleData();
                        break;
                    case 'clear-data':
                        await this.clearAllData();
                        break;
                    case 'sync-status':
                        this.showSyncStatus();
                        break;
                    case 'cleanup-duplicates':
                        await this.cleanupDuplicates();
                        break;
                }
            }
        });

        resolve(null);
    }

    async seedSampleData() {
        try {
            this.ui.setStatus('Adding sample data...');

            // Sample people
            const samplePeople = [
                {
                    first_name: 'John',
                    last_name: 'Doe',
                    email: '<EMAIL>',
                    phone: '555-0123',
                    housing_status: 'Housed',
                    notes: 'Sample person record'
                },
                {
                    first_name: 'Jane',
                    last_name: 'Smith',
                    email: '<EMAIL>',
                    phone: '555-0456',
                    housing_status: 'Temporarily Housed',
                    emergency_contact: 'Mary Smith',
                    emergency_contact_phone: '555-0789'
                }
            ];

            // Sample addresses
            const sampleAddresses = [
                {
                    street_address: '123 Main Street',
                    city: 'Calgary',
                    province: 'AB',
                    postal_code: 'T2P 1A1',
                    country: 'Canada'
                },
                {
                    street_address: '456 Oak Avenue',
                    city: 'Edmonton',
                    province: 'AB',
                    postal_code: 'T5K 2B2',
                    country: 'Canada'
                }
            ];

            // Sample license plates
            const samplePlates = [
                {
                    plate_number: 'ABC123',
                    province: 'AB',
                    vehicle_make: 'Toyota',
                    vehicle_model: 'Camry',
                    vehicle_year: 2020,
                    vehicle_color: 'Blue',
                    owner_name: 'John Doe'
                },
                {
                    plate_number: 'XYZ789',
                    province: 'BC',
                    vehicle_make: 'Honda',
                    vehicle_model: 'Civic',
                    vehicle_year: 2019,
                    vehicle_color: 'Red',
                    owner_name: 'Jane Smith'
                }
            ];

            // Insert sample data
            for (const person of samplePeople) {
                await this.data.insert('people', person);
            }

            for (const address of sampleAddresses) {
                await this.data.insert('addresses', address);
            }

            for (const plate of samplePlates) {
                await this.data.insert('license_plates', plate);
            }

            this.ui.showDialog('Success', 'Sample data added successfully! You can now test the search functionality.', 'success');
            this.ui.setStatus('Ready');

        } catch (error) {
            this.ui.showDialog('Error', `Failed to add sample data: ${error.message}`, 'error');
            this.ui.setStatus('Error');
        }
    }

    async clearAllData() {
        const confirmed = confirm('Are you sure you want to clear all local data? This cannot be undone.');
        if (confirmed) {
            try {
                // Clear SQLite cache if available
                if (this.app.data.sqlite) {
                    const cacheTables = [
                        'cache_people', 'cache_pets', 'cache_incidents',
                        'cache_addresses', 'cache_bikes', 'cache_encampments',
                        'cache_media', 'cache_items', 'cache_organizations'
                    ];
                    for (const table of cacheTables) {
                        this.app.data.sqlite.clear(table);
                    }
                }

                // Clear memory cache
                this.app.data.cache.clear();

                this.ui.showDialog('Success', 'All local data cleared.', 'success');
            } catch (error) {
                this.ui.showDialog('Error', `Failed to clear data: ${error.message}`, 'error');
            }
        }
    }

    showSyncStatus() {
        const stats = this.data.getStats();

        // Get sync queue from SQLite or memory
        let syncQueueLength = 0;
        if (this.app.data.sqlite) {
            const syncQueue = this.app.data.sqlite.getAll('sync_queue') || [];
            syncQueueLength = syncQueue.filter(item => item.status === 'pending').length;
        } else if (this.app.data.syncQueue) {
            syncQueueLength = this.app.data.syncQueue.length;
        }

        const statusText = `
Network Status: ${stats.isOnline ? 'Online' : 'Offline'}
Cache Size: ${stats.cacheSize} items
Pending Sync: ${syncQueueLength} operations

Local Records:
- People: ${this.getLocalRecordCount('people')}
- Addresses: ${this.getLocalRecordCount('addresses')}
- Pets: ${this.getLocalRecordCount('pets')}
- Incidents: ${this.getLocalRecordCount('incidents')}
- Bikes: ${this.getLocalRecordCount('bikes')}
        `.trim();

        this.ui.showDialog('Sync Status', statusText, 'info');
    }

    getLocalRecordCount(table) {
        try {
            if (this.app.data.sqlite) {
                const data = this.app.data.sqlite.getAll(table);
                return data ? data.length : 0;
            } else {
                // Fallback to memory cache count
                return Array.from(this.app.data.cache.values())
                    .filter(item => item.table === table).length;
            }
        } catch {
            return 0;
        }
    }

    async cleanupDuplicates() {
        try {
            this.ui.setStatus('Cleaning up duplicates...');

            const tables = ['people', 'addresses', 'license_plates', 'incidents'];
            let totalCleaned = 0;

            for (const table of tables) {
                const beforeCount = this.getLocalRecordCount(table);
                this.data.deduplicateRecords(table);
                const afterCount = this.getLocalRecordCount(table);
                const cleaned = beforeCount - afterCount;
                totalCleaned += cleaned;

                if (cleaned > 0) {
                    console.log(`Cleaned ${cleaned} duplicates from ${table}`);
                }
            }

            if (totalCleaned > 0) {
                this.ui.showDialog('Cleanup Complete', `Removed ${totalCleaned} duplicate records.`, 'success');
            } else {
                this.ui.showDialog('Cleanup Complete', 'No duplicate records found.', 'info');
            }

            this.ui.setStatus('Ready');

        } catch (error) {
            this.ui.showDialog('Cleanup Error', `Failed to clean up duplicates: ${error.message}`, 'error');
            this.ui.setStatus('Error');
        }
    }
}

class AboutCommand extends BaseCommand {
    async execute(args) {
        const user = this.auth.getCurrentUser();
        const stats = this.data.getStats();

        // Get current version dynamically
        const currentVersion = await window.electronAPI.invoke('app-version');

        const aboutText = `
S.T.E.V.I Retro v${currentVersion}
Supportive Technology to Enable Vulnerable Individuals - Retro Interface

Current User: ${user?.email || 'Not logged in'}
Cache Size: ${stats.cacheSize} items
Online Status: ${stats.isOnline ? 'Connected' : 'Offline'}
Pending Sync: ${stats.pendingSync} operations

© 2024 I.H.A.R.C. All rights reserved.
        `.trim();

        this.ui.showDialog('About S.T.E.V.I Retro', aboutText, 'info');
    }
}

class EmergencyContactsCommand extends BaseCommand {
    async execute(args) {
        const emergencyContacts = `
🚑 EMERGENCY CONTACTS

Emergency Services: 911

Crisis Lines:
• Crisis & Suicide Line: 1-************
• Mental Health Helpline: 1-************
• Addiction Helpline: 1-************

Shelters:
• Emergency Shelter: (*************
• Women's Shelter: (*************
• Youth Shelter: (*************

Medical:
• Mobile Medical Unit: (*************
• Detox Center: (*************
• Harm Reduction: (*************

Support Services:
• Food Bank: (*************
• Clothing Depot: (*************
• Transportation: (*************

IHARC Dispatch: (*************
        `.trim();

        this.ui.showDialog('Emergency Contacts', emergencyContacts, 'info');
    }
}

class UserProfileCommand extends BaseCommand {
    async execute(args) {
        this.showUserProfile();
    }

    showUserProfile() {
        const currentUser = this.auth.getCurrentUser();

        if (!currentUser) {
            this.ui.showDialog('Error', 'No user session found', 'error');
            return;
        }

        const profileModal = document.createElement('div');
        profileModal.className = 'modal-overlay';

        profileModal.innerHTML = `
            <div class="modal-dialog user-profile-modal">
                <div class="modal-header">
                    <h3>👤 User Profile</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="profile-container">
                        <!-- Profile Information -->
                        <div class="profile-info-section">
                            <h4>Profile Information</h4>
                            <div class="profile-details">
                                <div class="profile-field">
                                    <label>Email:</label>
                                    <span class="field-value">${currentUser.email || 'Not available'}</span>
                                </div>
                                <div class="profile-field">
                                    <label>Name:</label>
                                    <span class="field-value" id="user-name">${currentUser.name || currentUser.user_metadata?.name || 'Not set'}</span>
                                    <button class="edit-field-btn" data-field="name">Edit</button>
                                </div>
                                <div class="profile-field">
                                    <label>IHARC Staff ID:</label>
                                    <span class="field-value" id="user-staff-id">${currentUser.iharc_staff_id || 'Not assigned'}</span>
                                    ${currentUser.role === 'iharc_admin' ? '<button class="edit-field-btn" data-field="staff_id">Edit</button>' : '<span class="field-note">(Contact admin to update)</span>'}
                                </div>
                                <div class="profile-field">
                                    <label>Ranger ID:</label>
                                    <span class="field-value" id="user-ranger-id">${currentUser.ranger_id || 'Not assigned'}</span>
                                    ${currentUser.role === 'iharc_admin' ? '<button class="edit-field-btn" data-field="ranger_id">Edit</button>' : '<span class="field-note">(Contact admin to update)</span>'}
                                </div>
                                <div class="profile-field">
                                    <label>Phone:</label>
                                    <span class="field-value" id="user-phone">${currentUser.phone || currentUser.user_metadata?.phone || 'Not set'}</span>
                                    <button class="edit-field-btn" data-field="phone">Edit</button>
                                </div>
                                <div class="profile-field">
                                    <label>Department:</label>
                                    <span class="field-value" id="user-department">${currentUser.department || currentUser.user_metadata?.department || 'Not set'}</span>
                                    <button class="edit-field-btn" data-field="department">Edit</button>
                                </div>
                                <div class="profile-field">
                                    <label>Role:</label>
                                    <span class="field-value">${currentUser.role || currentUser.user_metadata?.role || 'Outreach Staff'}</span>
                                </div>
                                <div class="profile-field">
                                    <label>Last Login:</label>
                                    <span class="field-value">${this.formatDate(currentUser.last_sign_in_at)}</span>
                                </div>
                                <div class="profile-field">
                                    <label>Account Created:</label>
                                    <span class="field-value">${this.formatDate(currentUser.created_at)}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Security Section -->
                        <div class="security-section">
                            <h4>Security</h4>
                            <div class="security-actions">
                                <button class="action-button" id="change-password-btn">
                                    <span class="action-icon">🔒</span>
                                    <span class="action-text">Change Password</span>
                                </button>
                                <button class="action-button" id="reset-password-btn">
                                    <span class="action-icon">📧</span>
                                    <span class="action-text">Reset Password via Email</span>
                                </button>
                            </div>
                        </div>

                        <!-- Session Information -->
                        <div class="session-section">
                            <h4>Session Information</h4>
                            <div class="session-details">
                                <div class="session-field">
                                    <label>Session Status:</label>
                                    <span class="field-value status-active">Active</span>
                                </div>
                                <div class="session-field">
                                    <label>Login Method:</label>
                                    <span class="field-value">${currentUser.app_metadata?.provider || 'Email'}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(profileModal);

        // Set up event handlers
        this.setupProfileHandlers(profileModal);
    }

    setupProfileHandlers(modal) {
        // Edit field buttons
        modal.querySelectorAll('.edit-field-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const fieldName = e.target.dataset.field;
                this.editProfileField(fieldName);
            });
        });

        // Change password button
        const changePasswordBtn = modal.querySelector('#change-password-btn');
        if (changePasswordBtn) {
            changePasswordBtn.addEventListener('click', () => {
                this.showChangePasswordForm();
            });
        }

        // Reset password button
        const resetPasswordBtn = modal.querySelector('#reset-password-btn');
        if (resetPasswordBtn) {
            resetPasswordBtn.addEventListener('click', () => {
                this.showResetPasswordForm();
            });
        }
    }

    editProfileField(fieldName) {
        const currentUser = this.auth.getCurrentUser();

        // Prevent staff from editing their staff ID
        if (fieldName === 'staff_id' && currentUser.role !== 'iharc_admin') {
            this.ui.showDialog('Access Denied', 'Only administrators can edit staff ID numbers. Please contact an admin to update this field.', 'error');
            return;
        }

        const currentValue = currentUser[fieldName] || currentUser.user_metadata?.[fieldName] || '';

        const fieldLabels = {
            name: 'Full Name',
            phone: 'Phone Number',
            department: 'Department',
            staff_id: 'IHARC Staff ID',
            ranger_id: 'Ranger ID'
        };

        const label = fieldLabels[fieldName] || fieldName;

        this.ui.showForm(
            `Edit ${label}`,
            [{
                name: fieldName,
                label: label,
                type: 'text',
                required: fieldName === 'name',
                value: currentValue
            }],
            async (formData) => {
                try {
                    const result = await this.auth.updateProfile({
                        [fieldName]: formData[fieldName]
                    });

                    if (result.success) {
                        this.ui.showDialog('Success', result.message, 'success');

                        // Update the display
                        const fieldElement = document.getElementById(`user-${fieldName}`);
                        if (fieldElement) {
                            fieldElement.textContent = formData[fieldName] || 'Not set';
                        }

                        return true;
                    } else {
                        this.ui.showDialog('Error', result.message, 'error');
                        return false;
                    }
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to update ${label.toLowerCase()}: ${error.message}`, 'error');
                    return false;
                }
            }
        );
    }

    showChangePasswordForm() {
        this.ui.showForm(
            'Change Password',
            [
                {
                    name: 'currentPassword',
                    label: 'Current Password',
                    type: 'password',
                    required: true
                },
                {
                    name: 'newPassword',
                    label: 'New Password',
                    type: 'password',
                    required: true
                },
                {
                    name: 'confirmPassword',
                    label: 'Confirm New Password',
                    type: 'password',
                    required: true
                }
            ],
            async (formData) => {
                try {
                    // Validate passwords match
                    if (formData.newPassword !== formData.confirmPassword) {
                        this.ui.showDialog('Error', 'New passwords do not match', 'error');
                        return false;
                    }

                    // Validate password strength
                    if (formData.newPassword.length < 8) {
                        this.ui.showDialog('Error', 'Password must be at least 8 characters long', 'error');
                        return false;
                    }

                    const result = await this.auth.changePassword(formData.newPassword);

                    if (result.success) {
                        this.ui.showDialog('Success', result.message, 'success');
                        return true;
                    } else {
                        this.ui.showDialog('Error', result.message, 'error');
                        return false;
                    }
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to change password: ${error.message}`, 'error');
                    return false;
                }
            }
        );
    }

    showResetPasswordForm() {
        const currentUser = this.auth.getCurrentUser();

        this.ui.showForm(
            'Reset Password via Email',
            [{
                name: 'email',
                label: 'Email Address',
                type: 'email',
                required: true,
                value: currentUser.email || ''
            }],
            async (formData) => {
                try {
                    const result = await this.auth.resetPassword(formData.email);

                    if (result.success) {
                        this.ui.showDialog('Success', result.message, 'success');
                        return true;
                    } else {
                        this.ui.showDialog('Error', result.message, 'error');
                        return false;
                    }
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to send reset email: ${error.message}`, 'error');
                    return false;
                }
            }
        );
    }

    formatDate(dateString) {
        if (!dateString) return 'Not available';
        try {
            return new Date(dateString).toLocaleString();
        } catch {
            return 'Invalid date';
        }
    }
}

// Check Updates Command
class CheckUpdatesCommand extends BaseCommand {
    async execute(args) {
        try {
            this.ui.setStatus('Checking for updates...', 'info');

            // Import UpdateUIManager if not already available
            if (!window.updateUIManager) {
                const { UpdateUIManager } = await import('./updater.js');
                window.updateUIManager = new UpdateUIManager();
            }

            const updateInfo = await window.updateUIManager.checkForUpdates(true);

            if (updateInfo.available) {
                this.ui.setStatus(`Update available: v${updateInfo.latestVersion}`, 'success');
            } else {
                this.ui.setStatus('No updates available', 'info');
            }

            return updateInfo;
        } catch (error) {
            this.ui.setStatus('Failed to check for updates', 'error');
            throw error;
        }
    }
}

// Update Command (Download and Install)
class UpdateCommand extends BaseCommand {
    async execute(args) {
        try {
            this.ui.setStatus('Checking for updates...', 'info');

            // Import UpdateUIManager if not already available
            if (!window.updateUIManager) {
                const { UpdateUIManager } = await import('./updater.js');
                window.updateUIManager = new UpdateUIManager();
            }

            const updateInfo = await window.updateUIManager.checkForUpdates();

            if (updateInfo.available) {
                this.ui.setStatus(`Starting update to v${updateInfo.latestVersion}...`, 'info');
                await window.updateUIManager.startUpdateDownload(updateInfo);
            } else {
                this.ui.setStatus('No updates available', 'info');
                window.updateUIManager.showNoUpdateDialog();
            }

            return updateInfo;
        } catch (error) {
            this.ui.setStatus('Update failed', 'error');
            throw error;
        }
    }
}

// Release Notes Command
class ReleaseNotesCommand extends BaseCommand {
    async execute(args) {
        try {
            this.ui.setStatus('Loading release notes...', 'info');

            // Import UpdateUIManager if not already available
            if (!window.updateUIManager) {
                const { UpdateUIManager } = await import('./updater.js');
                window.updateUIManager = new UpdateUIManager();
            }

            // Show release notes for current version
            await this.showReleaseNotesDialog();

            this.ui.setStatus('Ready', 'info');
        } catch (error) {
            this.ui.setStatus('Failed to load release notes', 'error');
            throw error;
        }
    }

    async showReleaseNotesDialog() {
        try {
            // Get current version release notes
            const currentVersion = await window.electronAPI.invoke('app-version');

            // Try to get release notes from GitHub
            let releaseNotes = 'Release notes not available.';
            let releaseDate = 'Unknown';

            try {
                const updateInfo = await window.electronAPI.invoke('check-for-updates');
                if (updateInfo && updateInfo.releaseNotes) {
                    releaseNotes = updateInfo.releaseNotes;
                    releaseDate = new Date(updateInfo.publishedAt).toLocaleDateString();
                }
            } catch (error) {
                console.log('Could not fetch release notes from GitHub:', error.message);
                // Use fallback release notes
                releaseNotes = this.getFallbackReleaseNotes(currentVersion);
            }

            const dialog = document.createElement('div');
            dialog.className = 'modal-overlay release-notes-dialog';

            dialog.innerHTML = `
                <div class="modal-dialog" style="max-width: 700px;">
                    <div class="modal-header">
                        <h3>📋 Release Notes - Version ${currentVersion}</h3>
                    </div>
                    <div class="modal-body">
                        <div class="release-info">
                            <p><strong>Version:</strong> ${currentVersion}</p>
                            <p><strong>Release Date:</strong> ${releaseDate}</p>
                        </div>
                        <div class="release-notes">
                            <h4>What's New:</h4>
                            <div class="release-notes-content">${this.formatReleaseNotes(releaseNotes)}</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="primary-button" onclick="this.closest('.modal-overlay').remove()">Close</button>
                        <button class="secondary-button" id="check-updates-btn">Check for Updates</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);

            // Set up check updates button
            dialog.querySelector('#check-updates-btn').onclick = async () => {
                dialog.remove();
                try {
                    await this.manager.executeCommand('check-updates');
                } catch (error) {
                    console.error('Error checking for updates:', error);
                }
            };

        } catch (error) {
            console.error('Error showing release notes:', error);
            this.ui.showDialog('Error', 'Failed to load release notes', 'error');
        }
    }

    getFallbackReleaseNotes(version) {
        // Fallback release notes for when GitHub is not accessible
        return `
            <strong>S.T.E.V.I Retro v${version}</strong><br><br>

            <strong>🚀 Features:</strong><br>
            • Professional incident reporting system<br>
            • Comprehensive record management<br>
            • Offline-first operation with automatic sync<br>
            • Secure authentication and role-based access<br>
            • Retro terminal interface with modern functionality<br><br>

            <strong>🔧 System:</strong><br>
            • Built with Electron for cross-platform compatibility<br>
            • Supabase backend integration<br>
            • Real-time data synchronization<br>
            • Secure local data caching<br><br>

            <strong>📱 Interface:</strong><br>
            • Authentic retro DOS styling<br>
            • Tabbed navigation system<br>
            • Modal dialogs and forms<br>
            • Network status monitoring<br><br>

            For the latest updates and detailed release notes, please check the GitHub repository.
        `;
    }

    formatReleaseNotes(notes) {
        if (!notes) return 'No release notes available.';

        // Convert markdown-style formatting to HTML
        return notes
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/#{1,6}\s*(.*?)$/gm, '<h4>$1</h4>')
            .replace(/^\s*[-*+]\s+(.*)$/gm, '• $1');
    }
}

// Add Activity Command
class AddActivityCommand extends BaseCommand {
    async execute(args) {
        this.showActivityTypeSelection();
    }

    showActivityTypeSelection() {
        // Create activity type selection modal
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';

        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>Add Activity - Select Type</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <p>What type of activity would you like to add?</p>
                    <div class="activity-type-selection">
                        <div class="menu-grid">
                            <div class="menu-item" data-activity-type="people">
                                <div class="menu-icon">👤</div>
                                <div class="menu-title">Person Activity</div>
                                <div class="menu-desc">Add activity for a person record</div>
                            </div>
                            <div class="menu-item" data-activity-type="addresses">
                                <div class="menu-icon">🏠</div>
                                <div class="menu-title">Address Activity</div>
                                <div class="menu-desc">Add activity for an address record</div>
                            </div>
                            <div class="menu-item" data-activity-type="license_plates">
                                <div class="menu-icon">🚗</div>
                                <div class="menu-title">Vehicle Activity</div>
                                <div class="menu-desc">Add activity for a vehicle record</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Handle activity type selection
        modal.addEventListener('click', (e) => {
            const menuItem = e.target.closest('.menu-item');
            if (menuItem) {
                const activityType = menuItem.dataset.activityType;
                modal.remove();
                this.showRecordSearch(activityType);
            }
        });
    }

    showRecordSearch(recordType) {
        // Create record search modal
        const searchModal = document.createElement('div');
        searchModal.className = 'modal-overlay';

        const recordTypeNames = {
            people: 'People',
            addresses: 'Addresses',
            license_plates: 'License Plates'
        };

        searchModal.innerHTML = `
            <div class="modal-dialog search-modal">
                <div class="modal-header">
                    <h3>Select ${recordTypeNames[recordType]} Record</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-field">
                            <label for="search-query">Search For:</label>
                            <input type="text" id="search-query" name="search-query" placeholder="Enter search term...">
                        </div>
                        <button type="button" id="search-submit" class="primary-button">Search</button>
                    </div>
                    <div class="search-results" id="search-results" style="display: none;">
                        <h4>Search Results:</h4>
                        <div class="results-container" id="results-container"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                </div>
            </div>
        `;

        document.body.appendChild(searchModal);

        const searchQuery = searchModal.querySelector('#search-query');
        const searchSubmit = searchModal.querySelector('#search-submit');
        const searchResults = searchModal.querySelector('#search-results');
        const resultsContainer = searchModal.querySelector('#results-container');

        // Handle search submission
        const performSearch = async () => {
            const query = searchQuery.value.trim();

            if (!query) {
                this.ui.showDialog('Search Error', 'Please enter a search term.', 'error');
                return;
            }

            try {
                searchSubmit.disabled = true;
                searchSubmit.textContent = 'Searching...';

                const results = await this.performRecordSearch(recordType, query);
                this.displaySearchResults(results, resultsContainer, recordType);
                searchResults.style.display = 'block';

            } catch (error) {
                this.ui.showDialog('Search Error', `Search failed: ${error.message}`, 'error');
            } finally {
                searchSubmit.disabled = false;
                searchSubmit.textContent = 'Search';
            }
        };

        searchSubmit.addEventListener('click', performSearch);
        searchQuery.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Focus search input
        searchQuery.focus();
    }

    async performRecordSearch(recordType, query) {
        try {
            // Get all records and filter client-side for comprehensive search
            const allRecords = await this.data.search(recordType, {});

            if (!allRecords || allRecords.length === 0) {
                return [];
            }

            const lowerQuery = query.toLowerCase();

            return allRecords.filter(record => {
                // Search across all string fields
                return Object.values(record).some(value => {
                    if (typeof value === 'string') {
                        return value.toLowerCase().includes(lowerQuery);
                    }
                    return false;
                });
            });
        } catch (error) {
            console.error(`Error searching ${recordType}:`, error);
            return [];
        }
    }

    displaySearchResults(results, container, recordType) {
        container.innerHTML = '';

        if (results.length === 0) {
            container.innerHTML = '<div class="no-results">No records found matching your search.</div>';
            return;
        }

        results.forEach(record => {
            const recordDiv = document.createElement('div');
            recordDiv.className = 'record-item selectable';
            recordDiv.innerHTML = `
                ${this.formatRecord(recordType, record)}
                <button class="select-record-btn primary-button" data-record-id="${record.id}">Select</button>
            `;

            // Add click handler for select button
            const selectBtn = recordDiv.querySelector('.select-record-btn');
            selectBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handleRecordSelection(record, recordType);
            });

            container.appendChild(recordDiv);
        });
    }

    formatRecord(table, record) {
        switch (table) {
            case 'people':
                return `
                    <div class="record-summary">
                        <strong>${record.first_name || ''} ${record.last_name || ''}</strong>
                        ${record.date_of_birth ? `<br>DOB: ${record.date_of_birth}` : ''}
                        ${record.phone ? `<br>Phone: ${record.phone}` : ''}
                        ${record.email ? `<br>Email: ${record.email}` : ''}
                    </div>
                `;
            case 'addresses':
                return `
                    <div class="record-summary">
                        <strong>${record.street_address || 'Unknown Address'}</strong>
                        <br>${record.city || ''}, ${record.province || ''}
                        ${record.postal_code ? ` ${record.postal_code}` : ''}
                        ${record.country ? `<br>Country: ${record.country}` : ''}
                    </div>
                `;
            case 'license_plates':
                return `
                    <div class="record-summary">
                        <strong>${record.plate_number || 'Unknown Plate'}</strong>
                        <br>Province: ${record.province || 'Unknown'}
                        ${record.vehicle_make ? `<br>Vehicle: ${record.vehicle_year || ''} ${record.vehicle_make} ${record.vehicle_model || ''}`.trim() : ''}
                        ${record.vehicle_color ? `<br>Color: ${record.vehicle_color}` : ''}
                    </div>
                `;
            default:
                return `<div class="record-summary">Record ID: ${record.id}</div>`;
        }
    }

    handleRecordSelection(record, recordType) {
        // Close the search modal
        document.querySelector('.modal-overlay').remove();

        // Show the activity form for the selected record
        this.showAddActivityForm(recordType, record);
    }

    // Reuse the existing showAddActivityForm method from SearchRecordsCommand
    showAddActivityForm(table, record) {
        try {
            console.log(`Showing add activity form for ${table} record:`, record);

            const activityTable = this.getActivityTableName(table);
            const foreignKey = this.getForeignKeyName(table);

            console.log(`Activity table: ${activityTable}, Foreign key: ${foreignKey}`);

            // Get current user info
            const currentUser = this.auth.getCurrentUser();

            // Pre-fill some fields
            const defaultData = {
                [foreignKey]: record.id,
                activity_date: new Date().toISOString().split('T')[0], // Today's date
                staff_member: currentUser?.name || currentUser?.email || 'Unknown Staff',
                created_by: currentUser?.email || 'unknown'
            };

            console.log('Default data:', defaultData);

            // Check if schema is available
            if (!this.schema) {
                throw new Error('Schema manager not available');
            }

            // Generate form fields for the activity table, excluding system fields and foreign keys
            const excludeFields = ['id', 'created_at', 'created_by', foreignKey];
            const fields = this.schema.generateFormFields(activityTable, excludeFields);

            // Set default values in the fields
            fields.forEach(field => {
                if (defaultData[field.name] !== undefined) {
                    field.value = defaultData[field.name];
                }
            });

            console.log(`Generated ${fields.length} fields for activity form`);

            // Create and show the form
            this.ui.showFullScreenForm(
                `Add Activity - ${this.getRecordTitle(table, record)}`,
                fields,
                async (formData) => {
                    try {
                        // Merge default data with form data
                        const activityData = { ...defaultData, ...formData };

                        await this.data.insert(activityTable, activityData);
                        this.ui.showDialog('Success', 'Activity added successfully!', 'success');

                        return true;
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to add activity: ${error.message}`, 'error');
                        return false;
                    }
                }
            );
        } catch (error) {
            console.error('Error showing add activity form:', error);
            this.ui.showDialog('Error', `Failed to show activity form: ${error.message}`, 'error');
        }
    }

    // Helper methods (reused from SearchRecordsCommand)
    getActivityTableName(recordTable) {
        const mapping = {
            'people': 'people_activities',
            'addresses': 'address_activities',
            'license_plates': 'vehicle_activities'
        };
        return mapping[recordTable] || 'activities';
    }

    getForeignKeyName(table) {
        const mapping = {
            'people': 'person_id',
            'addresses': 'address_id',
            'license_plates': 'vehicle_id'
        };
        return mapping[table] || 'record_id';
    }

    getRecordTitle(table, record) {
        switch (table) {
            case 'people':
                return `${record.first_name || ''} ${record.last_name || ''}`.trim() || 'Unknown Person';
            case 'addresses':
                return record.street_address || 'Unknown Address';
            case 'license_plates':
                return record.plate_number || 'Unknown Plate';
            default:
                return `Record ${record.id}`;
        }
    }
}

// Items Management Command
class ItemsCommand extends BaseCommand {
    async execute(args) {
        return new Promise((resolve) => {
            this.showItemsInterface(resolve);
        });
    }

    showItemsInterface(resolve) {
        // Create items management modal
        const itemsModal = document.createElement('div');
        itemsModal.className = 'modal-overlay';

        itemsModal.innerHTML = `
            <div class="modal-dialog large-modal">
                <div class="modal-header">
                    <h3>📦 Items Management</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="items-controls">
                        <button class="primary-button" id="add-item-btn">+ Add New Item</button>
                        <button class="secondary-button" id="low-stock-btn">⚠️ Low Stock Alert</button>
                        <button class="secondary-button" id="refresh-items-btn">🔄 Refresh</button>
                    </div>
                    <div class="items-list" id="items-list">
                        <div class="loading">Loading items...</div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(itemsModal);

        // Set up event handlers
        this.setupItemsHandlers(itemsModal, resolve);

        // Load items
        this.loadItems();
    }

    setupItemsHandlers(modal, resolve) {
        const addItemBtn = modal.querySelector('#add-item-btn');
        const lowStockBtn = modal.querySelector('#low-stock-btn');
        const refreshBtn = modal.querySelector('#refresh-items-btn');

        addItemBtn.addEventListener('click', () => {
            this.showAddItemForm();
        });

        lowStockBtn.addEventListener('click', () => {
            this.showLowStockAlert();
        });

        refreshBtn.addEventListener('click', () => {
            this.loadItems();
        });
    }

    async loadItems() {
        try {
            const itemsList = document.getElementById('items-list');
            if (!itemsList) return;

            itemsList.innerHTML = '<div class="loading">Loading items...</div>';

            const items = await this.data.search('items', {});

            if (!items || items.length === 0) {
                itemsList.innerHTML = '<div class="no-items">No items found. Click "Add New Item" to get started.</div>';
                return;
            }

            // Sort items by category and name
            items.sort((a, b) => {
                if (a.category !== b.category) {
                    return a.category.localeCompare(b.category);
                }
                return a.name.localeCompare(b.name);
            });

            const itemsHTML = items.map(item => this.formatItemRow(item)).join('');

            itemsList.innerHTML = `
                <div class="items-table">
                    <div class="items-header">
                        <div class="item-name">Name</div>
                        <div class="item-category">Category</div>
                        <div class="item-stock">Stock</div>
                        <div class="item-unit">Unit</div>
                        <div class="item-status">Status</div>
                        <div class="item-actions">Actions</div>
                    </div>
                    <div class="items-body">
                        ${itemsHTML}
                    </div>
                </div>
            `;

            // Set up item action handlers
            this.setupItemActionHandlers();

        } catch (error) {
            console.error('Error loading items:', error);
            const itemsList = document.getElementById('items-list');
            if (itemsList) {
                itemsList.innerHTML = `<div class="error">Failed to load items: ${error.message}</div>`;
            }
        }
    }

    formatItemRow(item) {
        const isLowStock = item.minimum_threshold && item.current_stock <= item.minimum_threshold;
        const stockClass = isLowStock ? 'low-stock' : '';
        const statusClass = item.active ? 'active' : 'inactive';

        return `
            <div class="item-row ${stockClass}" data-item-id="${item.id}">
                <div class="item-name">${item.name}</div>
                <div class="item-category">${item.category.replace('_', ' ')}</div>
                <div class="item-stock ${stockClass}">${item.current_stock}</div>
                <div class="item-unit">${item.unit_type}</div>
                <div class="item-status ${statusClass}">${item.active ? 'Active' : 'Inactive'}</div>
                <div class="item-actions">
                    <button class="action-btn edit-item-btn" data-item-id="${item.id}" title="Edit Item">✏️</button>
                    <button class="action-btn stock-btn" data-item-id="${item.id}" title="Update Stock">📦</button>
                    <button class="action-btn toggle-btn" data-item-id="${item.id}" title="${item.active ? 'Deactivate' : 'Activate'}">${item.active ? '🔴' : '🟢'}</button>
                </div>
            </div>
        `;
    }

    setupItemActionHandlers() {
        const itemsList = document.getElementById('items-list');
        if (!itemsList) return;

        itemsList.addEventListener('click', async (e) => {
            const itemId = e.target.dataset.itemId;
            if (!itemId) return;

            if (e.target.classList.contains('edit-item-btn')) {
                await this.showEditItemForm(itemId);
            } else if (e.target.classList.contains('stock-btn')) {
                await this.showUpdateStockForm(itemId);
            } else if (e.target.classList.contains('toggle-btn')) {
                await this.toggleItemStatus(itemId);
            }
        });
    }

    async showAddItemForm() {
        try {
            const fields = this.schema.generateFormFields('items', ['id', 'created_at', 'updated_at']);

            // Set default values
            fields.forEach(field => {
                if (field.name === 'active') {
                    field.value = true;
                } else if (field.name === 'current_stock') {
                    field.value = 0;
                }
            });

            this.ui.showFullScreenForm('Add New Item', fields, async (formData) => {
                try {
                    const itemData = {
                        ...formData,
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    };

                    await this.data.insert('items', itemData);
                    this.ui.showDialog('Success', 'Item added successfully!', 'success');
                    this.loadItems();
                    return true;
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to add item: ${error.message}`, 'error');
                    return false;
                }
            });
        } catch (error) {
            console.error('Error showing add item form:', error);
            this.ui.showDialog('Error', `Failed to show add item form: ${error.message}`, 'error');
        }
    }

    async showEditItemForm(itemId) {
        try {
            const item = await this.data.get('items', itemId);
            if (!item) {
                this.ui.showDialog('Error', 'Item not found', 'error');
                return;
            }

            const fields = this.schema.generateFormFields('items', ['id', 'created_at', 'updated_at']);

            // Set current values
            fields.forEach(field => {
                if (item[field.name] !== undefined) {
                    field.value = item[field.name];
                }
            });

            this.ui.showForm(`Edit Item: ${item.name}`, fields, async (formData) => {
                try {
                    const updateData = {
                        ...formData,
                        updated_at: new Date().toISOString()
                    };

                    await this.data.update('items', itemId, updateData);
                    this.ui.showDialog('Success', 'Item updated successfully!', 'success');
                    this.loadItems();
                    return true;
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to update item: ${error.message}`, 'error');
                    return false;
                }
            });
        } catch (error) {
            console.error('Error showing edit item form:', error);
            this.ui.showDialog('Error', `Failed to show edit item form: ${error.message}`, 'error');
        }
    }

    async showUpdateStockForm(itemId) {
        try {
            const item = await this.data.get('items', itemId);
            if (!item) {
                this.ui.showDialog('Error', 'Item not found', 'error');
                return;
            }

            const fields = [
                {
                    name: 'action',
                    label: 'Action',
                    type: 'select',
                    required: true,
                    options: [
                        { value: 'add', label: 'Add Stock' },
                        { value: 'remove', label: 'Remove Stock' },
                        { value: 'set', label: 'Set Stock Level' }
                    ]
                },
                {
                    name: 'quantity',
                    label: 'Quantity',
                    type: 'number',
                    required: true,
                    min: 0
                },
                {
                    name: 'notes',
                    label: 'Notes',
                    type: 'textarea',
                    placeholder: 'Reason for stock change...'
                }
            ];

            this.ui.showForm(`Update Stock: ${item.name} (Current: ${item.current_stock})`, fields, async (formData) => {
                try {
                    let newStock = item.current_stock;

                    switch (formData.action) {
                        case 'add':
                            newStock += parseInt(formData.quantity);
                            break;
                        case 'remove':
                            newStock -= parseInt(formData.quantity);
                            break;
                        case 'set':
                            newStock = parseInt(formData.quantity);
                            break;
                    }

                    if (newStock < 0) {
                        this.ui.showDialog('Error', 'Stock cannot be negative', 'error');
                        return false;
                    }

                    await this.data.update('items', itemId, {
                        current_stock: newStock,
                        updated_at: new Date().toISOString()
                    });

                    this.ui.showDialog('Success', `Stock updated! New level: ${newStock} ${item.unit_type}`, 'success');
                    this.loadItems();
                    return true;
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to update stock: ${error.message}`, 'error');
                    return false;
                }
            });
        } catch (error) {
            console.error('Error showing update stock form:', error);
            this.ui.showDialog('Error', `Failed to show update stock form: ${error.message}`, 'error');
        }
    }

    async toggleItemStatus(itemId) {
        try {
            const item = await this.data.get('items', itemId);
            if (!item) {
                this.ui.showDialog('Error', 'Item not found', 'error');
                return;
            }

            const newStatus = !item.active;
            const action = newStatus ? 'activate' : 'deactivate';

            const confirmed = confirm(`Are you sure you want to ${action} "${item.name}"?`);
            if (!confirmed) return;

            await this.data.update('items', itemId, {
                active: newStatus,
                updated_at: new Date().toISOString()
            });

            this.ui.showDialog('Success', `Item ${action}d successfully!`, 'success');
            this.loadItems();
        } catch (error) {
            console.error('Error toggling item status:', error);
            this.ui.showDialog('Error', `Failed to toggle item status: ${error.message}`, 'error');
        }
    }

    async showLowStockAlert() {
        try {
            const items = await this.data.search('items', { active: true });
            const lowStockItems = items.filter(item =>
                item.minimum_threshold && item.current_stock <= item.minimum_threshold
            );

            if (lowStockItems.length === 0) {
                this.ui.showDialog('Low Stock Alert', 'No items are currently below their minimum threshold.', 'info');
                return;
            }

            const alertHTML = lowStockItems.map(item => `
                <div class="low-stock-item">
                    <strong>${item.name}</strong><br>
                    Current: ${item.current_stock} ${item.unit_type}<br>
                    Minimum: ${item.minimum_threshold} ${item.unit_type}<br>
                    Category: ${item.category.replace('_', ' ')}
                </div>
            `).join('');

            const alertModal = document.createElement('div');
            alertModal.className = 'modal-overlay';
            alertModal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-header">
                        <h3>⚠️ Low Stock Alert</h3>
                        <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>
                    <div class="modal-body">
                        <p><strong>${lowStockItems.length} item(s) are below minimum threshold:</strong></p>
                        <div class="low-stock-list">
                            ${alertHTML}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="primary-button" onclick="this.closest('.modal-overlay').remove()">OK</button>
                    </div>
                </div>
            `;

            document.body.appendChild(alertModal);
        } catch (error) {
            console.error('Error showing low stock alert:', error);
            this.ui.showDialog('Error', `Failed to show low stock alert: ${error.message}`, 'error');
        }
    }
}

// Cleanup Updates Command
class CleanupUpdatesCommand extends BaseCommand {
    async execute(args) {
        try {
            this.ui.setStatus('Checking update storage usage...', 'info');

            // Get current storage usage
            const storageInfo = await window.electronAPI.invoke('get-update-storage-usage');

            if (storageInfo.totalFiles === 0) {
                this.ui.showDialog(
                    'No Cleanup Needed',
                    'No temporary update files found.\n\n' +
                    'Your system is already optimized!',
                    'info'
                );
                this.ui.setStatus('No cleanup needed', 'info');
                return;
            }

            // Show storage usage and confirmation dialog
            const filesList = storageInfo.files.map(file =>
                `• ${file.name} (${file.formattedSize})`
            ).join('\n');

            const confirmed = confirm(
                `Update Storage Usage:\n\n` +
                `Files: ${storageInfo.totalFiles}\n` +
                `Total Size: ${storageInfo.formattedSize}\n\n` +
                `Files to be removed:\n${filesList}\n\n` +
                `This will free up ${storageInfo.formattedSize} of storage space.\n\n` +
                'This is safe and will not affect your application or data.\n\n' +
                'Continue with cleanup?'
            );

            if (!confirmed) {
                this.ui.setStatus('Cleanup cancelled', 'info');
                return;
            }

            this.ui.setStatus('Cleaning up update files...', 'info');

            // Perform cleanup
            await window.electronAPI.invoke('cleanup-updates');

            this.ui.showDialog(
                'Cleanup Complete',
                `Successfully cleaned up ${storageInfo.totalFiles} temporary update files.\n\n` +
                `Freed ${storageInfo.formattedSize} of storage space.\n\n` +
                'Your system is now optimized!',
                'success'
            );

            this.ui.setStatus('Update cleanup completed', 'success');

        } catch (error) {
            console.error('Error during update cleanup:', error);
            this.ui.showDialog(
                'Cleanup Failed',
                `Failed to clean up update files: ${error.message}\n\n` +
                'You can try again later or contact support if the problem persists.',
                'error'
            );
            this.ui.setStatus('Cleanup failed', 'error');
        }
    }
}



// Create Test Incident Command for testing map functionality
class CreateTestIncidentCommand extends BaseCommand {
    async execute(args) {
        try {
            this.ui.setStatus('Creating test incident with map coordinates...', 'info');

            // Create test incident with Cobourg coordinates
            const testIncident = {
                incident_number: `TEST-${Date.now()}`,
                incident_type: 'Test Incident',
                location: 'Cobourg Police Station, 107 King St W, Cobourg, ON',
                coordinates: '43.9589, -78.1648', // Cobourg coordinates
                description: 'Test incident created for map functionality testing',
                status: 'active',
                priority: 'medium',
                reported_by: 'System Test',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            const result = await this.data.insert('incidents', testIncident);

            this.ui.showDialog(
                'Test Incident Created',
                `Test incident created successfully!\n\n` +
                `Incident Number: ${testIncident.incident_number}\n` +
                `Location: ${testIncident.location}\n` +
                `Coordinates: ${testIncident.coordinates}\n\n` +
                `To test the map:\n` +
                `1. Go to Dispatch screen\n` +
                `2. Click on the test incident\n` +
                `3. Click the "Map" tab\n` +
                `4. Verify the map loads with a marker\n\n` +
                `The incident will appear in the dispatch list.`,
                'success'
            );

            this.ui.setStatus('Test incident created successfully', 'success');

        } catch (error) {
            console.error('Error creating test incident:', error);
            this.ui.showDialog(
                'Error',
                `Failed to create test incident: ${error.message}\n\n` +
                `This might be due to database connection issues.`,
                'error'
            );
            this.ui.setStatus('Failed to create test incident', 'error');
        }
    }
}

// Outreach Transaction Command - Opens dedicated outreach transaction screen
class OutreachTransactionCommand extends BaseCommand {
    async execute(args) {
        try {
            this.ui.setStatus('Opening outreach transaction screen...', 'info');

            // Switch to outreach transaction screen
            await this.showOutreachTransactionScreen();

        } catch (error) {
            console.error('Error opening outreach transaction screen:', error);
            this.ui.showDialog('Error', `Failed to open outreach transactions: ${error.message}`, 'error');
            this.ui.setStatus('Failed to open outreach transactions', 'error');
        }
    }

    async showOutreachTransactionScreen() {
        // Get current location if available
        const location = await this.getCurrentLocation();

        const screenHTML = `
            <div class="outreach-transaction-screen">
                <div class="outreach-header">
                    <h2>📦 Outreach Transaction</h2>
                    <button class="close-screen-btn" onclick="app.goToDashboard()">← Back to Dashboard</button>
                </div>

                <div class="outreach-content">
                    <!-- Person Selection Section -->
                    <div class="outreach-section person-section">
                        <div class="section-header">
                            <h3>👤 Select Person</h3>
                        </div>
                        <div class="person-search-container">
                            <div class="search-input-group">
                                <input type="text" id="person-search" placeholder="Search by name, phone, or ID..." class="person-search-input">
                                <button class="search-btn" onclick="supplyDistribution.searchPerson()">🔍</button>
                                <button class="add-person-btn" onclick="supplyDistribution.showAddPersonModal()">+ New Person</button>
                            </div>
                            <div class="person-results" id="person-results">
                                <div class="no-selection">Search for a person or create a new one</div>
                            </div>
                            <div class="selected-person" id="selected-person" style="display: none;">
                                <div class="person-info"></div>
                                <button class="change-person-btn" onclick="outreachTransaction.clearPersonSelection()">Change Person</button>
                            </div>
                        </div>
                    </div>

                    <!-- Items Section -->
                    <div class="outreach-section items-section">
                        <div class="section-header">
                            <h3>📋 Items to Provide</h3>
                            <button class="add-item-btn" onclick="outreachTransaction.showItemCatalog()" disabled id="add-item-btn">+ Add Item</button>
                        </div>
                        <div class="items-list" id="items-list">
                            <div class="no-items">Select a person first, then add items to provide</div>
                        </div>
                        <div class="items-total" id="items-total" style="display: none;">
                            <div class="total-items">Total Items: <span id="total-item-count">0</span></div>
                        </div>
                    </div>

                    <!-- Details Section -->
                    <div class="outreach-section details-section">
                        <div class="section-header">
                            <h3>📝 Transaction Details</h3>
                        </div>
                        <div class="details-form">
                            <div class="form-group">
                                <label for="transaction-location">Location:</label>
                                <input type="text" id="transaction-location" value="${location.address || ''}" placeholder="Transaction location...">
                                <button class="location-btn" onclick="outreachTransaction.getCurrentLocation()" title="Get current location">📍</button>
                            </div>
                            <div class="form-group">
                                <label for="transaction-notes">Notes:</label>
                                <textarea id="transaction-notes" placeholder="Additional notes about this transaction..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="outreach-actions">
                    <button class="secondary-button" onclick="app.goToDashboard()">Cancel</button>
                    <button class="primary-button" onclick="outreachTransaction.completeTransaction()" disabled id="complete-btn">Complete Transaction</button>
                    <button class="primary-button" onclick="outreachTransaction.completeAndNew()" disabled id="complete-new-btn">Complete & New</button>
                </div>
            </div>
        `;

        // Replace main content with outreach transaction screen
        const contentArea = document.querySelector('.content-area');
        contentArea.innerHTML = screenHTML;

        // Initialize outreach transaction manager
        window.outreachTransaction = new OutreachTransactionManager(this.data, this.ui);

        this.ui.setStatus('Outreach transaction screen loaded', 'success');
    }

    async getCurrentLocation() {
        try {
            if (!navigator.geolocation) {
                return { address: '', coordinates: null };
            }

            return new Promise((resolve) => {
                navigator.geolocation.getCurrentPosition(
                    async (position) => {
                        const { latitude, longitude } = position.coords;

                        try {
                            // Use Google Geocoding API to get address
                            const address = await this.geocodeCoordinates(latitude, longitude);
                            resolve({
                                address: address,
                                coordinates: `${latitude}, ${longitude}`
                            });
                        } catch (error) {
                            console.warn('Geocoding failed:', error);
                            resolve({
                                address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
                                coordinates: `${latitude}, ${longitude}`
                            });
                        }
                    },
                    (error) => {
                        console.warn('Geolocation failed:', error);
                        resolve({ address: '', coordinates: null });
                    },
                    { timeout: 10000, enableHighAccuracy: true }
                );
            });
        } catch (error) {
            console.warn('Location services not available:', error);
            return { address: '', coordinates: null };
        }
    }

    async geocodeCoordinates(lat, lng) {
        const apiKey = this.app?.config?.get('google.apiKey');
        if (!apiKey) {
            throw new Error('Google API key not available');
        }

        const response = await fetch(
            `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`
        );

        if (!response.ok) {
            throw new Error('Geocoding request failed');
        }

        const data = await response.json();

        if (data.status === 'OK' && data.results.length > 0) {
            return data.results[0].formatted_address;
        }

        throw new Error('No address found for coordinates');
    }
}



// Create Test Data Command - Creates comprehensive test data for all systems and verifies both Supabase and caching
class CreateTestDataCommand extends BaseCommand {
    generateId() {
        // Generate a proper UUID v4
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    generateTestId() {
        // For test data that needs to be easily identifiable, use a test prefix with UUID
        return 'test-' + this.generateId();
    }

    async execute(args) {
        try {
            this.ui.setStatus('Creating and testing comprehensive data...', 'info');

            let results = [];
            let testResults = {
                supabaseInserts: 0,
                cacheInserts: 0,
                supabaseReads: 0,
                cacheReads: 0,
                failures: []
            };

            // Create test people (using correct schema)
            const testPeople = [
                {
                    first_name: 'John',
                    last_name: 'Doe',
                    phone: '555-0101',
                    email: '<EMAIL>',
                    date_of_birth: '1985-03-15',
                    notes: 'Test person for system testing',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                },
                {
                    first_name: 'Jane',
                    last_name: 'Smith',
                    phone: '555-0102',
                    email: '<EMAIL>',
                    date_of_birth: '1990-07-22',
                    notes: 'Test person for system testing',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                },
                {
                    first_name: 'Mike',
                    last_name: 'Johnson',
                    phone: '555-0103',
                    email: '<EMAIL>',
                    date_of_birth: '1978-11-08',
                    notes: 'Test person for system testing',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                },
                {
                    first_name: 'Sarah',
                    last_name: 'Wilson',
                    phone: '555-0104',
                    email: '<EMAIL>',
                    date_of_birth: '1992-05-30',
                    notes: 'Test person for system testing',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }
            ];

            for (const person of testPeople) {
                try {
                    // Clean the data to ensure compatibility - use only standard column names
                    const cleanPerson = {
                        first_name: person.first_name || '',
                        last_name: person.last_name || '',
                        phone: person.phone || '',
                        email: person.email || '',
                        date_of_birth: person.date_of_birth || null,
                        notes: person.notes || '',
                        created_at: person.created_at || new Date().toISOString(),
                        updated_at: person.updated_at || new Date().toISOString()
                    };

                    // Test Supabase insert
                    const result = await this.data.insert('people', cleanPerson);
                    testResults.supabaseInserts++;

                    // Verify the record was created with an ID
                    if (result && result.id) {
                        // Test Supabase read
                        const supabaseRecord = await this.data.get('people', result.id);
                        if (supabaseRecord) {
                            testResults.supabaseReads++;
                        }

                        // Test cache read (if SQLite is available)
                        if (this.data.sqlite) {
                            const cachedRecord = this.data.sqlite.get('people', result.id);
                            if (cachedRecord) {
                                testResults.cacheReads++;
                            }
                        }

                        results.push(`✅ Created & verified person: ${person.first_name} ${person.last_name} (ID: ${result.id})`);
                    } else {
                        results.push(`⚠️ Created person but no ID returned: ${person.first_name} ${person.last_name}`);
                    }
                } catch (error) {
                    console.error('Person creation error:', error);
                    testResults.failures.push(`people: ${person.first_name} ${person.last_name} - ${error.message}`);
                    results.push(`❌ Failed to create person: ${person.first_name} ${person.last_name} - ${error.message}`);
                }
            }

            // Create test items (using correct schema)
            const testItems = [
                {
                    id: this.generateId(),
                    name: 'Naloxone Kit',
                    description: 'Emergency overdose reversal kit',
                    category: 'harm_reduction',
                    unit_type: 'kit',
                    current_stock: 50,
                    minimum_threshold: 10,
                    active: true,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                },
                {
                    id: this.generateId(),
                    name: 'Clean Needles',
                    description: 'Sterile injection needles',
                    category: 'harm_reduction',
                    unit_type: 'piece',
                    current_stock: 200,
                    minimum_threshold: 50,
                    active: true,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                },
                {
                    id: this.generateId(),
                    name: 'Bandages',
                    description: 'First aid bandages',
                    category: 'medical',
                    unit_type: 'piece',
                    current_stock: 100,
                    minimum_threshold: 20,
                    active: true,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                },
                {
                    id: this.generateId(),
                    name: 'Soap',
                    description: 'Personal hygiene soap',
                    category: 'hygiene',
                    unit_type: 'bar',
                    current_stock: 75,
                    minimum_threshold: 15,
                    active: true,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                },
                {
                    id: this.generateId(),
                    name: 'Granola Bars',
                    description: 'Nutritious snack bars',
                    category: 'food',
                    unit_type: 'piece',
                    current_stock: 150,
                    minimum_threshold: 30,
                    active: true,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }
            ];

            for (const item of testItems) {
                try {
                    // Clean the data to ensure compatibility
                    const cleanItem = {
                        ...item,
                        // Ensure numeric fields are proper numbers
                        current_stock: typeof item.current_stock === 'number' ? item.current_stock : parseInt(item.current_stock) || 0,
                        minimum_threshold: typeof item.minimum_threshold === 'number' ? item.minimum_threshold : parseInt(item.minimum_threshold) || 0,
                        cost_per_unit: typeof item.cost_per_unit === 'number' ? item.cost_per_unit : parseFloat(item.cost_per_unit) || null,
                        // Ensure boolean fields are proper booleans
                        active: item.active !== false
                    };

                    await this.data.insert('items', cleanItem);
                    results.push(`✅ Created item: ${item.name} (${item.current_stock} ${item.unit_type})`);
                } catch (error) {
                    console.error('Item creation error:', error);
                    results.push(`❌ Failed to create item: ${item.name} - ${error.message}`);
                }
            }

            // Create test addresses (using correct schema)
            const testAddresses = [
                {
                    street_address: '123 Main Street',
                    city: 'Cobourg',
                    province: 'ON',
                    postal_code: 'K9A 1A1',
                    country: 'Canada',
                    notes: 'Test address for system testing',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                },
                {
                    street_address: '456 Oak Avenue',
                    city: 'Cobourg',
                    province: 'ON',
                    postal_code: 'K9A 2B2',
                    country: 'Canada',
                    notes: 'Test address for system testing',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }
            ];

            for (const address of testAddresses) {
                try {
                    // Clean the data to ensure compatibility
                    const cleanAddress = {
                        ...address,
                        // Ensure all string fields are properly formatted
                        street_address: address.street_address || '',
                        city: address.city || '',
                        province: address.province || '',
                        postal_code: address.postal_code || '',
                        country: address.country || 'Canada'
                    };

                    await this.data.insert('addresses', cleanAddress);
                    results.push(`✅ Created address: ${address.street_address}, ${address.city}`);
                } catch (error) {
                    console.error('Address creation error:', error);
                    results.push(`❌ Failed to create address: ${address.street_address} - ${error.message}`);
                }
            }

            // Create test incidents (using correct schema)
            const testIncidents = [
                {
                    incident_number: `TEST-${Date.now()}-001`,
                    location: '123 Main Street, Cobourg, ON',
                    narrative: 'Test incident for system testing - noise complaint from neighbors about loud music and disturbance',
                    is_urgent: false,
                    reporter_id: null,
                    tags: ['test', 'noise', 'disturbance'],
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                },
                {
                    incident_number: `TEST-${Date.now()}-002`,
                    location: '456 Oak Avenue, Cobourg, ON',
                    narrative: 'Test incident for system testing - welfare check request for individual who has not been seen for several days',
                    is_urgent: true,
                    reporter_id: null,
                    tags: ['test', 'welfare_check', 'urgent'],
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }
            ];

            for (const incident of testIncidents) {
                try {
                    // Clean the data to ensure compatibility
                    const cleanIncident = {
                        ...incident,
                        // Ensure boolean fields are proper booleans
                        is_urgent: incident.is_urgent === true,
                        // Ensure arrays are properly formatted
                        tags: Array.isArray(incident.tags) ? incident.tags : []
                    };

                    // Test incident creation (this was the main issue we fixed)
                    const result = await this.data.insert('incidents', cleanIncident);
                    testResults.supabaseInserts++;

                    if (result && result.id) {
                        // Verify incident was created and can be read
                        const supabaseRecord = await this.data.get('incidents', result.id);
                        if (supabaseRecord) {
                            testResults.supabaseReads++;
                        }

                        // Test cache read
                        if (this.data.sqlite) {
                            const cachedRecord = this.data.sqlite.get('incidents', result.id);
                            if (cachedRecord) {
                                testResults.cacheReads++;
                            }
                        }

                        results.push(`✅ Created & verified incident: ${incident.incident_number} (ID: ${result.id})`);
                    } else {
                        results.push(`⚠️ Created incident but no ID returned: ${incident.incident_number}`);
                    }
                } catch (error) {
                    console.error('Incident creation error:', error);
                    testResults.failures.push(`incidents: ${incident.incident_number} - ${error.message}`);
                    results.push(`❌ Failed to create incident: ${incident.incident_number} - ${error.message}`);
                }
            }

            // Test other critical data types that had RLS issues

            // Test encampments
            try {
                const testEncampment = {
                    name: 'Test Encampment',
                    location: 'Test Location for System Testing',
                    status: 'active',
                    estimated_population: 5,
                    description: 'Test encampment for system testing',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                const encampmentResult = await this.data.insert('encampments', testEncampment);
                testResults.supabaseInserts++;

                if (encampmentResult && encampmentResult.id) {
                    const supabaseRecord = await this.data.get('encampments', encampmentResult.id);
                    if (supabaseRecord) testResults.supabaseReads++;

                    if (this.data.sqlite) {
                        const cachedRecord = this.data.sqlite.get('encampments', encampmentResult.id);
                        if (cachedRecord) testResults.cacheReads++;
                    }

                    results.push(`✅ Created & verified encampment: ${testEncampment.name} (ID: ${encampmentResult.id})`);
                }
            } catch (error) {
                testResults.failures.push(`encampments: ${error.message}`);
                results.push(`❌ Failed to create encampment: ${error.message}`);
            }

            // Test addresses
            try {
                const testAddress = {
                    street_address: '123 Test Street',
                    city: 'Test City',
                    province: 'ON',
                    postal_code: 'A1B 2C3',
                    country: 'Canada',
                    notes: 'Test address for system testing',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                const addressResult = await this.data.insert('addresses', testAddress);
                testResults.supabaseInserts++;

                if (addressResult && addressResult.id) {
                    const supabaseRecord = await this.data.get('addresses', addressResult.id);
                    if (supabaseRecord) testResults.supabaseReads++;

                    if (this.data.sqlite) {
                        const cachedRecord = this.data.sqlite.get('addresses', addressResult.id);
                        if (cachedRecord) testResults.cacheReads++;
                    }

                    results.push(`✅ Created & verified address: ${testAddress.street_address} (ID: ${addressResult.id})`);
                }
            } catch (error) {
                testResults.failures.push(`addresses: ${error.message}`);
                results.push(`❌ Failed to create address: ${error.message}`);
            }

            // Test organizations
            try {
                const testOrg = {
                    name: 'Test Organization',
                    organization_type: 'nonprofit',
                    contact_person: 'Test Contact',
                    phone: '555-0456',
                    email: '<EMAIL>',
                    notes: 'Test organization for system testing',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                const orgResult = await this.data.insert('organizations', testOrg);
                testResults.supabaseInserts++;

                if (orgResult && orgResult.id) {
                    const supabaseRecord = await this.data.get('organizations', orgResult.id);
                    if (supabaseRecord) testResults.supabaseReads++;

                    if (this.data.sqlite) {
                        const cachedRecord = this.data.sqlite.get('organizations', orgResult.id);
                        if (cachedRecord) testResults.cacheReads++;
                    }

                    results.push(`✅ Created & verified organization: ${testOrg.name} (ID: ${orgResult.id})`);
                }
            } catch (error) {
                testResults.failures.push(`organizations: ${error.message}`);
                results.push(`❌ Failed to create organization: ${error.message}`);
            }

            // Display comprehensive results with statistics
            const successRate = testResults.failures.length === 0 ? '100%' :
                `${Math.round(((testResults.supabaseInserts - testResults.failures.length) / testResults.supabaseInserts) * 100)}%`;

            const statsMessage = `
📊 TEST STATISTICS:
✅ Supabase Inserts: ${testResults.supabaseInserts}
📖 Supabase Reads: ${testResults.supabaseReads}
💾 Cache Reads: ${testResults.cacheReads}
❌ Failures: ${testResults.failures.length}
📈 Success Rate: ${successRate}

${testResults.failures.length > 0 ?
    `⚠️ FAILURES:\n${testResults.failures.map(f => `• ${f}`).join('\n')}\n\n` :
    '🎉 ALL TESTS PASSED!\n\n'
}

📋 DETAILED RESULTS:
${results.join('\n')}

🧪 COMPREHENSIVE TEST COMPLETED
This test verified:
• RLS policies work correctly
• Data creation in Supabase
• Local caching functionality
• All major data types (people, incidents, encampments, addresses, organizations)
• Both insert and read operations`;

            this.ui.showDialog(
                testResults.failures.length === 0 ? 'All Tests Passed! ✅' : 'Test Results (Some Issues Found) ⚠️',
                statsMessage,
                testResults.failures.length === 0 ? 'success' : 'warning'
            );

            this.ui.setStatus(
                testResults.failures.length === 0 ?
                    'All tests passed - system is working correctly!' :
                    `Test completed with ${testResults.failures.length} failures`,
                testResults.failures.length === 0 ? 'success' : 'warning'
            );

        } catch (error) {
            console.error('Error creating test data:', error);
            this.ui.showDialog('Error', `Failed to create test data: ${error.message}`, 'error');
            this.ui.setStatus('Test data creation failed', 'error');
        }
    }
}

// Remove Test Data Command - Removes all test data created by CreateTestDataCommand
class RemoveTestDataCommand extends BaseCommand {
    async execute(args) {
        try {
            // Check if data manager is available
            if (!this.data) {
                throw new Error('Data manager not available');
            }

            // Confirm deletion
            const confirmed = confirm(
                'Are you sure you want to remove all test data?\n\n' +
                'This will delete:\n' +
                '• All people with "Test person for system testing" in notes\n' +
                '• All addresses with "Test address for system testing" in notes\n' +
                '• All incidents with numbers starting with "TEST-"\n' +
                '• All encampments with "Test encampment for system testing" in description\n' +
                '• All organizations with "Test organization for system testing" in notes\n' +
                '• All items with test data\n\n' +
                'This action cannot be undone!'
            );

            if (!confirmed) {
                this.ui.setStatus('Test data removal cancelled', 'info');
                return;
            }

            this.ui.setStatus('Removing test data...', 'info');

            let results = [];

            // Use direct Supabase deletion (more reliable than app layer)
            const directDeletionResults = await this.removeTestDataDirectly();
            results.push(...directDeletionResults);





            // Display results
            this.ui.showDialog(
                'Test Data Removal Results',
                results.join('\n\n') + '\n\n' +
                'Test data removal completed. All test records have been cleaned up.',
                'success'
            );

            this.ui.setStatus('Test data removal completed', 'success');

        } catch (error) {
            console.error('Error removing test data:', error);
            this.ui.showDialog('Error', `Failed to remove test data: ${error.message}`, 'error');
            this.ui.setStatus('Test data removal failed', 'error');
        }
    }

    async removeTestDataDirectly() {
        const results = [];

        try {
            const supabase = this.data.getSupabaseClient();
            if (!supabase) {
                results.push('⚠️ Supabase client not available, skipping direct deletion');
                return results;
            }

            // Delete test people
            const { error: peopleError } = await supabase
                .from('core.people')
                .delete()
                .like('notes', '%Test person for system testing%');

            if (peopleError) {
                results.push(`⚠️ Direct people deletion failed: ${peopleError.message}`);
            } else {
                results.push('✅ Deleted test people records directly from Supabase');
            }

            // Delete test addresses
            const { error: addressError } = await supabase
                .from('core.addresses')
                .delete()
                .like('notes', '%Test address for system testing%');

            if (addressError) {
                results.push(`⚠️ Direct address deletion failed: ${addressError.message}`);
            } else {
                results.push('✅ Deleted test address records directly from Supabase');
            }

            // Delete test incidents
            const { error: incidentError } = await supabase
                .from('case_mgmt.incidents')
                .delete()
                .like('incident_number', 'TEST-%');

            if (incidentError) {
                results.push(`⚠️ Direct incident deletion failed: ${incidentError.message}`);
            } else {
                results.push('✅ Deleted test incident records directly from Supabase');
            }

            // Delete test organizations
            const { error: orgError } = await supabase
                .from('core.organizations')
                .delete()
                .like('notes', '%Test organization for system testing%');

            if (orgError) {
                results.push(`⚠️ Direct organization deletion failed: ${orgError.message}`);
            } else {
                results.push('✅ Deleted test organization records directly from Supabase');
            }

            // Clear local cache
            if (this.data.cache) {
                this.data.cache.clear();
                results.push('✅ Cleared local memory cache');
            }

            // Clear SQLite cache
            if (this.data.sqlite) {
                const cacheTables = [
                    'cache_people', 'cache_addresses', 'cache_incidents',
                    'cache_organizations', 'cache_items'
                ];
                for (const table of cacheTables) {
                    this.data.sqlite.clear(table);
                }
                results.push('✅ Cleared SQLite cache tables');
            }

        } catch (error) {
            results.push(`❌ Direct deletion error: ${error.message}`);
        }

        return results;
    }
}

// Log Property Command
class LogPropertyCommand extends BaseCommand {
    constructor(manager) {
        super(manager);
        this.name = 'log-property';
        this.description = 'Log found/stolen/discarded property';
    }

    async execute() {
        try {
            // Call the property logging function from the main app
            await this.manager.app.showLogPropertyForm();
        } catch (error) {
            console.error('Error logging property:', error);
            this.ui.showDialog('Error', 'Failed to open property logging form: ' + error.message, 'error');
        }
    }
}

// Property Report Command
class PropertyReportCommand extends BaseCommand {
    constructor(manager) {
        super(manager);
        this.name = 'property-report';
        this.description = 'Generate property management reports';
    }

    async execute() {
        try {
            await this.showPropertyReportDialog();
        } catch (error) {
            console.error('Error generating property report:', error);
            this.ui.showDialog('Error', 'Failed to generate property report: ' + error.message, 'error');
        }
    }

    async showPropertyReportDialog() {
        const now = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const fields = [
            {
                name: 'report_type',
                type: 'select',
                label: 'Report Type',
                options: [
                    { value: 'summary', label: 'Summary Report' },
                    { value: 'overdue', label: 'Overdue Properties' },
                    { value: 'trends', label: 'Trends Analysis' },
                    { value: 'locations', label: 'Location Analytics' }
                ],
                required: true
            },
            {
                name: 'date_from',
                type: 'date',
                label: 'From Date',
                value: thirtyDaysAgo.toISOString().split('T')[0]
            },
            {
                name: 'date_to',
                type: 'date',
                label: 'To Date',
                value: now.toISOString().split('T')[0]
            },
            {
                name: 'status_filter',
                type: 'select',
                label: 'Status Filter (Optional)',
                options: [
                    { value: '', label: 'All Statuses' },
                    { value: 'found', label: 'Found' },
                    { value: 'investigating', label: 'Investigating' },
                    { value: 'returned', label: 'Returned' },
                    { value: 'handed_to_police', label: 'Handed to Police' }
                ]
            }
        ];

        this.ui.showForm('Generate Property Report', fields, async (formData) => {
            try {
                await this.generateReport(formData);
            } catch (error) {
                console.error('Error generating report:', error);
                this.ui.showDialog('Error', 'Failed to generate report: ' + error.message, 'error');
            }
        });
    }

    async generateReport(formData) {
        const { report_type, date_from, date_to, status_filter } = formData;
        const propertyManager = this.manager.app.propertyManager;

        let reportHTML = '';
        let reportTitle = '';

        switch (report_type) {
            case 'summary':
                const filters = status_filter ? { status: status_filter } : {};
                const report = await propertyManager.getPropertyReport(date_from, date_to, filters);
                reportTitle = 'Property Summary Report';
                reportHTML = this.generateSummaryReportHTML(report, date_from, date_to);
                break;

            case 'overdue':
                const overdueProperties = await propertyManager.getOverdueProperties();
                reportTitle = 'Overdue Properties Report';
                reportHTML = this.generateOverdueReportHTML(overdueProperties);
                break;

            case 'trends':
                const trends = await propertyManager.getPropertyTrends(30);
                reportTitle = 'Property Trends Analysis';
                reportHTML = this.generateTrendsReportHTML(trends);
                break;

            case 'locations':
                const locationAnalytics = await propertyManager.getLocationAnalytics();
                reportTitle = 'Location Analytics Report';
                reportHTML = this.generateLocationReportHTML(locationAnalytics);
                break;
        }

        this.ui.showDialog(reportTitle, reportHTML, 'info');
    }

    generateSummaryReportHTML(report, dateFrom, dateTo) {
        return `
            <div class="property-report">
                <div class="report-header">
                    <h3>Property Summary Report</h3>
                    <p>Period: ${dateFrom} to ${dateTo}</p>
                </div>

                <div class="report-section">
                    <h4>Overview</h4>
                    <div class="report-stats">
                        <div class="stat-item">Total Properties: <strong>${report.totalProperties}</strong></div>
                        <div class="stat-item">Average Processing Time: <strong>${report.averageProcessingTime} days</strong></div>
                        <div class="stat-item">Overdue Properties: <strong>${report.overdueProperties.length}</strong></div>
                        <div class="stat-item">Recent Returns: <strong>${report.recentReturns.length}</strong></div>
                    </div>
                </div>

                <div class="report-section">
                    <h4>By Status</h4>
                    <div class="report-breakdown">
                        ${Object.entries(report.byStatus).map(([status, count]) =>
                            `<div class="breakdown-item">${status}: <strong>${count}</strong></div>`
                        ).join('')}
                    </div>
                </div>

                <div class="report-section">
                    <h4>By Property Type</h4>
                    <div class="report-breakdown">
                        ${Object.entries(report.byType).map(([type, count]) =>
                            `<div class="breakdown-item">${type}: <strong>${count}</strong></div>`
                        ).join('')}
                    </div>
                </div>

                <div class="report-section">
                    <h4>Top Locations</h4>
                    <div class="report-breakdown">
                        ${Object.entries(report.topLocations)
                            .sort(([,a], [,b]) => b - a)
                            .slice(0, 5)
                            .map(([location, count]) =>
                                `<div class="breakdown-item">${location}: <strong>${count}</strong></div>`
                            ).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    generateOverdueReportHTML(overdueProperties) {
        return `
            <div class="property-report">
                <div class="report-header">
                    <h3>Overdue Properties Report</h3>
                    <p>Properties found more than 30 days ago and still pending</p>
                </div>

                <div class="report-section">
                    <h4>Overdue Properties (${overdueProperties.length})</h4>
                    <div class="overdue-list">
                        ${overdueProperties.length === 0 ?
                            '<div class="no-data">No overdue properties found!</div>' :
                            overdueProperties.map(property => `
                                <div class="overdue-item">
                                    <div class="property-number">${property.property_number}</div>
                                    <div class="property-details">
                                        <strong>${property.property_type}</strong> - ${property.description}
                                    </div>
                                    <div class="property-date">Found: ${property.found_date}</div>
                                    <div class="property-status status-${property.status}">${property.status}</div>
                                </div>
                            `).join('')
                        }
                    </div>
                </div>
            </div>
        `;
    }

    generateTrendsReportHTML(trends) {
        const sortedDates = Object.keys(trends).sort();

        return `
            <div class="property-report">
                <div class="report-header">
                    <h3>Property Trends Analysis</h3>
                    <p>Daily property activity over the last 30 days</p>
                </div>

                <div class="report-section">
                    <h4>Daily Activity</h4>
                    <div class="trends-list">
                        ${sortedDates.map(date => `
                            <div class="trend-item">
                                <div class="trend-date">${date}</div>
                                <div class="trend-stats">
                                    Found: <strong>${trends[date].found}</strong> |
                                    Returned: <strong>${trends[date].returned}</strong> |
                                    To Police: <strong>${trends[date].handed_to_police}</strong>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    generateLocationReportHTML(locationAnalytics) {
        return `
            <div class="property-report">
                <div class="report-header">
                    <h3>Location Analytics Report</h3>
                    <p>Top 10 locations where property is found</p>
                </div>

                <div class="report-section">
                    <h4>Top Locations</h4>
                    <div class="location-list">
                        ${Object.entries(locationAnalytics).map(([location, stats]) => `
                            <div class="location-item">
                                <div class="location-name"><strong>${location}</strong></div>
                                <div class="location-stats">Total: ${stats.total}</div>
                                <div class="location-breakdown">
                                    Types: ${Object.entries(stats.byType).map(([type, count]) => `${type}(${count})`).join(', ')}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }
}



// Clear Data Command - Clears all local cached data
class ClearDataCommand extends BaseCommand {
    async execute(args) {
        const confirmed = confirm('Are you sure you want to clear all local data? This cannot be undone.');
        if (confirmed) {
            try {
                // Clear SQLite cache if available
                if (this.app.data.sqlite) {
                    const cacheTables = [
                        'cache_people', 'cache_pets', 'cache_incidents',
                        'cache_addresses', 'cache_bikes', 'cache_encampments',
                        'cache_media', 'cache_items', 'cache_organizations'
                    ];
                    for (const table of cacheTables) {
                        this.app.data.sqlite.clear(table);
                    }
                }

                // Clear memory cache
                this.app.data.cache.clear();

                this.ui.showDialog('Success', 'All local data cleared.', 'success');
                this.ui.setStatus('Local data cleared');
            } catch (error) {
                this.ui.showDialog('Error', `Failed to clear data: ${error.message}`, 'error');
                this.ui.setStatus('Error clearing data');
            }
        }
    }

    getHelp() {
        return 'clear-data - Clear all local cached data (SQLite and memory cache)';
    }
}

// CommandManager is already exported at the top of the file
